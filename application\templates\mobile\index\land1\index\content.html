<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>{$data.title} - {:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- Bootstrap -->
        <link href="__RES__/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="__RES__/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Main Css -->
        <link href="__RES__/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="__RES__/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">
        <link href="__RES__/merchant/default/css/icons.min.css" rel="stylesheet" type="text/css">
    </head>

    <body>
        {include file="./default_header"}
        <!-- Hero Start -->
        <section class="bg-half bg-light d-table w-100" style="background: url('__RES__/theme/landrick/xiu.jpg') center center;"> 
            <div class="bg-overlay"></div>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-12 text-center">
                        <div class="page-next-level">
                            <h4 class="title text-white title-dark"> {$data.title} </h4>
                            <ul class="list-unstyled mt-4">
                                <li class="list-inline-item h6 date  text-white"> <span class="text-white">发布日期 :</span> {:date('Y-m-d H:i',$data.create_at)}</li>
                            </ul>
                        </div>
                    </div>  <!--end col-->
                </div><!--end row-->
            </div> <!--end container-->
        </section><!--end section-->
        <!-- Hero End --> 
        <!-- Shape Start -->
        <div class="position-relative">
            <div class="shape overflow-hidden text-white">
                <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
                </svg>
            </div>
        </div>
        <!--Shape End-->

        <!-- Start Privacy -->
        <section class="section">
            <div class="container">

                <div class="row justify-content-center">
                    <div class="col-lg-9">
                        <div class="card shadow rounded border-0">
                            <div class="card-body">
                                {$data.content|htmlspecialchars_decode|removeXSS}
                            </div>
                        </div>
                    </div><!--end col-->
                </div><!--end row-->
            </div><!--end container-->
        </section><!--end section-->
        <!-- End Privacy -->
        {include file="./default_footer"}

        <!-- javascript -->
        <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>
        <script>
            $(function () {
                $('.navigation-menu').addClass("nav-light");
            })
        </script>
    </body>
</html>