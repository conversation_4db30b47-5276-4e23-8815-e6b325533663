{extend name="base"}


{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-12">

                                <form class="form-inline" role="form" action="" method="get">
                                    <div class="form-group">
                                        <select name="paytype" class="form-control select2">
                                            <option value="" {if $Think.get.paytype==''}selected{/if}>全部方式</option>
                                            {foreach $pay_product as $k => $v}
                                            <option value="{$v.id|htmlentities}" {if $Think.get.paytype==$v.id}selected{/if}>{$v.name}</option>
                                            {/foreach}
                                        </select>
                                    </div>

                                    <div class="form-group ml-1">
                                        <select name="cate_id" class="form-control select2">
                                            <option value="" {if $Think.get.cate_id==''}selected{/if}>全部分类</option>
                                            {foreach $categorys as $v}
                                            <option value="{$v.id}" {if $Think.get.cate_id==$v.id}selected{/if}>{$v.name}</option>
                                            {/foreach}
                                        </select>
                                    </div>

                                    <div class="form-group ml-1">
                                        <select name="goods_id" class="form-control select2">
                                            <option value="" {if $Think.get.goods_id==''}selected{/if}>全部商品</option>
                                            {foreach $goods as $v}
                                            <option value="{$v.id}" {if $Think.get.goods_id==$v.id}selected{/if}>{$v.name}</option>
                                            {/foreach}
                                        </select>
                                    </div>

                                    <div class="form-group ml-1">
                                        <input class="form-control"   type="text" name="trade_no" value="{$Think.get.trade_no}" placeholder="请输入订单编号">
                                    </div>
                                    <div class="form-group ml-1">
                                        <input class="form-control input-daterange-datepicker"  type="text" name="date_range" value="{:urldecode($Think.get.date_range)}" placeholder="点击选择查询日期">
                                    </div>

                                    <button type="submit" class="btn btn-light waves-effect waves-light ml-1"><i class="bx bx-search mr-1"></i>搜索</button>
                                </form>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-auto">
                                <span class="badge badge-soft-primary font-size-14 my-2 px-2 py-2">总收入：{$total_price}</span>
                            </div>
                            <div class="col-sm-auto">
                                <span class="badge badge-soft-success font-size-14 my-2  px-2 py-2">总成本：{$total_cost_price}</span>
                            </div>
                            <div class="col-sm-auto">
                                <span class="badge badge-soft-success font-size-14 my-2  px-2 py-2">总利润：{$total_profit}</span>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>商品名称</th>
                                        <th>支付方式</th>
                                        <th>总价</th>
                                        <th>购买者信息</th>
                                        <th>状态</th>
                                        <th>取卡状态</th>
                                        <th>交易时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    {foreach $orders as $v}
                                    <tr>
                                        <th><a href="/orderquery?orderid={$v.trade_no}" target="_blank">{$v.trade_no}</a></th>
                                        <td>
                                            {$v.goods_name}
                                            {eq name="v.status" value="1"}
                                            <a class="fetch" href="{:url('order/fetchcard',['id'=>$v.id])}">（{$v.quantity}张）</a>
                                            {else}
                                            （{$v.quantity}张）
                                            {/eq}
                                            {if $v.coupon_type==1}
                                            <span class="badge badge-pill badge-success font-size-12" data-toggle="tooltip" data-placement="top" title="支持优惠券" data-original-title="支持优惠券">券</span>
                                            {/if}
                                            {if $v.take_card_type==1}
                                            {eq name="v.status" value="1"}
                                            <a href="{:url('order/fetchcard',['id'=>$v.id])}">  <span class="badge badge-pill badge-primary font-size-12"  data-toggle="tooltip" data-placement="top" title="提卡必填或选填取卡密码" data-original-title="提卡必填或选填取卡密码">取</span></a>
                                            {else}
                                            <span class="badge badge-pill badge-primary font-size-12"  data-toggle="tooltip" data-placement="top" title="提卡必填或选填取卡密码" data-original-title="提卡必填或选填取卡密码">取</span>
                                            {/eq}
                                            {/if}
                                        </td>
                                        <td>{:get_paytype_name($v.paytype)}</td>
                                        <td>{$v.total_product_price}</td>
                                        <td>{$v.contact}</td>
                                        <td>
                                            {if $v.status==1}
                                            <span class="badge badge-pill badge-success">{$v.status_text}</span>
                                            {elseif $v.status==0/}
                                            <span class="badge badge-pill badge-danger">{$v.status_text}</span>
                                            {else/}
                                            <span class="badge badge-pill badge-light">{$v.status_text}</span>
                                            {/if}
                                        </td>
                                        <td>
                                            {if $v.cards_count>0}
                                            {if $v.cards_count>=$v.quantity}
                                            已取
                                            {else/}
                                            已取部分
                                            {/if}
                                            {else/}
                                            未取
                                            {/if}
                                        </td>
                                        <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                                        <td>
                                            {if $v.status==1 && $v.cards_count==0}
                                            <a class="fetch" href="{:url('order/fetchcard',['id'=>$v.id])}">提卡</a>
                                            <a href="{:url('order/dumpCards',['trade_no'=>$v.trade_no])}" target="_blank">导出</a>
                                            {/if}
                                        </td>
                                    </tr>
                                    {/foreach}

                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="...">
                            {$page}
                        </nav>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}
<script>


</script>
{/block}
