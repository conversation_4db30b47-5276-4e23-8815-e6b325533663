<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
    <meta name="description" content="{:sysconf('site_desc')}">
    <meta name="keywords" content="{:sysconf('site_keywords')}">
    <link rel="shortcut icon" href="{:sysconf('browser_icon')}">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{:sysconf('site_name')}">
    
    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="__RES__/theme/modern/css/modern.css">
    
    <!-- Mobile-specific styles -->
    <style>
        /* Mobile-first responsive design */
        .mobile-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 60vh;
            position: relative;
            overflow: hidden;
        }
        
        .mobile-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="10" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="15" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="12" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }
        
        .mobile-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin: 1rem;
            overflow: hidden;
        }
        
        .mobile-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 50;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            z-index: 50;
            padding: 0.5rem 0;
        }
        
        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: #6b7280;
            text-decoration: none;
            font-size: 0.75rem;
            transition: color 0.2s;
        }
        
        .bottom-nav-item.active,
        .bottom-nav-item:hover {
            color: #3b82f6;
        }
        
        .floating-action {
            position: fixed;
            bottom: 5rem;
            right: 1rem;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            z-index: 40;
            transition: all 0.3s ease;
        }
        
        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 1rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
        }
        
        .feature-list {
            padding: 0 1rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .news-section {
            margin: 2rem 1rem;
        }
        
        .news-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #3b82f6;
        }
        
        /* Ensure content doesn't hide behind fixed elements */
        body {
            padding-top: 4rem;
            padding-bottom: 4rem;
        }
        
        /* Touch-friendly button sizes */
        .btn {
            min-height: 44px;
            padding: 0.75rem 1.5rem;
        }
        
        /* Improved readability on mobile */
        .mobile-title {
            font-size: 1.75rem;
            line-height: 1.2;
            font-weight: 700;
        }
        
        .mobile-subtitle {
            font-size: 1rem;
            line-height: 1.5;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <div class="flex items-center justify-between px-4 py-3">
            <img src="{:sysconf('site_logo')}" alt="{:sysconf('site_name')}" class="h-8 w-auto">
            <button class="p-2" onclick="toggleMobileMenu()">
                <i data-lucide="menu" class="w-6 h-6"></i>
            </button>
        </div>
    </nav>

    <!-- Mobile Hero Section -->
    <section class="mobile-hero flex items-center">
        <div class="container relative z-10">
            <div class="text-center text-white px-4">
                <h1 class="mobile-title mb-4">快捷下单，自动发卡</h1>
                <p class="mobile-subtitle mb-8">持续更新的虚拟商品寄售平台</p>
                <div class="space-y-3">
                    <a href="/register" class="btn btn-lg bg-white text-primary-600 w-full">
                        立即入驻，成为商户
                    </a>
                    <a href="/orderquery" class="btn btn-lg btn-outline border-white text-white w-full">
                        查询订单
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="text-2xl font-bold text-primary-600 mb-1" data-counter="151825">0</div>
            <div class="text-sm text-gray-600">成功寄售</div>
        </div>
        <div class="stat-card">
            <div class="text-2xl font-bold text-primary-600 mb-1" data-counter="8111">0</div>
            <div class="text-sm text-gray-600">服务商户</div>
        </div>
        <div class="stat-card">
            <div class="text-2xl font-bold text-primary-600 mb-1" data-counter="2">0</div>
            <div class="text-sm text-gray-600">年稳健运营</div>
        </div>
        <div class="stat-card">
            <div class="text-2xl font-bold text-primary-600 mb-1">99.9%</div>
            <div class="text-sm text-gray-600">服务可用性</div>
        </div>
    </div>

    <!-- Features Section -->
    <section class="py-8">
        <div class="text-center mb-6 px-4">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">平台优势</h2>
            <p class="text-gray-600">实体公司运营，资金安全有保障</p>
        </div>
        
        <div class="feature-list">
            <div class="feature-item">
                <div class="feature-icon">
                    <i data-lucide="shield-check" class="w-6 h-6"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">高防服务器</h3>
                    <p class="text-sm text-gray-600">安全稳定，7x24小时运行</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i data-lucide="banknote" class="w-6 h-6"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">次日结账</h3>
                    <p class="text-sm text-gray-600">满100元自动提现</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i data-lucide="headphones" class="w-6 h-6"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">全天候服务</h3>
                    <p class="text-sm text-gray-600">10秒响应，专业客服</p>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i data-lucide="award" class="w-6 h-6"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">证照齐全</h3>
                    <p class="text-sm text-gray-600">正规企业，合规经营</p>
                </div>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <section class="news-section">
        <h2 class="text-xl font-bold text-gray-900 mb-4">最新公告</h2>
        
        <!-- System Announcements -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">系统公告</h3>
            {foreach $announceList as $v}
            <div class="news-card">
                <a href="/article/{$v.id}.html" class="block">
                    <h4 class="font-medium text-gray-900 mb-2">{$v.title}</h4>
                    <p class="text-sm text-gray-600 line-clamp-2">{$v.content|htmlspecialchars_decode|removeXSS}</p>
                </a>
            </div>
            {/foreach}
        </div>
        
        <div class="text-center">
            <a href="/company/faq" class="btn btn-outline">查看更多公告</a>
        </div>
    </section>

    <!-- Floating Action Button -->
    <a href="/login" class="floating-action">
        <i data-lucide="user-plus" class="w-6 h-6"></i>
    </a>

    <!-- Bottom Navigation -->
    <nav class="mobile-bottom-nav">
        <div class="flex justify-around">
            <a href="/" class="bottom-nav-item active">
                <i data-lucide="home" class="w-5 h-5 mb-1"></i>
                <span>首页</span>
            </a>
            <a href="/orderquery" class="bottom-nav-item">
                <i data-lucide="search" class="w-5 h-5 mb-1"></i>
                <span>查询</span>
            </a>
            <a href="/complaint" class="bottom-nav-item">
                <i data-lucide="message-square" class="w-5 h-5 mb-1"></i>
                <span>投诉</span>
            </a>
            <a href="/company/contact" class="bottom-nav-item">
                <i data-lucide="phone" class="w-5 h-5 mb-1"></i>
                <span>联系</span>
            </a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobileMenuOverlay" class="fixed inset-0 bg-black/50 z-40 hidden">
        <div class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300" id="mobileMenuPanel">
            <div class="p-4 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold">菜单</h3>
                    <button onclick="toggleMobileMenu()" class="p-2">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <ul class="space-y-4">
                    <li><a href="/" class="block py-2 text-gray-700">网站首页</a></li>
                    <li><a href="/orderquery" class="block py-2 text-gray-700">卡密查询</a></li>
                    <li><a href="/complaint" class="block py-2 text-gray-700">订单投诉</a></li>
                    <li><a href="/complaintquery" class="block py-2 text-gray-700">投诉进度</a></li>
                    <li><a href="/company/contact" class="block py-2 text-gray-700">联系我们</a></li>
                    <li><a href="/company/faq" class="block py-2 text-gray-700">帮助中心</a></li>
                    <li class="pt-4 border-t">
                        <a href="/login" class="btn btn-primary w-full">商家登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script src="__RES__/theme/modern/js/modern.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Mobile menu functionality
        function toggleMobileMenu() {
            const overlay = document.getElementById('mobileMenuOverlay');
            const panel = document.getElementById('mobileMenuPanel');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    panel.classList.remove('translate-x-full');
                }, 10);
            } else {
                panel.classList.add('translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }
        
        // Close menu when clicking overlay
        document.getElementById('mobileMenuOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });
        
        // Touch gestures for better mobile experience
        let touchStartX = 0;
        let touchEndX = 0;
        
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const swipeThreshold = 100;
            const diff = touchStartX - touchEndX;
            
            // Swipe left to open menu (from right edge)
            if (diff < -swipeThreshold && touchStartX > window.innerWidth - 50) {
                toggleMobileMenu();
            }
            // Swipe right to close menu
            else if (diff > swipeThreshold && !document.getElementById('mobileMenuOverlay').classList.contains('hidden')) {
                toggleMobileMenu();
            }
        }
    </script>
</body>
</html>
