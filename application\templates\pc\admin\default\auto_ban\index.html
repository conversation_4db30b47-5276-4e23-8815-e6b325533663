{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="{:url('save')}" data-auto="true" method="post" class='form-horizontal' style='padding-top:20px'>

    <div class="form-group">
        <label class="col-sm-2 control-label">自动封禁功能</label>
        <div class='col-sm-8'>
            <select name="autoban_status" class="layui-input">
                {if $autoban_status eq 1}
                <option value="1" selected>启用</option>
                <option value="0">禁用</option>
                {else}
                <option value="1">启用</option>
                <option value="0" selected>禁用</option>
                {/if}
            </select>
            <p class="help-block">开启后，将自动封禁长期未登录的用户账号</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">封禁时间</label>
        <div class='col-sm-8'>
            <input type="number" name="autoban_time" required="required" title="请输入自动封禁时间" placeholder="请输入自动封禁时间（天）" value="{$autoban_time|default=0}" class="layui-input">
            <p class="help-block">设置用户允许的最长未登录时间（天），如果用户上次登录时间加上此天数小于当前系统时间，将被自动封禁，0表示不执行自动封禁</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">白名单</label>
        <div class='col-sm-8'>
            <textarea name="autoban_whitelist" class="layui-textarea" placeholder="请输入白名单用户，每行一个用户名，或使用逗号、分号分隔">{$whitelist|default=''}</textarea>
            <p class="help-block">添加到白名单的用户不会被自动封禁，可以通过换行、逗号或分号分隔多个用户名</p>
        </div>
    </div>

    <div class="hr-line-dashed"></div>
    
    <div class="form-group">
        <div class="col-sm-4 col-sm-offset-2">
            <button class="layui-btn" type="submit">保存配置</button>
            <button class="layui-btn layui-btn-danger" data-action="{:url('execute')}" data-value="execute" type="button">立即执行</button>
        </div>
    </div>
</form>

<!-- 添加任务计划配置说明 -->
<div class="panel panel-default" style="margin-top:20px;">
    <div class="panel-heading">
        <h3 class="panel-title">自动化任务计划配置说明</h3>
    </div>
    <div class="panel-body">
        <div class="alert alert-info">
            <p><strong>说明：</strong>自动封禁功能可以通过系统计划任务定时执行，无需手动点击"立即执行"按钮。</p>
        </div>
        
        <h4>配置步骤：</h4>
        <ol>
            <li>服务器需要支持Crontab计划任务</li>
            <li>确保PHP命令行可以正常执行（php -v可以显示版本信息）</li>
            <li>使用以下脚本添加计划任务：</li>
        </ol>
        
        <pre style="background-color:#f5f5f5;padding:10px;border-radius:5px;">#!/bin/bash

# 进入网站目录
cd /www/wwwroot/自己网站域名

# 执行自动封禁命令
php think autoban

echo "自动封禁任务已执行完成"</pre>
        
        <div class="alert alert-warning">
            <p><strong>注意：</strong>请将上述脚本中的网站目录路径替换为您自己的实际路径。</p>
        </div>
        
        <!-- 计划任务设置示例图片 -->
        <div class="text-center" style="margin:15px 0;">
            <img id="crontabImage" src="" alt="计划任务设置示例" class="img-responsive center-block" style="max-width:100%; border:1px solid #ddd; padding:5px; display:none;">
        </div>
        
        <h4>计划任务说明：</h4>
        <ul>
            <li><strong>执行频率：</strong>您可以根据需要设置执行时间，如每天凌晨2点执行一次</li>
            <li><strong>脚本路径：</strong>保存上述脚本为.sh文件，赋予执行权限后添加到计划任务</li>
            <li><strong>任务内容：</strong>执行内容与点击"立即执行"按钮一致，但输出到终端或日志</li>
            <li><strong>白名单功能：</strong>添加到白名单的用户不会被自动封禁，可保护重要账号</li>
            <li><strong>频率控制：</strong>任务默认最小执行间隔为1小时，防止频繁执行</li>
            <li><strong>强制执行：</strong>使用 <code>php think autoban --force</code> 可跳过频率限制强制执行</li>
            <li><strong>锁机制：</strong>任务执行时会创建锁文件，防止重复执行和并发问题</li>
            <li><strong>用户范围：</strong>现在会检查所有用户，包括从未登录的用户（基于注册时间判断）</li>
        </ul>
    </div>
</div>

<script>
    $(function () {
        $('[data-action]').on('click', function () {
            var url = $(this).attr('data-action');
            var value = $(this).attr('data-value');
            var that = this;
            
            // 添加确认对话框
            layer.confirm('确定要立即执行自动封禁操作吗？此操作将根据设置的天数封禁长期未登录的用户。', {
                btn: ['确定执行','取消'] // 按钮
            }, function(){
                // 显示加载中提示
                var loadIndex = layer.msg('正在执行自动封禁，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0 // 不自动关闭
                });
                
                $.ajax({
                    type: 'POST',
                    url: url,
                    data: {value: value},
                    dataType: 'json',
                    success: function (res) {
                        // 关闭加载中提示
                        layer.close(loadIndex);
                        
                        if (res.code) {
                            layer.msg(res.msg, {icon: 1, time: 2000});
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    },
                    error: function() {
                        // 关闭加载中提示
                        layer.close(loadIndex);
                        layer.msg('执行过程中发生错误，请查看系统日志', {icon: 2, time: 2000});
                    }
                });
            });
        });
    });

    layui.use('form', function () {
        var form = layui.form; 
        form.render();
    });
    
    // 如果有计划任务图片上传功能，可以在这里添加处理代码
</script>
{/block} 