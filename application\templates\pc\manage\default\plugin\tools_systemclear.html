{extend name='./content'}


{block name="content"}

<!-- 表单搜索 开始 -->
<form id="del-form" class="form-horizontal" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true"
      method="post">

    {:token()}


    <div class="form-group">
        <label class="col-sm-2 control-label">数据类型</label>
        <div class='col-sm-8'>
            <input type="checkbox" name="clear_type[]" title="" value="1">店铺IP访问记录
            <input type="checkbox" name="clear_type[]" title="" value="2">交易金额统计表（商家柱状图数据）
            <input type="checkbox" name="clear_type[]" title="" value="3">已售卡密
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">要清理多少天之前的数据</label>
        <div class='col-sm-8'>
            <input name="clear_number" id="clear_number" value="45" placeholder="要清理多少天之前的数据" class="layui-input">
            <p class="help-block">至少保留15天，建议保留45天以上</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">短信验证码</label>
        <div class='col-sm-8'>
            <input name="chcode" id="chcode" value="" placeholder="请输入短信验证码" class="layui-input">
            <button type="button" style="margin: 1rem 0" class="btn btn-primary" onclick="sendSms()" id="sendbtn">发送短信
            </button>
        </div>
    </div>

    <div class="hr-line-dashed"></div>
    <div class="layui-form-item text-center">
        <button class="layui-btn" id="del_submit" type="button">删除</button>
        <button class="layui-btn layui-btn-danger" type="button" data-close="">取消</button>
    </div>
</form>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        layer = layui.layer;
        form.render();
    });


    function sendSms() {
        $.post("{:url('manage/order/sendDelBatchSms')}", {}, function (res) {
            layer.msg(res.msg);
        }, 'json')
    }


    $('#del_submit').click(function () {
        var date_range = $('#order_date_range').val();
        if (date_range == '') {
            layer.msg('请先选择日期范围！');
            return false;
        }

        var chcode = $('#chcode').val();
        if (chcode == '') {
            layer.msg('请输入短信验证码！');
            return false;
        }

        layer.confirm('确定删除前已备份数据库，确定要删除数据吗？该操作不可恢复！', function (index) {
            layer.load();
            $.ajax({
                url: "{:url('toolsSystemclear')}",
                type: 'post',
                data: $('#del-form').serialize(),
                success: function (res) {
                    layer.closeAll();
                    if (res.code == 1) {
                        layer.closeAll();
                        layer.alert(res.msg);
                        $.form.reload();
                    } else {
                        layer.msg(res.msg, {icon: 0, time: 1000}, function () {
                            $.form.reload();
                        });
                    }
                },
                error: function () {
                    layer.closeAll();
                }
            });
        });
    });
</script>
{/block}