<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>购买密码验证</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <link href="__RES__/merchant/default/css/bootstrap.min.css" id="bootstrap-style" rel="stylesheet" type="text/css">
        <link href="__RES__/merchant/default/css/components.css" id="bootstrap-style" rel="stylesheet" type="text/css">
        <link href="__RES__/merchant/default/css/bootstrap-extended.css" id="bootstrap-style" rel="stylesheet" type="text/css">
        <link href="__RES__/merchant/default/libs/jquery-confirm/css/jquery-confirm.css" rel="stylesheet" type="text/css">
        <link href="__RES__/merchant/default/libs/select2/css/select2.min.css" rel="stylesheet" type="text/css">
        <!-- Icons Css -->
        <link href="__RES__/merchant/default/css/icons.min.css" rel="stylesheet" type="text/css">
        <!-- App Css-->
        <link href="__RES__/merchant/default/css/app.min.css" id="app-style" rel="stylesheet" type="text/css">
        <link href="__RES__/merchant/default/css/custom.css" id="app-style" rel="stylesheet" type="text/css">
    </head>


    <body data-sidebar="dark" >

        <!-- Begin page -->
        <div id="layout-wrapper">

            <div class="main-content">
                <section id="floating-label-layouts">
                    <div class="row ">

                        <div class="col-12">
                            <div class="card mb-0">
                                <div class="card-content">
                                    <div class="card-body">
                                        <div class="form-body">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label for="password-icon">购买密码</label>
                                                        <div class="position-relative has-icon-left">
                                                            <input type="password" id="password-icon" class="form-control" name="password" placeholder="请输入购买密码">
                                                            <div class="form-control-position">
                                                                <i class="bx bx-lock"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 d-flex justify-content-end">
                                                    <button class="btn btn-light mr-1 mb-1 text-primary btn-sm" onclick="verify_password()">立即验证</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <!-- END layout-wrapper -->


        <!-- Right bar overlay-->
        <div class="rightbar-overlay"></div>

        <!-- JAVASCRIPT -->
        <script src="__RES__/merchant/default/libs/jquery/jquery.min.js"></script>
        <script src="__RES__/merchant/default/libs/node-waves/waves.min.js"></script>
        <script src="__RES__/merchant/default/libs/jquery-confirm/js/jquery-confirm.js"></script>
        <script src="__RES__/merchant/default/libs/layer/layer.js"></script>

        <script>


                                                        function verify_password() {
                                                            var password = $.trim($("input[name=password]").val());
                                                            if (password == "") {
                                                                parent.$.alert("请填写购买密码！");
                                                                $("input[name=password]").focus();
                                                                return false;
                                                            }
                                                            var reg = /^([a-z0-9A-Z]+){6,20}$/;
                                                            if (!reg.test(password)) {
                                                                $.alert("购买密码格式为6-20个长度，数字、大小写字母或组合！");
                                                                $("input[name=password]").focus();
                                                                return false;
                                                            }

                                                            var goods_id = '{$goods_id}';

                                                            $.post("/ajax/checkpwdforbuy", {password: password, goods_id: goods_id, t: new Date().getTime()},
                                                                    function (data) {
                                                                        layer.msg(data.msg);
                                                                        if (data.result == 1) {
                                                                            setInterval(function () {
                                                                                parent.closePwdforbuy()
                                                                            }, 1500);
                                                                        }
                                                                    }
                                                            );
                                                            return false;
                                                        }
        </script>
    </body>

</html>