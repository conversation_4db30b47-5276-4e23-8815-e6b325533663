<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>用户登录 - {:sysconf('site_name')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <!-- Bootstrap -->
        <link href="__RES__/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="__RES__/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Main Css -->
        <link href="__RES__/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="__RES__/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">

    </head>

    <body>
        <!-- Loader -->
        <div id="preloader">
            <div id="status">
                <div class="spinner">
                    <div class="double-bounce1"></div>
                    <div class="double-bounce2"></div>
                </div>
            </div>
        </div> 
        <!-- Loader -->

        <div class="back-to-home rounded d-none d-sm-block">
            <a href="index.html" class="btn btn-icon btn-soft-primary"><i data-feather="home" class="icons"></i></a>
        </div>

        <!-- Hero Start -->
        <section class="bg-home d-flex align-items-center">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-7 col-md-6">
                        <div class="mr-lg-5">   
                            <img src="__RES__/theme/landrick/images/user/login.svg" class="img-fluid d-block mx-auto" alt="">
                        </div>
                    </div>
                    <div class="col-lg-5 col-md-6">
                        <div class="card login-page bg-white shadow rounded border-0">
                            <div class="card-body">
                                <h4 class="card-title text-center">商户登录</h4>  
                                <form class="login-form mt-4" role="form" method="post" action="/login/userlogin">
                                    <input type="hidden" name="__token__" value="{$Request.token|htmlentities}" />
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group position-relative">
                                                <label>用户名</label>
                                                <i data-feather="user" class="fea icon-sm icons"></i>
                                                <input class="form-control pl-5" placeholder="请输入用户名" type="text" name="username" id="username" value="" required="">
                                            </div>
                                        </div>

                                        <div class="col-lg-12">
                                            <div class="form-group position-relative">
                                                <label>密码</label>
                                                <i data-feather="key" class="fea icon-sm icons"></i>
                                                <input type="password" name="password" id="password" class="form-control pl-5" value="" placeholder="请输入密码" required="">
                                            </div>
                                        </div>

                                        <div class="col-lg-12">
                                            <div class="d-flex justify-content-between">
                                                <div class="form-group">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" name="rememberme" value="1" class="custom-control-input" id="customCheck1">
                                                        <label class="custom-control-label" for="customCheck1">记住密码</label>
                                                    </div>
                                                </div>
                                                <p class="forgot-pass mb-0"><a href="/login/retpwd" class="text-dark font-weight-bold">忘记密码 ?</a></p>
                                            </div>
                                        </div>
                                        <div class="col-lg-12 mb-0">
                                            <button  type="button" id="login_btn" class="btn btn-light mt-2 mr-2 w-100" >登 录</button>
                                        </div>

                                        <div class="col-12 text-center">
                                            <div class="d-flex justify-content-between  mt-3 align-items-center">
                                                <div>
                                                    {if plugconf("oauth2", "qq_open_merchant") == 1}
                                                    <a href="{:url('user/qqlogin')}" class="mr-2">
                                                        <img src="__STATIC__/theme/landrick/images/qq.svg" width="30" height="30">
                                                    </a>
                                                    {/if}
                                                    {if plugconf("oauth2", "wechat_open_merchant") == 1}
                                                    <a href="{:url('user/wechatlogin')}" class="mr-2">
                                                        <img src="__STATIC__/theme/landrick/images/wx.svg" width="30" height="30">
                                                    </a>
                                                    {/if}
                                                </div>
                                                <p class="mb-0"><small class="text-dark mr-2">没有账号 ?</small> <a href="/register" class="text-dark font-weight-bold">立即注册</a></p>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div><!---->
                    </div> <!--end col-->
                </div><!--end row-->
            </div> <!--end container-->
        </section><!--end section-->
        <!-- Hero End -->

        <!-- javascript -->
        <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>

        <script src="/static/app/js/layer.js"></script>
        <script>
            $('#login_btn').click(function () {
                if ($('#username').val() == '') {
                    layer.msg('请输入用户名');
                    return false;
                }
                if ($('#password').val() == '') {
                    layer.msg('请输入密码');
                    return false;
                }
                var loading = '';
                $.ajax({
                    type: 'post',
                    url: '/index/user/doLogin',
                    dataType: "json",
                    data: $("form").serialize(),
                    beforeSend: function (xhr) {
                        loading = layer.load()
                    },
                    success: function (res) {
                        layer.close(loading);
                        if (res.code == 1) {
                            layer.msg('恭喜您，登录成功！', {icon: 6, time: 1000});
                            window.location.href = '/merchant';
                        } else {
                            layer.msg(res.msg, {icon: 6, time: 1000});
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        layer.close(loading);
                        layer.msg('请刷新页面重试');
                    }
                });
            })
            $(document).keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#login_btn").trigger("click");
                }
            })
        </script>

    </body>
</html>