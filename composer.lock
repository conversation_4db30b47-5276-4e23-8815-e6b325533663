{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "fde5ec4b219df9bdf0743ff5b923388d", "packages": [{"name": "5ini99/think-addons", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/5ini99/think-addons.git", "reference": "65d56d90005ad2e567494f138fc44c46e92f3c81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/5ini99/think-addons/zipball/65d56d90005ad2e567494f138fc44c46e92f3c81", "reference": "65d56d90005ad2e567494f138fc44c46e92f3c81", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "topthink/think-helper": ">=1.0.4", "topthink/think-installer": ">=1.0.10"}, "type": "library", "extra": {"think-config": {"addons": "src/config.php"}}, "autoload": {"psr-4": {"think\\": "src/"}, "files": ["src/common.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "xiaobo.sun", "email": "<EMAIL>"}], "description": "addons package for thinkphp5", "homepage": "https://github.com/5ini99/think-addons", "time": "2017-03-28T12:41:19+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/e69f57916678458642ac9d2fd341ae78a56996c8", "reference": "e69f57916678458642ac9d2fd341ae78a56996c8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "~1.0"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "time": "2018-01-08T06:59:35+00:00"}, {"name": "endroid/qrcode", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "c9644bec2a9cc9318e98d1437de3c628dcd1ef93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/c9644bec2a9cc9318e98d1437de3c628dcd1ef93", "reference": "c9644bec2a9cc9318e98d1437de3c628dcd1ef93", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "php": ">=5.4", "symfony/options-resolver": "^2.3|^3.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0", "sensio/framework-extra-bundle": "^3.0", "symfony/browser-kit": "^2.3|^3.0", "symfony/framework-bundle": "^2.3|^3.0", "symfony/http-kernel": "^2.3|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://endroid.nl/"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/QrCode", "keywords": ["bundle", "code", "endroid", "qr", "qrcode", "symfony"], "abandoned": "endroid/qr-code", "time": "2017-04-08T09:13:59+00:00"}, {"name": "jere<PERSON><PERSON><PERSON>l/php-domain-parser", "version": "5.6.0", "source": {"type": "git", "url": "https://github.com/jeremykendall/php-domain-parser.git", "reference": "fc210036cf739f679a125145b1a23c202d5cc485"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jeremy<PERSON><PERSON>l/php-domain-parser/zipball/fc210036cf739f679a125145b1a23c202d5cc485", "reference": "fc210036cf739f679a125145b1a23c202d5cc485", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-intl": "*", "php": ">=7.0", "psr/log": "^1.1", "psr/simple-cache": "^1.0.1"}, "require-dev": {"composer/composer": "^1.6", "friendsofphp/php-cs-fixer": "^2.7", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.3"}, "suggest": {"ext-curl": "To use the package http client", "league/uri-parser": "To parse URL and validate host", "psr/simple-cache-implementation": "To enable using other cache providers"}, "bin": ["bin/update-psl"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"Pdp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://about.me/jere<PERSON><PERSON><PERSON>l", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://nyamsprod.com", "role": "Developer"}, {"name": "Contributors", "homepage": "https://github.com/jeremy<PERSON><PERSON>l/php-domain-parser/graphs/contributors"}], "description": "Public Suffix List based URL parsing implemented in PHP.", "homepage": "https://github.com/jeremykendall/php-domain-parser", "keywords": ["PSL", "Public Suffix List", "domain parsing", "icann", "idn"], "time": "2019-12-29T06:43:19+00:00"}, {"name": "libern/qr-code-reader", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/libern/qr-code-reader.git", "reference": "f5443ef16ca145ceaf21df7d28efee3771989ee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/libern/qr-code-reader/zipball/f5443ef16ca145ceaf21df7d28efee3771989ee5", "reference": "f5443ef16ca145ceaf21df7d28efee3771989ee5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Libern\\QRCodeReader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Libern Lin", "email": "<EMAIL>"}], "description": "Simple PHP QR Code Reader / Decoder", "homepage": "https://github.com/libern/qr-code-reader", "keywords": ["laravel", "qr code decoder", "qr code reader"], "time": "2017-06-02T12:56:55+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "67852ba9cdd7f48e0e080961abebafee134fb329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/67852ba9cdd7f48e0e080961abebafee134fb329", "reference": "67852ba9cdd7f48e0e080961abebafee134fb329", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "time": "2018-02-20T13:59:54+00:00"}, {"name": "robbiep/zbar-qrdecoder", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/robbiepaul/zbar-qrdecoder.git", "reference": "ca652762b28745e8e470ee43f006a2691403c4e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/robbiepaul/zbar-qrdecoder/zipball/ca652762b28745e8e470ee43f006a2691403c4e8", "reference": "ca652762b28745e8e470ee43f006a2691403c4e8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0", "symfony/process": "^3.0"}, "require-dev": {"mockery/mockery": "0.9.*@dev", "phpunit/phpunit": "4.3.5"}, "type": "library", "autoload": {"psr-0": {"RobbieP\\ZbarQrdecoder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP wrapper for Zbar. Decodes images/photos containing QR codes.", "keywords": ["decode", "laravel", "php", "qr", "qrcode", "zbar"], "time": "2016-03-13T16:05:08+00:00"}, {"name": "symfony/options-resolver", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "f3109a6aedd20e35c3a33190e932c2b063b7b50e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/f3109a6aedd20e35c3a33190e932c2b063b7b50e", "reference": "f3109a6aedd20e35c3a33190e932c2b063b7b50e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2018-01-11T07:56:07+00:00"}, {"name": "symfony/process", "version": "v3.4.8", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "4b7d64e852886319e93ddfdecff0d744ab87658b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/4b7d64e852886319e93ddfdecff0d744ab87658b", "reference": "4b7d64e852886319e93ddfdecff0d744ab87658b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2018-04-03T05:22:50+00:00"}, {"name": "topthink/think-captcha", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "0c55455df26a1626a60d0dc35d2d89002b741d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/0c55455df26a1626a60d0dc35d2d89002b741d44", "reference": "0c55455df26a1626a60d0dc35d2d89002b741d44", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "time": "2016-07-06T01:47:11+00:00"}, {"name": "topthink/think-helper", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "0c99dc625b0d2d4124e1b6ca15a3ad6f0125963f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/0c99dc625b0d2d4124e1b6ca15a3ad6f0125963f", "reference": "0c99dc625b0d2d4124e1b6ca15a3ad6f0125963f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "autoload": {"psr-4": {"think\\helper\\": "src"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Helper Package", "time": "2017-04-05T07:15:37+00:00"}, {"name": "topthink/think-installer", "version": "v1.0.12", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "1be326e68f63de4e95977ed50f46ae75f017556d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/1be326e68f63de4e95977ed50f46ae75f017556d", "reference": "1be326e68f63de4e95977ed50f46ae75f017556d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-plugin-api": "^1.0"}, "require-dev": {"composer/composer": "1.0.*@dev"}, "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2017-05-27T06:58:09+00:00"}, {"name": "topthink/think-mongo", "version": "v1.8.4", "source": {"type": "git", "url": "https://github.com/top-think/think-mongo.git", "reference": "4ea4eaf8d7dc272122e124c6e795b3b5badb30ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-mongo/zipball/4ea4eaf8d7dc272122e124c6e795b3b5badb30ab", "reference": "4ea4eaf8d7dc272122e124c6e795b3b5badb30ab", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "autoload": {"psr-4": {"think\\mongo\\": "src"}, "files": []}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "mongodb driver for thinkphp5", "time": "2018-04-01T11:14:58+00:00"}, {"name": "topthink/think-queue", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "ad709611d516e13d6760234bc98e91faa901cae8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/ad709611d516e13d6760234bc98e91faa901cae8", "reference": "ad709611d516e13d6760234bc98e91faa901cae8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/think-helper": ">=1.0.4", "topthink/think-installer": ">=1.0.10"}, "type": "think-extend", "extra": {"think-config": {"queue": "src/config.php"}}, "autoload": {"psr-4": {"think\\": "src"}, "files": ["src/common.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Queue Package", "time": "2017-06-25T00:49:56+00:00"}, {"name": "zoujingli/ip2region", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/zoujingli/ip2region.git", "reference": "5d981fbf3b574bad7fe9652e7aecba0920f54325"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/ip2region/zipball/5d981fbf3b574bad7fe9652e7aecba0920f54325", "reference": "5d981fbf3b574bad7fe9652e7aecba0920f54325", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["Ip2Region.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache 2.0"], "description": "Ip2Region", "homepage": "https://github.com/zoujingli/Ip2Region", "keywords": ["Ip2Region"], "time": "2017-11-09T03:36:17+00:00"}, {"name": "zoujingli/wechat-php-sdk", "version": "v1.3.18", "source": {"type": "git", "url": "https://github.com/zoujingli/wechat-php-sdk.git", "reference": "d37d0c1919ede2ee54e65100ac3792e947b1e0ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/wechat-php-sdk/zipball/d37d0c1919ede2ee54e65100ac3792e947b1e0ef", "reference": "d37d0c1919ede2ee54e65100ac3792e947b1e0ef", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-libxml": "*", "ext-simplexml": "*", "php": ">=5.3.3"}, "type": "project", "autoload": {"psr-4": {"Wechat\\": "./Wechat"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WeChat development of SDK", "homepage": "http://www.kancloud.cn/zoujingli/wechat-php-sdk", "keywords": ["wechat-php-sdk"], "time": "2019-10-10T09:42:15+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}