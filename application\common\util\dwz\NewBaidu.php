<?php

namespace app\common\util\dwz;

use app\common\util\DWZ;

class NewBaidu extends DWZ {

    const API_URL = 'https://dwz.cn/api/v3/short-urls';

    protected $key;

    public function __construct() {
        $this->key = sysconf('short_newbaidu_token');
    }

    public function create($url) {
        $res = $this->http_post_json(SELF::API_URL, [[
        'LongUrl' => $url,
        'TermOfValidity' => 'long-term',
            ]], $this->key);

        $json = json_decode($res, true);
        if (!$json || $json['Code'] != 0) {
            return false;
        }
        if (isset($json['ShortUrls']) && isset($json['ShortUrls'][0]) && isset($json['ShortUrls'][0]['ShortUrl'])) {
            return $json['ShortUrls'][0]['ShortUrl'];
        } else {
            file_put_contents("baidu.txt", json_encode($json));
            record_system_log("新百度短链接生成失败：" . json_encode($json));
            return false;
        }
    }

    function http_post_json($url, $jsonStr, $token) {
        $jsonStr = json_encode($jsonStr);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  // 跳过检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 跳过检查
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($jsonStr),
            'Dwz-Token: ' . $token,
                )
        );
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }

}
