<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">上级菜单</label>
        <div class="layui-input-block">
            <select name='pid' class='layui-select full-width' style='display:block'>
                <!--{foreach $menus as $menu}-->
                <!--{eq name='menu.id' value='$vo.pid|default=0'}-->
                <option selected value='{$menu.id}'>{$menu.spl}{$menu.title}</option>
                <!--{else}-->
                <option value='{$menu.id}'>{$menu.spl}{$menu.title}</option>
                <!--{/eq}-->
                <!--{/foreach}-->
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">菜单名称</label>
        <div class="layui-input-block">
            <input type="text" name="title" value='{$vo.title|default=""}' required="required" title="请输入菜单名称" placeholder="请输入菜单名称" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">菜单链接</label>
        <div class="layui-input-block">
            <input type="text" onblur="(this.value === '') && (this.value = '#')" name="url" autocomplete="off" required="required" title="请输入菜单链接" placeholder="请输入菜单链接" value="{$vo.url|default='#'}" class="layui-input typeahead">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">菜单图标</label>
        <div class="layui-input-inline" style='width:300px'>
            <input placeholder="请输入或选择图标" onchange="$('#icon-preview').get(0).className = this.value" type="text" name="icon" value='{$vo.icon|default=""}' class="layui-input">
        </div>
        <span class='layui-btn layui-btn-primary' style='padding:0 12px;min-width:45.2px'>
            <i id='icon-preview' style='font-size:1.2em' class='{$vo.icon|default=""}'></i>
        </span>
        <button data-icon='icon' type='button' class='layui-btn layui-btn-primary'>选择图标</button>
    </div>

    <div class="hr-line-dashed"></div>

    <div class="layui-form-item text-center">
        {if isset($vo['id'])}<input type='hidden' value='{$vo.id}' name='id'/>{/if}
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>

    <script>
        require(['bootstrap.typeahead'], function () {
            var subjects = JSON.parse('{$nodes|json_encode}');
            $('.typeahead').typeahead({source: subjects, items: 5});
        });
    </script>

</form>
