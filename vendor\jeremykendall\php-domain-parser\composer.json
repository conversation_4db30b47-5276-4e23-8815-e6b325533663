{"name": "jere<PERSON><PERSON><PERSON>l/php-domain-parser", "description": "Public Suffix List based URL parsing implemented in PHP.", "homepage": "https://github.com/jeremykendall/php-domain-parser", "support": {"issues": "https://github.com/jeremy<PERSON>dall/php-domain-parser/issues", "source": "https://github.com/jeremykendall/php-domain-parser"}, "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "http://about.me/jere<PERSON><PERSON><PERSON>l", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://nyamsprod.com", "role": "Developer"}, {"name": "Contributors", "homepage": "https://github.com/jeremy<PERSON><PERSON>l/php-domain-parser/graphs/contributors"}], "bin": ["bin/update-psl"], "keywords": ["Public Suffix List", "domain parsing", "icann", "idn", "psl"], "require": {"php": ">=7.0", "ext-intl": "*", "psr/log": "^1.1", "psr/simple-cache": "^1.0.1"}, "require-dev": {"composer/composer": "^1.6", "friendsofphp/php-cs-fixer": "^2.7", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^0.9.2", "phpstan/phpstan-phpunit": "^0.9.4", "phpstan/phpstan-strict-rules": "^0.9.0", "phpunit/phpunit": "^6.3"}, "suggest": {"psr/simple-cache-implementation": "To enable using other cache providers", "ext-curl": "To use the package http client", "league/uri-parser": "To parse URL and validate host"}, "autoload": {"psr-4": {"Pdp\\": "src/"}}, "autoload-dev": {"psr-4": {"Pdp\\Tests\\": "tests/"}}, "scripts": {"phpcs": "php-cs-fixer fix -vvv --diff --dry-run --allow-risky=yes --ansi", "phpstan-src": "phpstan analyse -l max -c phpstan.src.neon src --ansi", "phpstan-tests": "phpstan analyse -l max -c phpstan.tests.neon tests --ansi", "phpstan": ["@phpstan-src", "@phpstan-tests"], "phpunit": "phpunit --coverage-text", "post-install-cmd": "\\Pdp\\Installer::updateLocalCache", "post-update-cmd": "\\Pdp\\Installer::updateLocalCache", "test": ["@phpcs", "@phpstan", "@phpunit"]}, "scripts-descriptions": {"phpcs": "Runs coding style test suite", "phpstan": "Runs complete codebase static analysis", "phpstan-src": "Runs PHP stan on the source code", "phpstan-test": "Runs PHP stan on the test suite", "phpunit": "Runs unit and functional testing", "test": "Runs the complete test suite"}, "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "config": {"sort-packages": true}}