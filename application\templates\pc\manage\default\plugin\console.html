{extend name='./content'}

{block name="content"}
<!-- 表单搜索 开始 -->

<div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
    <p style="font-size:14px;"> 用于记录系统执行某些操作失败的日志（例如邮箱，短信，短链接调用失败）    <button type="button" id="heand_del_batch" class="layui-btn layui-btn-normal layui-btn-small">清空日志</button>
</p>
</div>

<table class="layui-table" lay-skin="line" lay-size="sm">
    <thead>
        <tr>
            <th class='text-center'>ID</th>
            <th class='text-center'>内容</th>
            <th class='text-center'>创建时间</th>
        </tr>
    </thead>
    <tbody>
        {foreach $list as $v}
        <tr>
            <td class='text-center'>{$v.id}</td>
            <td class='text-center'>{$v.content}</td>
            <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>
        </tr>
        {/foreach}
    </tbody>
</table>
{$page}

<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //日期范围
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });

    $('#heand_del_batch').click(function () {
        layer.confirm('确定要清空记录吗？', function (index) {
        
            $.ajax({
                url: "/manage/plugin/consoleClear",
                type: 'post',
                data: {
                },
                success: function (res) {
                    if (res.code == 1) {
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                        layer.msg('操作成功!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('操作失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    });

</script>
{/block}
