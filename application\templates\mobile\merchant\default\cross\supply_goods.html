{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->

            <div class="search-form-wrapper">


                <form class="mb-3 pb-4 mt-2" action="" method="get">

                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm">商品分类</span>
                        <select name="cate_id" class="form-select  form-select-sm">
                            <option value="" {if $Think.get.cate_id==''}selected{/if}>全部分类</option>
                            {foreach $categorys as $v}
                            <option value="{$v.id|htmlentities}" {if $Think.get.cate_id==$v.id}selected{/if}>{$v.name}</option>
                            {/foreach}
                        </select>
                    </div>


                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm" id="basic-addon1">商品名称</span>
                        <input type="text" class="form-control form-control-sm" name="name" value="" maxlength="30" placeholder="商品名称">
                    </div>
                    <button class="btn btn-primary w-100 d-flex align-items-center justify-content-center" type="submit">
                        <i class="bx bx-search me-2"></i>查询
                    </button>
                </form>
            </div>


            {foreach $goodsList as $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="mb-0 float-left d-inline">商品名称：{$v.name}</h6>
                    {if $v.status==1}
                    <span onclick="change_status('{$v.id}', 0)" class="badge rounded-pill bg-success float-end">上架中</span>
                    {else/}
                    <span onclick="change_status('{$v.id}', 1)" class="badge rounded-pill bg-danger float-end">已下架</span>
                    {/if}
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>商品分类：</span>
                        <span>{$v.category.name|default="未分类"}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>来源：</span>
                        <span>
                            {switch name="$v.cross.platform"}
                            {case value="kaduoduo"}<span class="badge badge-pill bg-light text-primary font-size-12">卡多多</span>{/case}
                            {case value="kaduoduoapi"}<span class="badge badge-pill bg-light text-primary font-size-12">卡多多API</span>{/case}
                            {case value="taoka"}<span class="badge badge-pill bg-light text-primary font-size-12">淘卡</span>{/case}
                            {case value="taokaapi"}<span class="badge badge-pill bg-light text-primary font-size-12">淘卡API</span>{/case}

                            {case value="xinkashou"}<span class="badge badge-pill bg-light text-primary font-size-12">新卡售</span>{/case}
                            {case value="xingoukaapi"}<span class="badge badge-pill bg-light text-primary font-size-12">新购卡API</span>{/case}
                            {case value="xingouka"}<span class="badge badge-pill bg-light text-primary font-size-12">新购卡</span>{/case}
                            {case value="xinkagou"}<span class="badge badge-pill bg-light text-primary font-size-12">新卡购</span>{/case}
                            {case value="kayixiao"}<span class="badge badge-pill bg-light text-primary font-size-12">卡易销</span>{/case}
                            {case value="kayixin"}<span class="badge badge-pill bg-light text-primary font-size-12">卡易信</span>{/case}
                            {case value="ukashou"}<span class="badge badge-pill bg-light text-primary font-size-12">U卡售</span>{/case}
                            {/switch}
                            {$v.cross.domain}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>售价：</span>
                        <span>
                            {$v.price}元
                        </span>
                    </div>
                    <div class="order-wrap-text">
                        <span>创建时间：</span>
                        <span>
                            {$v.create_at|date="Y-m-d H:i:s",###}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>开启代理：</span>
                        <span>
                            {if $v.can_proxy == 1}
                            <span class="badge badge-pill bg-light text-success font-size-12">是</span>
                            {else}
                            <span class="badge badge-pill bg-light text-danger font-size-12">否</span>
                            {/if}
                        </span>
                    </div>
                </div>

                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">
                    <button  onclick="$.x_show('商品链接', '{:url(\'cross/link\',[\'id\'=>$v.id])}', '90%')"  class="btn btn-light waves-effect waves-light text-primary  btn-sm me-1">链接</button>
                    <a href="{:url('cross/edit',['id'=>$v.id])}" class="btn btn-light waves-effect waves-light text-primary btn-sm me-1">编辑</a>
                    <button onclick="del(this, '{$v.id}')" class="btn btn-light waves-effect waves-light text-danger  btn-sm me-1">删除</button>
                </div>
            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}

<script>
    $('#select_all').click(function () {
        if ($(this).is(':checked')) {
            $('tbody').find('.select-checkbox').each(function () {
                $(this).prop("checked", true)
            })
        } else {
            $('tbody').find('.select-checkbox').each(function () {
                $(this).prop("checked", false)
            })
        }
    });


    function change_status(id, status) {
        $.post("{:url('cross/changeStatus')}", {
            id: id,
            status: status
        }, function (res) {
            location.reload();
        });
    }


    function del(obj, id)
    {
        $.confirm({
            title: '确定删除吗？',
            content: '确定要删除此跨平台商品吗！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('cross/del')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("删除成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }



    function batch_del() {
        var ids = new Array();
        $('tbody').find('input[name="good_id"]').each(function () {
            if ($(this).is(':checked')) {
                ids.push($(this).val());
            }
        })
        if (ids.length == 0) {
            $.alert("选择要删除的数据");
            return false;
        }
        $.confirm({
            title: '提示！',
            content: '确定要批量删除跨平台商品吗？',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('cross/batch_del')}", {
                            ids: ids
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert("删除失败");
                            } else {
                                $.alert("删除成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }

</script>
{/block}