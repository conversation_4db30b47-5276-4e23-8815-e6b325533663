{extend name="./category_layout"}

{block name="content"}
<li>
    <span class="span_up">商品分类</span>
    <p class="big_txt" title="{$category.name}" style="padding: 5px 10px;color: #666;">{$category.name}</p>
    <script>
        $(function () {
            selectcateid();
        })
    </script>
    <input type="hidden" name="cateid" id="cateid" value="{$category.id}">
</li>
<li><span class="span_up">商品名称</span>
    <select name="goodid" id="goodid" onchange="selectgoodid()">
        <option value="">请选择商品</option>
    </select>
    <a class="spsm" onclick="layer_remark()">[ 卖家公告 ]</a>
    <span id="notice" style="display: none">{$shop.shop_notice}</span>
    <script>
        function layer_remark() {
            var gonggao = $('#notice').text();
            layer.open({
                time: 5000,
                content: gonggao,
                shadeClose: true
            });
        }
    </script>
</li>
<li>
    {/block}