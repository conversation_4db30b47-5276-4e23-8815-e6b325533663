{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >
    <div class="form-group">
        <label class="col-sm-2 control-label">下单收集买家IP信息</label>
        <div class='col-sm-8'>
            <select name="collect_ip" class="layui-input" disabled="disabled" style="background: #e9e9e9" >
                <option value="0">关闭</option>
                <option value="1" selected="">开启</option>
            </select>
            <p class="help-block">【买家不唯一 | 易绕过】</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">下单收集买家指纹信息</label>
        <div class='col-sm-8'>
            <select name="collect_fingerprint" class="layui-input" disabled="disabled" style="background: #e9e9e9"  >
                <option value="0">关闭</option>
                <option value="1" selected="">开启</option>
            </select>
            <p class="help-block">【买家唯一 | 易绕过】买家更换浏览器会指纹会变化</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">下单收集买家微信信息</label>
        <div class='col-sm-8'>
            <select name="collect_openid" class="layui-input" >
                <option value="0" {if plugconf('buyerblack','collect_openid')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('buyerblack','collect_openid')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">【买家唯一 | 极难绕过】如果对接口投诉要求高，建议开启。需要配置后台微信接口</p>
        </div>
    </div>

    <div class="hr-line-dashed"></div>

    <div class="form-group">
        <label class="col-sm-2 control-label">屏蔽IP黑名单内买家</label>
        <div class='col-sm-8'>
            <select name="ip_status" class="layui-input" >
                <option value="0" {if plugconf('buyerblack','ip_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('buyerblack','ip_status')=='1'}selected{/if}>开启</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">屏蔽微信黑名单内买家</label>
        <div class='col-sm-8'>
            <select name="openid_status" class="layui-input" >
                <option value="0" {if plugconf('buyerblack','openid_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('buyerblack','openid_status')=='1'}selected{/if}>开启</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">屏蔽指纹黑名单内买家</label>
        <div class='col-sm-8'>
            <select name="fingerprint_status" class="layui-input" >
                <option value="0" {if plugconf('buyerblack','fingerprint_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('buyerblack','fingerprint_status')=='1'}selected{/if}>开启</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">触发黑名单提示的文字</label>
        <div class="col-sm-8">
            <textarea name="black_tip" id="" cols="30" rows="5" class="layui-textarea">{:plugconf('buyerblack','black_tip')}</textarea>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>


<div class="row">
    <div class="col-sm-12   text-center">
        <div class="hr-line-dashed"></div>
        <a class="layui-btn" data-title="查看黑名单" data-open="{:url('buyerBlackList')}" href="javascript:void(0)">查看黑名单</a>
    </div>
</div>


<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });


    $('[name="collect_openid"]').change(function () {
        if ($(this).val() == '0') {
            $('.auth_type1').slideUp();
        } else if ($(this).val() == '1') {
            $('.auth_type1').slideDown();

        }
    });

</script>
{/block}