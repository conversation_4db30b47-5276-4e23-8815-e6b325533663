{extend name="simple_base"}


{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body  p-3">

            {foreach $goodslist as $v}
            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">{$v.name}</h6>
                    {switch name="v.status"}
                    {case value="0"}<span class="badge rounded-pill bg-danger float-end">下架</span>{/case}
                    {case value="1"}<span class="badge rounded-pill bg-success float-end">上架中</span>{/case}
                    {/switch}
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>原商品名：</span>
                        <span>{$v.pgoods.name}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>原分类名：</span>
                        <span>{$v.pgoods.category.name|default='未设置'}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>代理成本价：</span>
                        <span>
                            {switch name="v.proxy_price_diy"}
                            {case value="0"}<span class="ml-1 badge badge-pill bg-success">正常代理价</span>{/case}
                            {case value="1"}<span class="ml-1 badge badge-pill bg-primary">自定义价</span>{/case}
                            {/switch}
                            {$v.cost_price}
                            {if $v.proxy_price_diy==1}
                            【原￥{$v.pgoods.proxy_price}】
                            {/if}
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>销售价：</span>
                        <span>{$v.price}</span>
                    </div>
                </div>

                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">
                    <a onclick="$.x_show('自定义成本价', '{:url(\'agent/proxyPrice\',[\'id\'=>$v.id])}', '90%')"  href="javascript:void(0);" ><span class="goods-warp-btn" >自定义成本价</span></a>
                </div>
            </div>
            {/foreach}       
            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}
<script>

    function verify(obj, id, status, msg)
    {
        $.confirm({
            title: '温馨提示',
            content: msg,
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('Agent/verifyProxy')}", {
                            id: id,
                            status: status
                        }, function (res) {
                            layer.close(loading);
                            if (res.code == 1) {
                                $.alert(res.msg);
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            } else {
                                $.alert(res.msg);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }
</script>
{/block}

