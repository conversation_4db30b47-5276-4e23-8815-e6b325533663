{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >
    <div class="form-group">
        <label class="col-sm-2 control-label">功能是否开启</label>
        <div class='col-sm-8'>
            <select name="status" class="layui-input" >
                <option value="0" {if plugconf('lottery','status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('lottery','status')=='1'}selected{/if}>开启</option>
            </select>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label">日流水可获得一次抽奖机会（单位元）</label>
        <div class="col-sm-8">
            <input type="number"  name="stream"  autocomplete="off" class="layui-input"  value="{:plugconf('lottery','stream')}">
            <p class="help-block">叠加获得，例如日流水500元一次，2000元可获得4次</p>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>


<div class="row">
    <div class="col-sm-12   text-center">
        <div class="hr-line-dashed"></div>
        <a class="layui-btn" data-title="认证订单" data-open="{:url('lotteryGift')}" href="javascript:void(0)">奖品设置</a>
        <a class="layui-btn" data-title="获奖订单" data-open="{:url('lotteryOrder')}" href="javascript:void(0)">获奖订单</a>
    </div>
</div>


<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });

</script>
{/block}