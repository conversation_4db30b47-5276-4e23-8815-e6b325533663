<?php

/**
 * @file
 * This file was auto-generated by generate-includes.php and includes all of
 * the core files required by HTML Purifier. Use this if performance is a
 * primary concern and you are using an opcode cache. PLEASE DO NOT EDIT THIS
 * FILE, changes will be overwritten the next time the script is run.
 *
 * @version 4.11.0
 *
 * @warning
 *      You must *not* include any other HTML Purifier files before this file,
 *      because 'require' not 'require_once' is used.
 *
 * @warning
 *      This file requires that the include path contains the HTML Purifier
 *      library directory; this is not auto-set.
 */
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Arborize.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrCollections.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTypes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrValidator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Bootstrap.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Definition.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/CSSDefinition.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Config.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ConfigSchema.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ContentSets.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Context.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCacheFactory.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Doctype.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DoctypeRegistry.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ElementDef.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Encoder.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/EntityLookup.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/EntityParser.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ErrorCollector.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ErrorStruct.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Exception.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Filter.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Generator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLDefinition.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModuleManager.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/IDAccumulator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Language.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/LanguageFactory.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Length.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Lexer.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Node.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/PercentEncoder.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/PropertyList.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/PropertyListIterator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Queue.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/StringHash.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/StringHashParser.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/TagTransform.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/TokenFactory.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URI.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIDefinition.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIParser.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URISchemeRegistry.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/UnitConverter.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/VarParser.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/VarParserException.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Zipper.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Clone.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Enum.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Integer.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Lang.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Switch.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/Text.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Number.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/AlphaValue.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Background.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/BackgroundPosition.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Border.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Color.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Composite.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/DenyElementDecorator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Filter.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Font.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/FontFamily.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Ident.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/ImportantDecorator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Length.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/ListStyle.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Multiple.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/Percentage.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/TextDecoration.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/CSS/URI.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Bool.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Nmtokens.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Class.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Color.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/FrameTarget.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/ID.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Pixels.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/Length.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/LinkTypes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/HTML/MultiLength.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI/Email.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI/Host.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI/IPv4.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI/IPv6.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrDef/URI/Email/SimpleCheck.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Background.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/BdoDir.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/BgColor.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/BoolToCSS.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Border.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/EnumToCSS.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/ImgRequired.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/ImgSpace.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Input.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Lang.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Length.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Name.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/NameSync.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Nofollow.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/SafeEmbed.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/SafeObject.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/SafeParam.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/ScriptRequired.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/TargetBlank.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/TargetNoopener.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/TargetNoreferrer.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/AttrTransform/Textarea.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Chameleon.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Custom.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Empty.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/List.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Required.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Optional.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/StrictBlockquote.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/ChildDef/Table.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache/Decorator.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache/Null.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache/Serializer.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache/Decorator/Cleanup.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/DefinitionCache/Decorator/Memory.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Bdo.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/CommonAttributes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Edit.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Forms.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Hypertext.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Iframe.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Image.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Legacy.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/List.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Name.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Nofollow.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/NonXMLCommonAttributes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Object.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Presentation.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Proprietary.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Ruby.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/SafeEmbed.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/SafeObject.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/SafeScripting.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Scripting.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/StyleAttribute.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tables.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Target.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/TargetBlank.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/TargetNoopener.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/TargetNoreferrer.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Text.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/XMLCommonAttributes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/Name.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/Proprietary.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/XHTMLAndHTML4.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/Strict.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/Transitional.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/HTMLModule/Tidy/XHTML.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/AutoParagraph.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/DisplayLinkURI.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/Linkify.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/PurifierLinkify.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/RemoveEmpty.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/RemoveSpansWithoutAttributes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Injector/SafeObject.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Lexer/DOMLex.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Lexer/DirectLex.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Node/Comment.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Node/Element.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Node/Text.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/Composite.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/Core.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/FixNesting.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/MakeWellFormed.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/RemoveForeignElements.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Strategy/ValidateAttributes.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/TagTransform/Font.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/TagTransform/Simple.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/Comment.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/Tag.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/Empty.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/End.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/Start.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/Token/Text.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/DisableExternal.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/DisableExternalResources.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/DisableResources.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/HostBlacklist.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/MakeAbsolute.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/Munge.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIFilter/SafeIframe.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/data.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/file.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/ftp.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/http.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/https.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/mailto.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/news.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/nntp.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/URIScheme/tel.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/VarParser/Flexible.php';
require APP_PATH . '../extend/HTMLPurifier/HTMLPurifier/VarParser/Native.php';
