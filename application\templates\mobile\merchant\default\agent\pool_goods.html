{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->
            <div class="search-form-wrapper">

                <form class="mb-3 pb-4 mt-2" action="{:url('agent/poolGoods')}" method="get">
                    <div class="input-group mb-2 input-group-sm">
                        <span class="input-group-text input-group-sm" id="basic-addon1">搜索方式</span>
                        <select name="type" id="type" class="form-select form-select-sm">
                            <option value="1" {if $Think.get.type=='1'}selected{/if}>按对接码搜索</option>
                            <option value="2" {if $Think.get.type=='2'}selected{/if}>按商品名称搜索</option>
                        </select>
                    </div>

                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm" id="basic-addon1">商品搜索</span>
                        <input class="form-control  form-control-sm"  name="key" type="search" placeholder="请输入对接码或商品名称" value="{$Think.get.key|default=''|htmlentities}">
                    </div>

                    <button class="btn btn-primary w-100 d-flex align-items-center justify-content-center btn-sm" type="submit">
                        <i class="bx bx-search me-2"></i>查询
                    </button>
                </form>
                <button class="btn btn-primary   align-items-center justify-content-center btn-sm  mt-1 mb-2" onclick="once_add()">
                    <i class="bx bx-plus"></i>一键对接
                </button>

            </div>

            {foreach $lists as $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">商品名称：{$v.name}</h6>
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>商品分类：</span>
                        <span>{$v.category.name|default="未分类"}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>店铺名称：</span>
                        <span>
                            {$v.user.shop_name}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>供货价格：</span>
                        <span>
                            {$v.proxy_price}元
                        </span>
                    </div>
                    <div class="order-wrap-text">
                        <span>联系方式：</span>
                        <span>
                            <a href="//wpa.qq.com/msgrd?v=3&uin={$v.user.qq}&Site=" target="_blank"><i class="iconfont icon-qq-white"></i>{$v.user.qq}</a>
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>上架时间：</span>
                        <span>{$v.create_at|date="Y-m-d H:i:s",###}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>库存：</span>
                        <span>  
                            {if $v->cards_stock_count > 0}
                            {$v->cards_stock_count}张
                            {else}
                            <span class="text-danger">断货</span>
                            {/if}
                        </span>
                    </div>

                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>代理审核：</span>
                        <span>
                            {switch name=":proxyStatus($v->user->id, $_user->id)"}
                            {case value="-2"}
                            {if $v.user.need_check_agent==1}
                            <span class="text-danger">需要审核</span>
                            {else/}
                            <span class="text-success">免审</span>
                            {/if}
                            {/case}
                            {case value="-1"}<span class="text-danger">申请拒绝</span>{/case}
                            {case value="0"}<span class="text-info">申请中</span>{/case}
                            {case value="1"}<span class="text-success">已通过</span>{/case}
                            {/switch}
                        </span>
                    </div>
                </div>

                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">
                    {if hasAgentAdd($_user->id, $v->id)==1 }
                    <p> <span class="text-warning">已对接</span></p>
                    {else}
                    <a class="me-1" href="javascript:void(0)" onclick="add('{:url(\'agent/add\',[\'proxy_code\'=>$v.proxy_code])}', '{:proxyStatus($v->user->id, $_user->id)}', '{$v.user.need_check_agent}', '{$v.user.id}')"><span class="goods-warp-btn">正常对接</span></a>
                    <a class="me-1" href="javascript:void(0)" onclick="$.x_show('快速对接', '{:url(\'agent/addLite\',[\'proxy_code\'=>$v.proxy_code])}', '90%')"><span class="goods-warp-btn">快速对接</span></a>
                    {/if}
                </div>
            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>


<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">一键对接</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">

                <form>
                    <div class="form-group">
                        <p>
                            <label for="customSelect" class="col-form-label">选择需要对接的分类，货源商分类下所有商品将会自动对接，多次对接自动跳过</label>
                            <button type="button" class="btn btn-primary btn-sm" id="selectAllButton">全选</button>
                            <button type="button" class="btn btn-primary btn-sm" id="deselectAllButton">取消全选</button>
                        </p>
                        <div class="custom-checkboxes">
                            {foreach $category_list as $k=> $v}
                            <div class="custom-checkbox">
                                <input type="checkbox" id="checkbox{$k}" value="{$v.id}">
                                <label for="checkbox{$k}">{$v.name}</label>
                            </div>
                            {/foreach}
                        </div>
                    </div>


                    <div class="form-group">
                        <label>加价方式：</label>
                        <div class="custom-control custom-radio">
                            <input type="radio" class="custom-control-input" id="fixedAmountRadio" name="priceOption" value="fixed" checked>
                            <label class="custom-control-label" for="fixedAmountRadio">固定加价金额</label>
                        </div>
                        <div class="custom-control custom-radio">
                            <input type="radio" class="custom-control-input" id="percentageRadio" name="priceOption" value="percentage">
                            <label class="custom-control-label" for="percentageRadio">加价百分比</label>
                        </div>
                    </div>

                    <div id="fixedAmountInput" class="form-group">
                        <label for="fixedAmount">固定加价金额：</label>
                        <input type="number" class="form-control" id="fixedAmount" placeholder="输入固定加价金额" value="0">
                        <small id="additionalPriceHelp" class="form-text text-muted">过低的加价则跟随供应商设置的最低加价进行对接</small>
                    </div>
                    <div id="percentageInput" class="form-group" style="display: none;">
                        <label for="percentage">加价百分比：</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="percentage" placeholder="输入加价百分比" value="20">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <small id="additionalPriceHelp" class="form-text text-muted">过低的加价则跟随供应商设置的最低加价进行对接</small>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="saveChangesButton">确定</button>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="js"}

<script>
    function once_add()
    {
        $('#myModal').modal('show');
        var fixedAmountRadio = $('#fixedAmountRadio');
        var percentageRadio = $('#percentageRadio');
        var fixedAmountInput = $('#fixedAmountInput');
        var percentageInput = $('#percentageInput');

        // 监听单选按钮的变化
        fixedAmountRadio.change(function () {
            if (fixedAmountRadio.is(':checked')) {
                // 如果固定加价金额被选中，显示固定加价金额输入表单，同时隐藏加价百分比输入表单
                fixedAmountInput.show();
                percentageInput.hide();
            }
        });

        percentageRadio.change(function () {
            if (percentageRadio.is(':checked')) {
                // 如果加价百分比被选中，显示加价百分比输入表单，同时隐藏固定加价金额输入表单
                percentageInput.show();
                fixedAmountInput.hide();
            }
        });

        return false;
    }
    // 保存选定值的数组
    var selectedValues = [];

    // 监听复选框的点击事件
    $('.custom-checkbox input[type="checkbox"]').change(function () {
        var value = $(this).val();
        if ($(this).is(':checked')) {
            selectedValues.push(value);
        } else {
            selectedValues = selectedValues.filter(function (item) {
                return item !== value;
            });
        }
    });
    // 全选按钮的点击事件处理程序
    $('#selectAllButton').click(function () {
        $('.custom-checkbox input[type="checkbox"]').prop('checked', true);
        selectedValues = Array.from($('.custom-checkbox input[type="checkbox"]')).map(function (checkbox) {
            return $(checkbox).val();
        });
    });

    $(".modal-dialog .close").click(function () {
        $(this).parents(".modal").click();
    });

    // 取消全选按钮的点击事件处理程序
    $('#deselectAllButton').click(function () {
        $('.custom-checkbox input[type="checkbox"]').prop('checked', false);
        selectedValues = [];
    });

    // 当保存更改按钮被点击时
    $('#saveChangesButton').click(function () {
        var fixedAmountRadio = $('#fixedAmountRadio');
        var percentageRadio = $('#percentageRadio');

        var add_type = fixedAmountRadio.is(':checked') ? 'fixed' : 'percentage';

        var loading = layer.load(1, {shade: [0.1, '#fff']});
        $.post("{:url('Agent/onceAdd')}", {
            category_ids: selectedValues,
            add_type: add_type,
            fixed_price: $('#fixedAmount').val(),
            percentage: $('#percentage').val(),
        }, function (res) {
            layer.close(loading);
            if (res.code != 1) {
                $.alert(res.msg);
            } else {
                $.alert(res.msg);
                setTimeout(function () {
                    location.reload();
                }, 1000);
            }
        });
    });






    function add(url, status, need_check_agent, user_id)
    {
        if (status == "-2")
        {
            if (need_check_agent == "1")
            {
                //需要审核
                $.confirm({
                    title: '温馨提示',
                    content: '成为此商家代理需要申请，确定要申请吗？',
                    type: 'red',
                    typeAnimated: true,
                    buttons: {
                        tryAgain: {
                            text: '确定',
                            btnClass: 'btn-red',
                            action: function () {
                                var loading = layer.load(1, {shade: [0.1, '#fff']});
                                $.post("{:url('Agent/applyProxy')}", {
                                    user_id: user_id
                                }, function (res) {
                                    layer.close(loading);
                                    if (res.code != 1) {
                                        $.alert(res.msg);
                                    } else {
                                        $.alert(res.msg);
                                        setTimeout(function () {
                                            location.reload();
                                        }, 200);
                                    }
                                });
                            }
                        },
                        cancel: {
                            text: '取消'
                        }
                    }
                });
            } else {
                location.href = url;
            }
        } else if (status == "-1")
        {
            layer.msg("申请代理拒绝，您无法对接！");
        } else if (status == "0")
        {
            layer.msg("申请代理审核中，请耐心等待!");
        } else if (status == "1")
        {
            location.href = url;
        }
    }
</script>
{/block}


