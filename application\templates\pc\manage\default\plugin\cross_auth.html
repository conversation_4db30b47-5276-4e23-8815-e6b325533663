{extend name='./content'}

{block name="content"}


<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户账号</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''|htmlentities}" placeholder="请输入商户账号" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select name="status">
                <option value="">全部状态</option>
                <option value="0" {if $Think.get.status === '0'}selected{/if}>待审核</option>
                <option value="1" {if $Think.get.status === '1'}selected{/if}>已审核</option>
                <option value="-1" {if $Think.get.status === '-1'}selected{/if}>已拒绝</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>

<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action">
    <table class="table table-hover">
        <thead>
            <tr>
                <th class="text-center">ID</th>
                <th class="text-center">用户名</th>
                <th class="text-center">申请时间</th>
                <th class="text-center">审核状态</th>
                <th class="text-center">操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $v}
            <tr>
                <td class="text-center">{$v.id}</td>
                <td class="text-center">{$v.user.username}</td>
                <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                <td class='text-center'>
                    {if $v.status==1}
                    <span style="color:green"><i class="glyphicon glyphicon-ok"></i> 已审核</span>
                    {elseif $v.status==-1/}
                    <span style="color:red"><i class="glyphicon glyphicon-remove"></i> 已拒绝</span>
                    {else/}
                    <span><i class="glyphicon glyphicon-question-sign"></i> 未审核</span>
                    {/if}

                </td>
                <td class='text-center'>
                    <a style="color:green" data-tips="确定不可用吗？ " data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("crossAuthChangeStatus")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-ok"></i>通过</a>
                    <a style="color:red" data-tips="确定可用吗？" data-update="{$v.id}" data-field='status' data-value='-1' data-action='{:url("crossAuthChangeStatus")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-remove"></i>拒绝</a>
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });
</script>
{/block}
