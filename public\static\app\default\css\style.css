/*=========================
深圳知宇信息科技有限公司
===========================*/

@Default Styles

Table of Content:
01/ variables
02/ predefine
03/ button
04/ header
05/ slider
06/ banner
07/ team
08/ portfolio
09/ blog
10/ products
11/ testimonial
12/ features
13/ video
14/ pricing
15/ contact 
16/ footer 
=====================================================================*/
/*=== fonts ====*/

@font-face {
  font-family: 'DroidSerif';
  font-style: italic;
  font-weight: 400;
  src: url("../fonts/DroidSerif-Italic.ttf") format("truetype");
}

/*===== color =====*/
/*====================================================*/
/*====================================================*/
.p0 {
  padding: 0px;
}

ul {
  list-style: none;
  margin: 0px;
  padding: 0px;
}

a {
  text-decoration: none;
}

a:hover, a:focus {
  text-decoration: none;
}

.row.m0 {
  margin: 0px;
}

body, p, h1, h2, h3, h4, h5, h6 {
  margin: 0px;
  padding: 0px;
}

body {
  font: 400 15px/28px "Microsoft YaHei", sans-serif;
  letter-spacing: 0px;
  color: #6f7982;
  padding: 0px !important;
}

a, .btn, button {
  text-decoration: none;
  outline: none;
}

a:hover, a:focus, .btn:hover, .btn:focus, button:hover, button:focus {
  text-decoration: none;
  outline: none;
}

.btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

section.row, header.row, footer.row {
  margin: 0;
}

.sec-pad {
  padding: 124px 0px 130px;
}

.section-title.title-w .title {
  color: #fff;
}

.section-title.title-w p {
  color: #fff;
}

.title {
  font: 400 35px/45px "Poppins", sans-serif;
  color: #1e2d3c;
  padding-bottom: 32px;
}

.section-title {
  text-align: center;
  max-width: 770px;
  margin: 0 auto 76px;
}

.section-title p {
  color: #6f7982;
}

.sec-border {
  border-color: #e1e1e1;
  margin: 110px 0px 130px;
}

/*====================================================*/
/*====================================================*/
/*============ Start header css ===========*/
.navbar-default {
  margin-bottom: 0px;
  border: 0px;
  border-radius: 0px;
  background: transparent;
  padding: 10px 30px;
  width: 100%;
  z-index: 2;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

@media (min-width: 991px) {
  .navbar-default .navbar-header {
    display: none;
  }
}

.navbar-default .menu {
  padding-top: 23px;
  -webkit-transition: all 0.1s linear;
  -o-transition: all 0.1s linear;
  transition: all 0.1s linear;
  float: none;
  text-align: center;
}

.navbar-default .menu li {
  margin-right: 45px;
}

.navbar-default .menu li a {
  font: 500 12px/35px "Poppins", sans-serif;
  letter-spacing: 0.30px;
  color: #fff;
  padding: 0px;
  position: relative;
  text-transform: uppercase;
}

.navbar-default .menu li a:before {
  content: "";
  width: 0px;
  height: 1px;
  background: #fff;
  bottom: 0;
  left: 0;
  position: absolute;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.navbar-default .menu li a:hover, .navbar-default .menu li a:focus {
  color: #fff;
}

.navbar-default .menu li.active a {
  background: transparent;
  color: #fff;
}

.navbar-default .menu li.active a:before {
  width: 25px;
}

.navbar-default .menu li.active a:hover, .navbar-default .menu li.active a:focus {
  background: transparent;
  color: #fff;
}

.navbar-default .menu li:hover a {
  background: transparent;
  color: #fff;
}

.navbar-default .menu li:hover a:before {
  width: 25px;
}

.navbar-default .menu li:last-child {
  margin-right: 0px;
}

.navbar-default .menu li.open a {
  background-color: transparent;
}

.navbar-default .menu li.open a:focus, .navbar-default .menu li.open a:hover {
  background-color: transparent;
}

.navbar-default .menu li.submenu {
  position: relative;
}

.navbar-default .menu li.submenu .dropdown-menu {
  padding: 10px 0px;
  margin: 0px;
  border: none;
  border-radius: 0px;
}

@media (min-width: 992px) {
  .navbar-default .menu li.submenu .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0px;
    min-width: 200px;
    background: #fff;
    text-align: left;
    opacity: 0;
    -webkit-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
    visibility: hidden;
    display: block;
  }
}

.navbar-default .menu li.submenu .dropdown-menu li {
  display: block;
  margin-right: 0px;
}

.navbar-default .menu li.submenu .dropdown-menu li a {
  padding: 0px 15px;
  text-transform: uppercase;
  -webkit-transition: all 400ms linear 0s;
  -o-transition: all 400ms linear 0s;
  transition: all 400ms linear 0s;
  display: block;
  color: #333;
}

.navbar-default .menu li.submenu .dropdown-menu li a:before {
  display: none;
}

.navbar-default .menu li.submenu .dropdown-menu li:hover a {
  color: #3fcef7;
}

@media (min-width: 992px) {
  .navbar-default .menu li.submenu:hover ul {
    visibility: visible;
    opacity: 1;
  }
}

.navbar-default .navbar-toggle {
  margin-top: 15px;
  margin-right: 0px;
  border: 0px;
  padding-left: 0px;
  padding-right: 0px;
  margin-bottom: 12px;
}

.navbar-default .navbar-toggle.collapsed .icon-bar {
  -webkit-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

.navbar-default .navbar-toggle.collapsed .icon-bar:nth-child(2) {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
  top: 0;
}

.navbar-default .navbar-toggle.collapsed .icon-bar:nth-child(3) {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.navbar-default .navbar-toggle.collapsed .icon-bar:nth-child(4) {
  opacity: 1;
}

.navbar-default .navbar-toggle .icon-bar {
  background-color: #fff;
  -webkit-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

.navbar-default .navbar-toggle .icon-bar:nth-child(2) {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  top: 6px;
  position: relative;
}

.navbar-default .navbar-toggle .icon-bar:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.navbar-default .navbar-toggle .icon-bar:nth-child(4) {
  opacity: 0;
}

.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
  background: transparent;
}

/*============ End header css ===========*/
.header {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 25;
  padding: 0px 45px;
}

.main_menu_area_one .menu_logo {
  display: block;
  line-height: 80px;
  height: auto;
  padding: 0px;
}

.main_menu_area_one .menu_logo img {
  max-width:220px;
  display: inline-block;
}

.main_menu_area_one .menu_logo img + img {
  display: none;
}

@media (max-width: 991px) {
  .main_menu_area_one .menu_logo {
    display: none;
  }
}

.main_menu_area_one .navbar-header .navbar-brand {
  padding-left: 0px;
  padding-top: 8px;
}

.main_menu_area_one .navbar-header .navbar-brand img + img {
  display: none;
}

@media (max-width: 991px) {
  .main_menu_area_one .right-icon {
    display: none;
  }
}

.main_menu_area_one .right-icon .navbar-right {
  margin-right: 0px;
}

.main_menu_area_one .right-icon .navbar-right li a {
  color: #fff;
}

.main_menu_area_one .right-icon .navbar-right li .cart-remove a {
  color: #3fcef7;
}

.main_menu_area_one.affix {
  background: #fff;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  width: 100%;
  left: 0;
  top: 0;
  position: fixed;
}

.main_menu_area_one.affix .menu_logo {
  display: block;
  line-height: 80px;
  height: auto;
  padding: 0px;
}

.main_menu_area_one.affix .menu_logo img {
  /*display: block;*/
}

.main_menu_area_one.affix .menu_logo img + img {
  display: inline-block;
}

@media (max-width: 991px) {
  .main_menu_area_one.affix .menu_logo img + img {
    display: none;
  }
}

.main_menu_area_one.affix .navbar-header .navbar-brand img {
  display: none !important;
}

.main_menu_area_one.affix .navbar-header .navbar-brand img + img {
  display: block !important;
}

.main_menu_area_one.affix .right-icon .navbar-right li .counter-shop-box {
  background: #1e2d3c;
}

.main_menu_area_one.affix .right-icon .navbar-right li a {
  color: #1e2d3c;
}

.main_menu_area_one.affix .right-icon .navbar-right li a:hover {
  color: #3fcef7;
}

.main_menu_area_one.affix .right-icon .navbar-right li a:hover .counter-shop-box {
  background: #3fcef7;
}

.main_menu_area_one.affix .navbar {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
  padding: 0px;
}

.main_menu_area_one.affix .navbar .navbar-header .navbar-brand img {
  display: inline-block;
}

.main_menu_area_one.affix .navbar .menu li a {
  color: #1e2d3c;
}

.main_menu_area_one.affix .navbar .menu li.active a {
  color: #3fcef7;
}

.main_menu_area_one.affix .navbar .menu li.active a:before {
  background: #3fcef7;
}

.main_menu_area_one.affix .navbar .menu li.active a:hover, .main_menu_area_one.affix .navbar .menu li.active a:focus {
  color: #3fcef7;
}

.main_menu_area_one.affix .navbar .menu li.submenu .dropdown-menu li a {
  color: #333;
}

.main_menu_area_one.affix .navbar .menu li.submenu .dropdown-menu li a:hover {
  color: #3fcef7;
}

.main_menu_area_one.affix .navbar .menu li:hover a {
  color: #3fcef7;
}

.main_menu_area_one.affix .navbar .menu li:hover a:before {
  background: #3fcef7;
}

.main_menu_area_one.affix .navbar-toggle .icon-bar {
  background: #3fcef7;
}

.navbar {
  padding: 0px;
  position: relative;
  -webkit-transition: all 0s linear;
  -o-transition: all 0s linear;
  transition: all 0s linear;
}

.navbar .menu {
  float: none;
  text-align: center;
}

.navbar .menu li {
  float: none;
  display: inline-block;
}

.navbar .menu li a {
  color: #fff;
  text-transform: uppercase;
}

.navbar .menu li a:focus {
  background: transparent;
}

.right-icon .navbar-right {
  margin-right: 0px;
}

.right-icon .navbar-right li a {
  font-size: 15px;
  color: #62d5f8;
  padding: 0px;
  line-height: 80px;
  margin-left: 35px;
}

.right-icon .navbar-right li a:hover, .right-icon .navbar-right li a:focus {
  background-color: transparent;
}

.right-icon .navbar-right li .counter-shop-box {
  display: none;
  position: absolute;
  top: 24px;
  right: -14px;
  height: 16px;
  width: 16px;
  font-size: 12px;
  text-align: center;
  line-height: 14px;
  color: white;
  border-radius: 50px;
  background: #62d5f8;
}

.right-icon .navbar-right li.submenu {
  position: relative;
}

.right-icon .navbar-right li.submenu .dropdown-menu {
  padding: 10px 30px;
  margin: 0px;
  border: none;
  border-radius: 0px;
}

@media (min-width: 992px) {
  .right-icon .navbar-right li.submenu .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0px;
    min-width: 330px;
    background: #fff;
    text-align: left;
    opacity: 0;
    -webkit-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
    visibility: hidden;
    display: block;
  }
}

.right-icon .navbar-right li.submenu .dropdown-menu li .cart-pricing {
  border-bottom: 1px solid #eee;
  float: left;
  width: 100%;
  margin-bottom: 20px;
}

.right-icon .navbar-right li.submenu .dropdown-menu li .cart-pricing .total {
  color: #777;
  font-size: 14px;
  font-weight: 500;
  padding: 12px;
  letter-spacing: .9px;
  text-transform: uppercase;
}

.right-icon .navbar-right li.submenu .dropdown-menu li .cart-pricing .total .p-total {
  float: right;
  font-size: 12px;
}

.right-icon .navbar-right li.submenu .dropdown-menu li .btn-cart {
  font: 500 14px/20px "Poppins", sans-serif;
  background: #c1c1c1;
  color: #000;
  border-radius: 0px;
  margin-bottom: 10px;
  padding: 10px 20px;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.right-icon .navbar-right li.submenu .dropdown-menu li .btn-cart:hover {
  background: #33cdf8;
  color: #fff;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-right: 0px;
  padding: 15px 0px;
  border-bottom: 1px solid #ededed;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item a {
  line-height: 20px;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item .cart-content {
  padding-left: 30px;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item .cart-content .cart-title {
  font: 500 16px/22px "Poppins", sans-serif;
  margin: 0px;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item .cart-content p {
  color: #777;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: .9px;
  margin-bottom: 0px;
  line-height: 15px;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item .cart-content a {
  line-height: 20px;
  color: #777;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: .9px;
  margin-left: 0px;
}

.right-icon .navbar-right li.submenu .dropdown-menu .cart-single-item:hover a {
  color: #3fcef7;
}

@media (min-width: 992px) {
  .right-icon .navbar-right li.submenu:hover ul {
    visibility: visible;
    opacity: 1;
  }
}

.main_menu_area_two {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 25;
  padding: 0px 45px;
}

.main_menu_area_two .menu_logo a {
  display: inline-block;
  line-height: 80px;
}

.main_menu_area_two .navbar-default .menu li a {
  color: #1e2d3c;
  text-transform: uppercase;
}

.main_menu_area_two .navbar-default .menu li a:focus {
  background: transparent;
}

.main_menu_area_two .navbar-default .menu li.active a {
  color: #3fcef7;
}

.main_menu_area_two .navbar-default .menu li.active a:before {
  background: #3fcef7;
}

.main_menu_area_two .navbar-default .menu li.active a:hover, .main_menu_area_two .navbar-default .menu li.active a:focus {
  color: #3fcef7;
}

.main_menu_area_two .navbar-default .menu li:hover a {
  color: #3fcef7;
}

.main_menu_area_two .navbar-default .menu li:hover a:before {
  background: #3fcef7;
}

.main_menu_area_two .navbar-header .navbar-brand {
  padding-left: 0px;
  padding-top: 10px;
}

.main_menu_area_two .navbar-toggle .icon-bar {
  background: #3fcef7;
}

.main_menu_area_two + section, .main_menu_area_two + div {
  margin-top: 80px;
}

@media (max-width: 991px) {
  .main_menu_area_two + section, .main_menu_area_two + div {
    margin-top: 61px;
  }
}

.main_menu_area_three {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 25;
  padding: 0px 45px;
}

.main_menu_area_three .navbar {
  padding: 0px;
  position: relative;
}

.main_menu_area_three .navbar .menu li a {
  color: #1e2d3c;
  text-transform: uppercase;
}

.main_menu_area_three .navbar .menu li.active a {
  color: #3fcef7;
}

.main_menu_area_three .navbar .menu li.active a:before {
  background: #3fcef7;
}

.main_menu_area_three .navbar .menu li.active a:hover, .main_menu_area_three .navbar .menu li.active a:focus {
  color: #3fcef7;
}

.main_menu_area_three .navbar .menu li:hover a {
  color: #3fcef7;
}

.main_menu_area_three .navbar .menu li:hover a:before {
  background: #3fcef7;
}

.main_menu_area_three .get_btn {
  border-color: #3fcef7;
  color: #3fcef7;
}

.main_menu_area_three .get_btn:hover {
  background: #3fcef7;
  color: #fff;
}

.main_menu_area_three + section, .main_menu_area_three + div {
  margin-top: 80px;
}

/*affix css
==============================================*/
.main_menu_area_two, .main_menu_area_three {
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.main_menu_area_two.affix, .main_menu_area_three.affix {
  background: #fff;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  width: 100%;
  left: 0;
  top: 0;
  position: fixed;
}

.main_menu_area_two.affix .right-icon .navbar-right li .counter-shop-box, .main_menu_area_three.affix .right-icon .navbar-right li .counter-shop-box {
  background: #1e2d3c;
}

.main_menu_area_two.affix .right-icon .navbar-right li a, .main_menu_area_three.affix .right-icon .navbar-right li a {
  color: #1e2d3c;
}

.main_menu_area_two.affix .right-icon .navbar-right li a:hover, .main_menu_area_three.affix .right-icon .navbar-right li a:hover {
  color: #3fcef7;
}

.main_menu_area_two.affix .right-icon .navbar-right li a:hover .counter-shop-box, .main_menu_area_three.affix .right-icon .navbar-right li a:hover .counter-shop-box {
  background: #3fcef7;
}

.main_menu_area_two.affix .navbar, .main_menu_area_three.affix .navbar {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
  padding: 0px;
}

.main_menu_area_two.affix .navbar .navbar-header .navbar-brand img, .main_menu_area_three.affix .navbar .navbar-header .navbar-brand img {
  display: inline-block;
}

.main_menu_area_two.affix .navbar .menu li a, .main_menu_area_three.affix .navbar .menu li a {
  color: #1e2d3c;
}

.main_menu_area_two.affix .navbar .menu li.active a, .main_menu_area_three.affix .navbar .menu li.active a {
  color: #3fcef7;
}

.main_menu_area_two.affix .navbar .menu li.active a:before, .main_menu_area_three.affix .navbar .menu li.active a:before {
  background: #3fcef7;
}

.main_menu_area_two.affix .navbar .menu li.active a:hover, .main_menu_area_two.affix .navbar .menu li.active a:focus, .main_menu_area_three.affix .navbar .menu li.active a:hover, .main_menu_area_three.affix .navbar .menu li.active a:focus {
  color: #3fcef7;
}

.main_menu_area_two.affix .navbar .menu li.submenu .dropdown-menu li a, .main_menu_area_three.affix .navbar .menu li.submenu .dropdown-menu li a {
  color: #333;
}

.main_menu_area_two.affix .navbar .menu li.submenu .dropdown-menu li a:hover, .main_menu_area_three.affix .navbar .menu li.submenu .dropdown-menu li a:hover {
  color: #3fcef7;
}

.main_menu_area_two.affix .navbar .menu li:hover a, .main_menu_area_three.affix .navbar .menu li:hover a {
  color: #3fcef7;
}

.main_menu_area_two.affix .navbar .menu li:hover a:before, .main_menu_area_three.affix .navbar .menu li:hover a:before {
  background: #3fcef7;
}

.popup-wrapper {
  position: fixed;
  z-index: 50;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  left: -100%;
  -o-transition-delay: .3s;
  transition-delay: .3s;
  -webkit-transition-delay: .3s;
}

.popup-wrapper .bg-layer {
  position: absolute;
  left: 0;
  top: -100px;
  right: 0;
  bottom: -100px;
  background: rgba(0, 0, 0, 0.8);
  -o-transition: all .3s ease-out;
  transition: all .3s ease-out;
  -webkit-transition: all .3s ease-out;
  opacity: 0;
}

.popup-wrapper .popup-content {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%) !important;
  -ms-transform: translateY(-50%) !important;
  transform: translateY(-50%) !important;
  width: 100%;
  height: auto;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0;
  text-align: center;
  -webkit-transition: opacity .3s ease-out, left 0s .3s, -webkit-transform .3s ease-out;
  transition: opacity .3s ease-out, left 0s .3s, -webkit-transform .3s ease-out;
  -o-transition: transform .3s ease-out, opacity .3s ease-out, left 0s .3s;
  transition: transform .3s ease-out, opacity .3s ease-out, left 0s .3s;
  transition: transform .3s ease-out, opacity .3s ease-out, left 0s .3s, -webkit-transform .3s ease-out;
  -webkit-transition: transform .3s ease-out, opacity .3s ease-out, left 0s .3s;
  opacity: 0;
  left: -10000px;
  padding: 15px;
}

.popup-wrapper .popup-content .layer-close {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 10000px;
}

.popup-wrapper .popup-content .popup-container {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  position: relative;
  text-align: left;
  background: #fff;
  max-width: 570px;
}

.popup-wrapper .popup-content .popup-container.searhPopup {
  position: relative;
  background: #252525;
  padding: 70px 50px 90px;
}

.popup-wrapper .popup-content .popup-container.size-2 {
  max-width: 950px;
}

.popup-wrapper .popup-content .popup-container form {
  position: relative;
  overflow: hidden;
}

.popup-wrapper .popup-content .popup-container form h5 {
  padding: 0px;
  font: 600 28px/46px "Poppins", sans-serif;
  color: #fff;
  margin: 0px;
}

.popup-wrapper .popup-content .popup-container form .simple-input {
  font-size: 14px;
  height: 50px;
  line-height: 50px;
  padding: 0px;
  width: 100%;
  border: 1px #eee solid;
  -o-transition: all .15s;
  transition: all .15s;
  -webkit-transition: all .15s;
  background: transparent;
  border: 0px;
  font-size: 16px;
  line-height: 25px;
  color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.popup-wrapper .popup-content .popup-container form .simple-input.placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.popup-wrapper .popup-content .popup-container form .simple-input:-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.popup-wrapper .popup-content .popup-container form .simple-input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.popup-wrapper .popup-content .popup-container form .simple-input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.popup-wrapper .popup-content .popup-container form .simple-input:focus {
  outline: none;
}

.popup-wrapper .popup-content .popup-container form .simple-input:focus + .searchBorderBottom:before {
  width: 100%;
}

.popup-wrapper .popup-content .popup-container form .searchBorderBottom {
  position: relative;
  height: 2px;
  width: 100%;
  background: #535353;
  content: '';
}

.popup-wrapper .popup-content .popup-container form .searchBorderBottom:before {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: #4356ea;
  content: '';
  -webkit-transition: all 200ms linear;
  -o-transition: all 200ms linear;
  transition: all 200ms linear;
}

.popup-wrapper button {
  position: absolute;
  bottom: 10px;
  right: 8px;
  width: 30px;
  height: 30px;
  border: 0px;
  background: transparent;
}

.popup-wrapper button i {
  font-size: 16px;
  line-height: 26px;
  color: #909090;
  -webkit-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  transition: all 150ms linear;
}

.popup-wrapper.active {
  left: 0;
  -o-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-transition-delay: 0s;
}

.popup-wrapper.active .bg-layer {
  opacity: 1;
}

.popup-wrapper.active .popup-content {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  left: 0;
  transition: opacity .3s ease-out, left 0s 0s, -webkit-transform .3s ease-out;
  -webkit-transition: opacity .3s ease-out, left 0s 0s, -webkit-transform .3s ease-out;
  -o-transition: transform .3s ease-out, opacity .3s ease-out, left 0s 0s;
  transition: transform .3s ease-out, opacity .3s ease-out, left 0s 0s;
  transition: transform .3s ease-out, opacity .3s ease-out, left 0s 0s, -webkit-transform .3s ease-out;
  position: relative;
}

.popup-wrapper .button-close {
  width: 30px;
  height: 30px;
  display: inline-block;
  vertical-align: bottom;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
  color: #fff;
  opacity: 0.6;
}

.popup-wrapper .button-close i {
  display: inline-block;
  font-size: 20px;
}

.popup-wrapper .button-close:hover {
  opacity: 1;
}

/*====================================================*/
/*====================================================*/
.get_btn {
  font: 500 12px/26px "Poppins", sans-serif;
  letter-spacing: 0.9px;
  text-transform: uppercase;
  border: 1px solid #fff;
  padding: 8px 35px;
  color: #fff;
  border-radius: 40px;
  margin-top: 17px;
  -webkit-transform: perspective(1px) translateZ(0);
  transform: perspective(1px) translateZ(0);
  -webkit-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  transition-duration: 0.5s;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.get_btn:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 0 50%;
  -ms-transform-origin: 0 50%;
  transform-origin: 0 50%;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
  border-radius: 40px;
}

.get_btn:hover {
  color: #3fcef7;
  -webkit-box-shadow: 0px 4.99px 22px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 4.99px 22px 0px rgba(0, 0, 0, 0.15);
}

.get_btn:hover:before {
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
  -o-transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
  transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
}

/*===== hero btn css =====*/
.pro_btn {
  font-size: 16px;
  line-height: 40px;
  font-weight: 600;
  letter-spacing: 1.6px;
  text-transform: uppercase;
  color: #fff;
  border: 1px solid #fff;
  padding: 10px 47px 8px;
  border-radius: 50px;
  margin-top: 45px;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.pro_btn:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 0 50%;
  -ms-transform-origin: 0 50%;
  transform-origin: 0 50%;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}

.pro_btn:hover {
  color: #3fcef7;
  -webkit-box-shadow: 7.5px 12.99px 25px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 7.5px 12.99px 25px 0px rgba(0, 0, 0, 0.15);
}

.pro_btn:hover:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}

.green_btn {
  background: #8ec549;
  color: #fff;
  border: 0px;
}

.green_btn:before {
  background: #6dad29;
  border-color: #6dad29;
}

.green_btn:hover {
  color: #fff;
}

.btn_blue {
  color: #fff;
}

.btn_blue:hover {
  color: #0b3b7e !important;
}

.social-btn {
  border: 0px;
}

.social-btn:before {
  background-image: -moz-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  background-image: -webkit-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  background-image: -ms-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  -webkit-border-image: -webkit-linear-gradient(185deg, #23ea8f 0%, #009efd 100%);
  -o-border-image: -o-linear-gradient(185deg, #23ea8f 0%, #009efd 100%);
  border-image: linear-gradient(-95deg, #23ea8f 0%, #009efd 100%);
  border-image-slice: 1;
  -webkit-transform: scaleX(0) !important;
  -ms-transform: scaleX(0) !important;
  transform: scaleX(0) !important;
}

.social-btn:hover {
  color: #fff;
  border: 0px;
  border-color: #009efd;
}

/*====================================================*/
/*====================================================*/
.hero_area {
  background: #3fcef7;
  padding: 200px 0px;
  position: relative;
}

.hero_area:before {
  content: "";
  position: absolute;
  bottom: -1px;
  background: url("../image/banner/shape-bg.png") no-repeat scroll center bottom;
  left: 0;
  width: 100%;
  height: calc(100% - 72px);
}

@media (min-width: 1921px) {
  .hero_area:before {
    background-size: cover;
    bottom: -3px;
  }
}

.hero_text {
  color: #fff;
  padding-top: 84px;
  padding-right: 57px;
}

.hero_text h2 {
  font-size: 42px;
  line-height: 60px;
  font-weight: 300;
  padding-bottom: 22px;
}

.hero_text p {
  font-size: 24px;
  line-height: 40px;
  font-weight: 300;
}

.header_mac_img {
  position: absolute;
  left: -87px;
  width: auto;
}

.hero_text .hero_title {
  font-size: 58px;
  line-height: 80px;
}

/*=========== hero_area_two css =============*/
.banner_pad {
  padding: 220px 0px;
  overflow: hidden;
}

.hero_area_two {
  background: url("../image/banner/banner-2.jpg") no-repeat scroll center 0;
  background-size: cover;
}

.hero_area_two .hero_text {
  padding-top: 30px;
  padding-right: 25px;
}

.hero_area_two .hero_text h2 {
  font-size: 60px;
  line-height: 80px;
}

.hero_area_two .hero_text p {
  font-weight: 300;
}

.hero_area_two.seo-features-two:before {
  height: calc(100% - 425px);
}

.hero_area_two.seo-features-two .header_mac_img {
  left: 87px;
}

.hero_area_two.seo-features-two .pro_btn:hover {
  color: #0b3774;
}

/*========== seo-social_banner css ========*/
.seo-social_banner {
  background-image: -moz-linear-gradient(145deg, #fdc64e 0%, #e23a39 100%);
  background-image: -webkit-linear-gradient(145deg, #fdc64e 0%, #e23a39 100%);
  background-image: -ms-linear-gradient(145deg, #fdc64e 0%, #e23a39 100%);
}

.seo-social_banner .header_mac_img {
  left: 0;
}

.seo-social_banner .pro_btn {
  color: #fff;
}

.seo-social_banner .pro_btn:before {
  background-image: -webkit-linear-gradient(55deg, #fdc64e 0%, #e23a39 100%);
  background-image: -o-linear-gradient(55deg, #fdc64e 0%, #e23a39 100%);
  background-image: -webkit-linear-gradient(305deg, #fdc64e 0%, #e23a39 100%);
  background-image: -o-linear-gradient(305deg, #fdc64e 0%, #e23a39 100%);
  background-image: linear-gradient(145deg, #fdc64e 0%, #e23a39 100%);
  border-top: 2px solid transparent;
  -webkit-border-image: -webkit-linear-gradient(305deg, #fdc64e 0%, #e23a39 100%);
  -o-border-image: -o-linear-gradient(305deg, #fdc64e 0%, #e23a39 100%);
  border-image: linear-gradient(145deg, #fdc64e 0%, #e23a39 100%);
  border-image-slice: 1;
}

.seo-social_banner .pro_btn:hover {
  color: #fff;
  border-color: transparent;
}

/*===== startup_banner css ====*/
.startup_banner {
  background: url("../image/banner/banner-3.jpg") no-repeat scroll center 0;
  background-size: cover;
  position: relative;
}

.startup_banner:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  background: #222624;
  opacity: 0.60;
}

/*===== software_banner css ====*/
.software_banner {
  background: url("../image/software/banner.jpg") no-repeat scroll center 0;
  background-size: cover;
}

.software_banner .header_mac_img {
  left: 0;
}

/*===== social_banner css ====*/
.social_banner {
  background: url("../image/banner/social.jpg") no-repeat scroll center 0;
  background-size: cover;
}

.social_banner:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-image: -moz-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  background-image: -webkit-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  background-image: -ms-linear-gradient(165deg, #23ea8f 0%, #009efd 100%);
  opacity: 0.8;
  position: absolute;
}

.social_banner .social-btn {
  background: #fff;
  color: #269bb9;
}

.social_banner .social-btn:hover {
  color: #fff;
}

.social_banner .social-btn:hover:before {
  -webkit-transform: scaleX(1) !important;
  -ms-transform: scaleX(1) !important;
  transform: scaleX(1) !important;
}

/* shop_banner css
======================================*/
.shop_banner {
  background: url("../image/shop/shop-banner.jpg") no-repeat scroll center 0;
  background-size: cover;
  background-attachment: fixed;
  position: relative;
  z-index: 1;
  padding: 128px 0px 80px;
}

.shop_banner:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #2b2b2b;
  opacity: 0.60;
  position: absolute;
  z-index: -1;
}

.shop_banner .hero_text {
  padding-top: 0px;
}

.shop_banner .hero_text .hero_title {
  text-transform: uppercase;
  font-size: 40px;
  line-height: 45px;
  letter-spacing: 2px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  padding-bottom: 28px;
}

.shop_banner .hero_text p {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0.28px;
}

/*====================================================*/
/*====================================================*/
/*==========Start perfect area css =============*/
.perfect-item .media .media-left {
  padding-right: 30px;
}

.perfect-item .media .media-body h3 {
  font-size: 24px;
  line-height: 30px;
  color: #1e2d3c;
  text-transform: capitalize;
  padding-bottom: 22px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.perfect-item + .perfect-item {
  margin-top: 55px;
}

/*==========End perfect area css =============*/
/*==========Start seo-features css =============*/
.bg-color {
  background: #f9faff;
}

.seo-features {
  padding: 130px 0px;
}

.seo-features-bg {
  background: url("../image/features/features-bg.jpg") no-repeat scroll center 0;
  background-size: cover;
  color: #fff;
}

.seo-features-bg .title {
  color: #fff;
}

.seo-features-bg img {
  padding-left: 70px;
}

.seo-features-bg .pro_btn {
  -webkit-box-shadow: 7.5px 12.99px 25px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 7.5px 12.99px 25px 0px rgba(0, 0, 0, 0.15);
  color: #8ec549;
}

.seo-features-bg .pro_btn:before {
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
}

.seo-features-bg .pro_btn:hover {
  color: #fff !important;
}

.seo-features-bg .pro_btn:hover:before {
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
}

.wave {
  position: relative;
}

.wave .wave-img {
  position: absolute;
  top: 0;
  width: 100%;
  height: 149px;
  background: url("../image/features/wave.png");
  background-repeat: repeat-x;
  -webkit-animation: animations 20s ease-in-out infinite;
  animation: animations 20s ease-in-out infinite;
}

.wave ul {
  text-align: right;
}

.wave ul li {
  display: inline-block;
  font-size: 14px;
  line-height: 22px;
  padding: 200px 0px 0px;
  color: #bac1c8;
  position: relative;
  width: 20px;
  text-align: center;
}

.wave ul li .round-s {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  left: 0;
  top: 24px;
  opacity: 0;
}

.wave ul li .round-s .br {
  height: 140px;
  width: 1px;
  position: absolute;
  background: #adadad;
  left: 50%;
  margin-left: -0.5px;
  top: 26px;
}

.wave ul li .round-s:before {
  content: "";
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #39b54a;
  opacity: 0.302;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.wave ul li .round-s:after {
  content: '';
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #8ec549;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.wave ul li:nth-child(odd) .round-s {
  top: 65px;
}

.wave ul li:nth-child(odd) .round-s .br {
  height: 96px;
}

.wave ul li:nth-child(6) .round-s {
  top: 100px;
}

.wave ul li:nth-child(6) .round-s .br {
  height: 67px;
}

.wave ul li + li {
  margin-left: 48px;
}

.wave ul li.active {
  color: #000;
}

.wave ul li.active .round-s {
  opacity: 1;
}

.seo-features-two {
  background: #0b3774;
  position: relative;
}

.seo-features-two:before {
  content: "";
  background: url("../image/features/analyzing-bg.png") no-repeat scroll center 0;
  background-size: cover;
  position: absolute;
  width: 100%;
  height: calc(100% - 180px);
  bottom: 0px;
}

@media (min-width: 2000px) {
  .seo-features-two:before {
    height: 100%;
  }
}

.seo-features-two .btn_blue {
  color: #0b3b7e;
}

.seo-features-two .btn_blue:hover {
  color: #fff;
}

.features-content ul {
  padding-top: 12px;
}

.features-content ul li {
  position: relative;
  padding-left: 25px;
  padding-top: 14px;
}

.features-content ul li i {
  color: #8ec549;
  position: absolute;
  left: 0;
  line-height: 28px;
}

.business-grow {
  padding-top: 20px;
}

.business-grow .title {
  padding-bottom: 10px;
}

.business-grow ul {
  padding-top: 0px;
}

/*==========End seo-features css =============*/
.seo-rang-area {
  text-align: center;
}

.seo-rang-area .range-content .title {
  padding-top: 60px;
}

/*=========Start power_features css ==========*/
.power_features {
  padding: 130px 0px 200px;
}

.power_features .power_fea_items {
  padding: 0px 96px;
}

.power_features .power_fea_items .perfect-item {
  margin-bottom: 65px;
}

.power_features .power_fea_items .perfect-item .media .media-body h3 {
  font-size: 18px;
}

/*=========End power_features css ==========*/
.power_features_two {
  padding: 125px 0px 74px;
}

.power_features_two .power_fea_items {
  padding: 0px;
}

.power_features_two .perfect-item {
  margin-bottom: 50px;
}

.power_features_two .perfect-item .media .media-left {
  display: block;
  padding-right: 0px;
  margin-bottom: 30px;
}

/*========= case-study-area css ========*/
.bg-transition {
  background: url("../image/features/pattern-bg.png");
  background-position: 0px -10px;
  background-repeat: repeat;
  -webkit-animation: animated-bg 30s linear infinite alternate;
  animation: animated-bg 30s linear infinite alternate;
  z-index: 1;
  position: relative;
}

.bg-transition:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  background: #ff6f51;
  opacity: 0.96;
  z-index: -1;
}

.case-study-area {
  padding: 280px 0px 130px;
}

.c_study_area_two {
  padding: 125px 0px;
}

.seo_study_area:before {
  background: #31d297;
}

.study-form {
  position: relative;
}

.subscribe_form {
  background: #fff;
  text-align: center;
  padding: 80px 0px;
  position: absolute;
  width: 100%;
  -webkit-transform: translateY(-140%);
  -ms-transform: translateY(-140%);
  transform: translateY(-140%);
  border-radius: 5px;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}

.subscribe_form .title {
  padding-bottom: 50px;
}

.mchimp-errmessage, .mchimp-sucmessage {
  color: #3fcef7;
}

.subcribes {
  max-width: 630px;
  margin: 0 auto;
}

.subcribes .form-control {
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0px;
  background: #fff;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 50px !important;
  font: 400 15px/46px "Poppins", sans-serif;
  padding: 2px 25px 0px;
  height: auto;
}

.subcribes .input-group-btn {
  padding: 0px 0px 0px 20px;
}

.subcribes .input-group-btn .btn-submit {
  border: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 40px;
  font: 400 16px/36px "Poppins", sans-serif;
  color: #fff;
  padding: 7px 51px;
  background: #8ec549;
  overflow: hidden;
}

.subcribes .input-group-btn .btn-submit:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #6dad29;
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 0 50%;
  -ms-transform-origin: 0 50%;
  transform-origin: 0 50%;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}

.subcribes .input-group-btn .btn-submit:hover:before {
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
}

.study-slider .study-item {
  background: #fff;
  text-align: center;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.study-slider .study-item img {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.study-slider .study-item h2 a {
  font: 400 24px/40px "Poppins", sans-serif;
  color: #1e2d3c;
  padding-bottom: 17px;
  padding-top: 0px;
  text-transform: capitalize;
}

.study-slider .study-item h2 a:hover {
  color: #8ec549;
}

.study-slider .study-item .text {
  padding: 40px 39px 32px;
}

.study-slider .study-item a {
  font-size: 14px;
  line-height: 28px;
  text-transform: uppercase;
  color: #8ec549;
  display: inline-block;
  padding-top: 27px;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.study-slider .study-item a:hover i {
  padding-left: 8px;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.owl-dots {
  text-align: center;
  margin-top: 76px;
  line-height: 10px;
}

.owl-dots .owl-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #fff;
  opacity: 0.40;
  display: inline-block;
  margin: 0px 10px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.owl-dots .owl-dot.active {
  opacity: 1;
}

/*============= chose_service_area css ==============*/
.chose_service_area {
  padding: 130px 0px 125px;
}

.chose_content h5 {
  font-size: 20px;
  line-height: 23px;
  letter-spacing: 0.5px;
  color: #9ca4ac;
  text-transform: uppercase;
}

.chose_content h2 {
  font-size: 30px;
  line-height: 40px;
  color: #1e2d3c;
  padding: 27px 0px 22px;
}

.chose_content p {
  font-size: 16px;
}

.chose-item {
  padding-left: 75px;
}

@-webkit-keyframes animated-bg {
  form {
    background-position: 0px -10px;
  }
  to {
    background-position: 0px -1000px;
  }
}

@keyframes animated-bg {
  form {
    background-position: 0px -10px;
  }
  to {
    background-position: 0px -1000px;
  }
}

@-webkit-keyframes animations {
  form {
    background-position: 0px;
  }
  to {
    background-position: 1000px;
  }
}

@keyframes animations {
  form {
    background-position: 0px;
  }
  to {
    background-position: 1000px;
  }
}

/*============ subcribe_area css ============*/
.subcribe_area {
  text-align: center;
}

.subcribe_area.bg-transition:before {
  background: #ffa00a;
}

.subcribe_area img {
  display: inline-block;
}

.subcribe_area .section-title {
  margin: 45px auto;
}

.subcribe_area .section-title .title {
  font-size: 30px;
  padding-bottom: 15px;
}

.subscribe_area_two {
  padding: 85px 0px 70px;
}

.subscribe_area_two:before {
  background: #ffffff;
}

.subscribe_area_two .subscribe {
  padding-top: 15px;
}

.subscribe-content h6 {
  font-size: 20px;
  line-height: 32px;
  color: #6f7982;
  font-weight: 400;
  padding-bottom: 15px;
}

.subscribe-content h2 {
  font-size: 30px;
  line-height: 42px;
  color: #1e2d3c;
  font-weight: 400;
}

/*======== social-analyzing css ========*/
.social-analyzing {
  background-image: -moz-linear-gradient(165deg, rgba(35, 234, 143, 0.8) 0%, rgba(0, 158, 253, 0.8) 100%);
  background-image: -webkit-linear-gradient(165deg, rgba(35, 234, 143, 0.8) 0%, rgba(0, 158, 253, 0.8) 100%);
  background-image: -ms-linear-gradient(165deg, rgba(35, 234, 143, 0.8) 0%, rgba(0, 158, 253, 0.8) 100%);
  position: relative;
}

.social-analyzing:before {
  content: "";
  background: url("../image/features/analyzing-bg2.png") no-repeat scroll center 0;
  background-size: cover;
  position: absolute;
  width: 100%;
  height: calc(100% - 180px);
  bottom: 0px;
}

@media (min-width: 2000px) {
  .social-analyzing:before {
    height: 100%;
  }
}

.social-analyzing .social-btn {
  background: #fff;
}

.social-analyzing .social-btn:hover:before {
  -webkit-transform: scaleX(1) !important;
  -ms-transform: scaleX(1) !important;
  transform: scaleX(1) !important;
}

/*====================================================*/
/*====================================================*/
.testimonial-area {
  background: url("../image/features/testimonial-bg.jpg") no-repeat scroll center 0;
  background-size: cover;
  position: relative;
  z-index: 1;
}

.testimonial-area:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #7ed321;
  opacity: 0.75;
  position: absolute;
  z-index: -1;
}

.testimonial-slider .review-content {
  background: #ebf6e0;
  padding: 60px 40px;
  -webkit-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

.testimonial-slider .review-content p {
  font-size: 16px;
  font-style: italic;
}

.testimonial-slider .review-content .media {
  padding-bottom: 25px;
}

.testimonial-slider .review-content .media .media-left {
  padding-right: 30px;
}

.testimonial-slider .review-content .media .media-left .img {
  width: 78px;
  height: 78px;
  border: 1px solid #fff;
  border-radius: 50%;
  text-align: center;
}

.testimonial-slider .review-content .media .media-left img {
  width: auto;
  max-width: none;
  display: inline-block;
}

.testimonial-slider .review-content .media .media-body {
  vertical-align: middle;
}

.testimonial-slider .review-content .media .media-body h2 {
  font-size: 18px;
  line-height: 32px;
  color: #1e2d3c;
}

.testimonial-slider .review-content .media .media-body p {
  font-size: 18px;
  color: #555555;
}

.testimonial-slider .center .review-content {
  background: #fff;
}

/*============= testimonial_area_two css ===========*/
.testimonial_area_two {
  background: #f8fcff;
}

.testimonial_area_two .section-title {
  margin-bottom: 65px;
}

.testimonial_area_two .section-title .title {
  padding-bottom: 0px;
}

.clients_slider .clients_content {
  background-color: white;
  -webkit-box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05);
  padding: 70px;
  margin-top: 10px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.clients_slider .clients_content p {
  font-size: 20px;
  line-height: 35px;
  padding-top: 25px;
}

.clients_slider .clients_content i {
  font-size: 230px;
  color: #f8f8f8;
  position: absolute;
  z-index: -1;
  top: 15px;
  right: 30px;
}

.clients_slider .clients_content i:after {
  content: "\f10e";
  position: absolute;
  color: #ed7241;
  font-size: 15px;
  top: 34px;
  right: 20px;
}

.clients_slider .clients_content .media .media-left {
  width: 80px;
  width: 80px;
  border-radius: 50%;
  padding-right: 20px;
}

.clients_slider .clients_content .media .media-left img {
  width: auto;
}

.clients_slider .clients_content .media .media-body {
  vertical-align: middle;
}

.clients_slider .clients_content .media .media-body h3 {
  font-size: 20px;
  line-height: 38px;
  color: #1e2d3c;
  font-weight: 500;
}

.clients_slider .clients_content .media .media-body h6 {
  font-size: 16px;
  line-height: 28px;
  color: #aaaaaa;
  font-weight: 500;
}

.clients_slider .owl-dots {
  margin-top: 52px;
}

.clients_slider .owl-dots .owl-dot {
  background: #c1c8cd;
}

.clients_slider .owl-dots .owl-dot.active {
  background: #6f7982;
}

/*=========== team_area css ==========*/
.team_area {
  background: #f8fcff;
}

.team_slider .item {
  padding-bottom: 30px;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}

.team_slider .owl-dots {
  margin-top: 50px;
}

.team_slider .owl-dots .owl-dot {
  background: #c1c8cd;
}

.team_slider .owl-dots .owl-dot.active {
  background: #6f7982;
}

.team_member {
  text-align: center;
  border: 1px solid #ebebeb;
  border-radius: 5px;
  background: #fff;
  padding: 35px;
}

.team_member .member_img {
  width: 170px;
  height: 170px;
  background: #ddecf5;
  border-radius: 50%;
  display: inline-block;
  overflow: hidden;
  border: 5px solid #ddecf5;
}

.team_member h2 {
  font-size: 20px;
  line-height: 35px;
  color: #1e2d3c;
  padding-top: 12px;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.team_member h6 {
  font-size: 14px;
  line-height: 28px;
  color: #b8b8b8;
}

.team_member p {
  padding: 15px 0px 20px;
}

.team_member a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #e3e8ed;
  border-radius: 50%;
  color: #6f7b8c;
  line-height: 40px;
  font-size: 14px;
  margin: 0px 5px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.team_member a:hover {
  background: #3b5998;
  color: #fff;
}

.team_member a.twitter:hover {
  background: #1da1f2;
}

.team_member a.instra:hover {
  background: #3f729b;
}

.team_member:hover {
  -webkit-box-shadow: 0px 15px 25px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 15px 25px 0px rgba(0, 0, 0, 0.05);
}

.team_member:hover h2 {
  color: #3b5998;
}

/*====================================================*/
/*====================================================*/
.contact_area .section-title h2 {
  padding-bottom: 0px;
}

.contact-form .form-group {
  position: relative;
  padding-bottom: 15px;
}

.contact-form .form-group .form-control {
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #ebebeb;
  height: 60px;
  padding-left: 30px;
  font: 400 15px/28px "Poppins", sans-serif;
  color: #9c9c9c;
}

.contact-form .form-group .form-control.error {
  border-color: #3fcef7;
}

.contact-form .form-group .form-control.error + .error {
  font: 13px/24px "Poppins", sans-serif;
  font-weight: normal;
  color: #3fcef7;
  position: absolute;
  bottom: -7px;
  margin: 0px;
  width: 80%;
  left: 15px;
}

.contact-form .form-group textarea.form-control {
  height: 280px;
  padding-top: 20px;
}

.contact-form .pro_btn {
  display: block;
  width: 100%;
  margin-top: 15px;
}

.contact-form #success, .contact-form #error {
  display: none;
}

/*=========== subcribe_analysis css ============*/
.subcribe_analysis:before {
  background: #f8fcff;
}

.subcribe_analysis .section-title {
  margin-bottom: 40px;
}

.analysis-form {
  max-width: 850px;
  margin: 0 auto;
  text-align: center;
}

.analysis-form .form-group {
  padding: 0px 10px 10px;
}

.analysis-form .form-group .form-control {
  border-radius: 50px;
  height: 50px;
  width: 100%;
}

.analysis-form .form-group .form-control:focus, .analysis-form .form-group .form-control:hover {
  border-color: #ebebeb;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}

.analysis-form .green_btn {
  display: inline-block;
  width: auto;
  padding: 0px 40px;
  margin-top: 28px;
  font-size: 14px;
  line-height: 50px;
  letter-spacing: 1px;
}

/*====================================================*/
/*====================================================*/
/*=============== start pricing-area css ===================*/
.perfect_price_plan {
  padding: 120px 0px 130px;
}

.price-tab {
  max-width: 570px;
  margin: 40px auto 0px;
  border: 0px;
}

.price-tab li {
  padding: 0px;
  margin: 0px;
  width: 50%;
}

.price-tab li a {
  padding: 0px 50px;
  font: 700 16px/60px "Open Sans", sans-serif;
  color: #1a264a;
  text-transform: uppercase;
  margin: 0px;
  letter-spacing: 1.6px;
  border: 1px solid #e2e5e7;
  border-radius: 40px 0px 0px 40px;
  text-align: center;
}

.price-tab li a:hover, .price-tab li a:focus {
  background: #f8fafc;
}

.price-tab li.active a {
  background: #f8fafc;
  border: 1px solid #e2e5e7;
  border-bottom-right-radius: 0px;
  border-top-right-radius: 0px;
}

.price-tab li.active a:hover, .price-tab li.active a:focus {
  background: #f8fafc;
  border-color: #e2e5e7;
}

.price-tab li:last-child a {
  border-radius: 0px 40px 40px 0px;
}

.priceing-tab {
  padding: 50px 0px 0px;
}

.price .pricing-box {
  border-radius: 3px;
  background-color: white;
  border: 1px solid #f8f8f8;
  text-align: center;
  padding: 0px 0px 40px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  margin-top: 30px;
}

.price .pricing-box .pricing-header {
  position: relative;
  background: #f8fafc;
  padding-top: 10px;
  position: relative;
}

.price .pricing-box .pricing-header:before {
  content: "";
  height: 33px;
  width: 3px;
  background: #7ed321;
  position: absolute;
  left: 0;
  top: 20px;
}

.price .pricing-box .pricing-header .tag {
  font: 600 12px/23px "Open Sans", sans-serif;
  color: #fff;
  text-transform: uppercase;
  background: #f88e40;
  width: 75px;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  position: absolute;
  top: 4px;
  right: 0;
  padding: 4px 0px;
  border: 0px;
}

.price .pricing-box .pricing-header .tag:before {
  content: "";
  width: 0;
  height: 0;
  border-left: 0px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 13px solid #f88e40;
  position: absolute;
  top: 1px;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  right: -14px;
}

.price .pricing-box .pricing-header .tag:after {
  content: "";
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 0px solid transparent;
  border-bottom: 13px solid #f88e40;
  position: absolute;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  right: -14px;
  bottom: 1px;
}

.price .pricing-box .pricing-header h2 {
  font: 400 18px/60px "Roboto", sans-serif;
  color: #8d99b3;
  text-transform: capitalize;
  padding-bottom: 0px;
}

.price .pricing-box .pricing-header .packeg_typ {
  font: 600 60px/60px "Open Sans", sans-serif;
  color: #1a264a;
  padding: 0px 0px 45px;
}

.price .pricing-box .pricing-header .packeg_typ span {
  font: 400 24px/35px "Open Sans", sans-serif;
  letter-spacing: 1px;
  vertical-align: top;
}

.price .pricing-box .pricing-header .packeg_typ small {
  font-family: "Lato", sans-serif;
  font-size: 16px;
  line-height: 30px;
  vertical-align: bottom;
  color: #1a264a;
}

.price .pricing-box .plan-lists {
  padding: 38px 0px 22px;
}

.price .pricing-box .plan-lists li {
  font: 400 15px/36px "Open Sans", sans-serif;
  color: #6f7982;
}

.price .pricing-box:hover {
  -webkit-box-shadow: 0px 17px 37px 0px rgba(26, 38, 74, 0.25);
  box-shadow: 0px 17px 37px 0px rgba(26, 38, 74, 0.25);
}

.price .pricing-box:hover .purchase-btn {
  background: #7ed321;
  color: #fff;
}

.price .pricing-box.active {
  -webkit-box-shadow: 0px 17px 37px 0px rgba(26, 38, 74, 0.25);
  box-shadow: 0px 17px 37px 0px rgba(26, 38, 74, 0.25);
}

.try {
  display: block;
  font: 400 12px/50px "Open Sans", sans-serif;
  color: #b8b8b8;
  text-transform: uppercase;
  -webkit-transition: all 400ms linear 0s;
  -o-transition: all 400ms linear 0s;
  transition: all 400ms linear 0s;
}

.try:hover {
  color: #7ed321;
}

.purchase-btn {
  font-size: 14px;
  color: #1a264a;
  font-weight: 700;
  letter-spacing: 1.4px;
  border: 1px solid #8ec549;
  display: inline-block;
  padding: 2px 46px;
  border-radius: 40px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  line-height: 50px;
  text-transform: uppercase;
}

/*=============== End pricing-area css ===================*/
/*============ pricing-area-two css ============*/
.pricing-table {
  max-width: 770px;
  margin: 0px auto;
  padding-top: 20px;
  padding-bottom: 23px;
}

.pricing-table .price {
  padding: 30px 0px;
  background-color: white;
  -webkit-box-shadow: 0px 1px 10px 0px rgba(18, 81, 125, 0.15);
  box-shadow: 0px 1px 10px 0px rgba(18, 81, 125, 0.15);
  text-align: center;
  border-top: 5px solid #8ec549;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.pricing-table .price .pricing-header {
  margin: 0px 30px;
  border-bottom: 1px solid #e3eaeb;
}

.pricing-table .price .pricing-header .packeg_typ {
  font-size: 60px;
  line-height: 60px;
  color: #8ec549;
}

.pricing-table .price .pricing-header .packeg_typ span {
  vertical-align: top;
  font-size: 24px;
  line-height: 32px;
}

.pricing-table .price .pricing-header h4 {
  font-size: 16px;
  color: #606d8d;
  padding: 10px 0px 20px;
}

.pricing-table .price .list-unstyled {
  padding: 15px 0px 0px;
  min-height: 185px;
}

.pricing-table .price .list-unstyled li {
  line-height: 36px;
}

.pricing-table .price .try {
  display: block;
  font-size: 12px;
  line-height: 40px;
  letter-spacing: 1.2px;
  color: #b8b8b8;
  text-transform: uppercase;
}

.pricing-table .price .try:hover {
  color: #8ec549;
}

.pricing-table .price .purchase-btn {
  font-size: 14px;
  line-height: 41px;
  color: #1a264a;
  text-transform: uppercase;
  font-weight: 600;
  display: inline-block;
  padding: 6px 52px;
  border: 1px solid #8ec549;
  border-radius: 50px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.pricing-table .price:nth-child(2) {
  border-top: 5px solid transparent;
  -webkit-border-image: -webkit-gradient(linear, right top, left top, from(#6e09b5), color-stop(0%, #6e09b5), color-stop(100%, #c198ff), color-stop(99%, #c198ff));
  -webkit-border-image: -webkit-linear-gradient(right, #6e09b5 0%, #6e09b5 0%, #c198ff 100%, #c198ff 99%);
  -o-border-image: -o-linear-gradient(right, #6e09b5 0%, #6e09b5 0%, #c198ff 100%, #c198ff 99%);
  border-image: -webkit-gradient(linear, right top, left top, from(#6e09b5), color-stop(0%, #6e09b5), color-stop(100%, #c198ff), color-stop(99%, #c198ff));
  border-image: linear-gradient(-90deg, #6e09b5 0%, #6e09b5 0%, #c198ff 100%, #c198ff 99%);
  border-image-slice: 1;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
  z-index: 1;
}

.pricing-table .price:nth-child(3) {
  border-top: 5px solid transparent;
  -webkit-border-image: -webkit-gradient(linear, right top, left top, from(#14dbff), color-stop(0%, #14dbff), color-stop(100%, #a5f1ff), color-stop(99%, #a5f1ff));
  -webkit-border-image: -webkit-linear-gradient(right, #14dbff 0%, #14dbff 0%, #a5f1ff 100%, #a5f1ff 99%);
  -o-border-image: -o-linear-gradient(right, #14dbff 0%, #14dbff 0%, #a5f1ff 100%, #a5f1ff 99%);
  border-image: -webkit-gradient(linear, right top, left top, from(#14dbff), color-stop(0%, #14dbff), color-stop(100%, #a5f1ff), color-stop(99%, #a5f1ff));
  border-image: linear-gradient(-90deg, #14dbff 0%, #14dbff 0%, #a5f1ff 100%, #a5f1ff 99%);
  border-image-slice: 1;
}

.pricing-table .price:hover {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
  z-index: 3;
}

.pricing-table .price:hover .purchase-btn {
  background: #8ec549;
  color: #fff;
}

/*========= clients_logo_area css =========*/
.clients_logo_area {
  border-bottom: 1px solid #e1e1e1;
  padding: 38px 0px;
}

.clients_logo_area .clients_logo_s {
  text-align: center;
}

.clients_logo_area .clients_logo_s img {
  width: auto;
  opacity: 0.40;
  display: inline-block;
  -webkit-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
  cursor: pointer;
}

.clients_logo_area .clients_logo_s img:hover {
  opacity: 1;
}

/*====================================================*/
/*====================================================*/
/*========== image-gallery css ===========*/
.gallery-item {
  position: relative;
  overflow: hidden;
}

.gallery-item img {
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.gallery-item .content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  font-size: 24px;
  line-height: 35px;
  font-family: "Poppins", sans-serif;
  color: #fff;
  padding: 18px 30px;
  background: rgba(0, 0, 0, 0.17);
}

.gallery-item:hover img {
  -webkit-transform: scale(1.02);
  -ms-transform: scale(1.02);
  transform: scale(1.02);
}

/*===========analytics_progress_area css ===========*/
.analytics_progress_area {
  background: #1f2932;
}

.analytics-progressbar {
  text-align: center;
}

.analytics-progressbar .circle {
  position: relative;
}

.analytics-progressbar .text {
  position: absolute;
  font-size: 30px;
  font-family: "Poppins", sans-serif;
  color: #fff;
  font-weight: 400;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.analytics-progressbar h2 {
  font-size: 20px;
  color: #fff;
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  padding: 20px 0px 15px;
}

.analytics-progressbar p {
  font-size: 15px;
  color: #fff;
  opacity: 0.50;
}

/*portfolio_area css
================================*/
.portfolio_area {
  padding: 120px 0px;
}

.portfolio_filter {
  text-align: center;
  margin-bottom: 70px;
}

.portfolio_filter li {
  font: 500 14px/26px "Poppins", sans-serif;
  color: #1e2d3c;
  letter-spacing: 0.70px;
  text-transform: uppercase;
  padding: 0px 0px 4px;
  border-bottom: 2px solid transparent;
  margin: 0px 20px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  cursor: pointer;
}

.portfolio_filter li.active, .portfolio_filter li:hover {
  border-color: #33cdf8;
}

.portfolio-item {
  display: block;
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

.portfolio-item:before {
  position: absolute;
  top: 10px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  content: '';
  opacity: 0;
  background: #fff;
  -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
  transition: opacity 0.35s, -webkit-transform 0.35s;
  -o-transition: opacity 0.35s, transform 0.35s;
  transition: opacity 0.35s, transform 0.35s;
  transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;
  -webkit-transform: scale3d(1.4, 1.4, 1);
  transform: scale3d(1.4, 1.4, 1);
}

.portfolio-item img {
  max-width: 100%;
  width: 100%;
}

.portfolio-item .project-hover {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  padding: 2em;
  overflow: hidden;
}

.portfolio-item .project-hover h5 {
  font: 600 14px/24px "Poppins", sans-serif;
  color: #111111;
  letter-spacing: 0.70px;
  text-transform: uppercase;
  padding-bottom: 7px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  -webkit-transform: translate3d(0, 10px, 0);
  transform: translate3d(0, 10px, 0);
  opacity: 0;
}

.portfolio-item .project-hover h6 {
  font: 400 13px/20px "Poppins", sans-serif;
  color: #777777;
  -webkit-transition: -webkit-transform 0.35s;
  transition: -webkit-transform 0.35s;
  -o-transition: transform 0.35s;
  transition: transform 0.35s;
  transition: transform 0.35s, -webkit-transform 0.35s;
  opacity: 0;
  -webkit-transform: translate3d(0, 20px, 0);
  transform: translate3d(0, 20px, 0);
}

.portfolio-item:hover:before {
  opacity: 0.80;
  -webkit-transform: scale3d(1, 1, 1);
  transform: scale3d(1, 1, 1);
}

.portfolio-item:hover .project-hover h5, .portfolio-item:hover .project-hover h6 {
  opacity: 1;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.load-btn {
  border: 2px solid #d4d4d4;
  font: 600 12px/48px "Poppins", sans-serif;
  color: #111111;
  letter-spacing: 0.60px;
  display: block;
  width: 170px;
  height: 50px;
  text-transform: uppercase;
  text-align: center;
  margin: 40px auto 0px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.load-btn:hover {
  background: #33cdf8;
  border-color: #33cdf8;
  color: #fff;
}

img.mfp-img {
  padding-left: 20px;
  padding-right: 20px;
  background: #ececec;
}

.mfp-title {
  display: none;
}

.mfp-counter {
  color: #000;
  right: 20px;
}

.mfp-image-holder .mfp-close, .mfp-iframe-holder .mfp-close {
  color: #000;
  right: 10px;
}

.pr_fluid_two {
  margin-bottom: 30px;
}

.pr_fluid_two .portfolio-item {
  margin-bottom: 0px;
}

/*====================================================*/
/*====================================================*/
/*============ seo_services_area css ============*/
.seo_services_area {
  padding: 100px 0px 80px;
}

.service_item {
  text-align: center;
  border: 1px solid #ebebeb;
  padding: 60px 30px;
  margin-bottom: 30px;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.service_item h2 {
  font: 400 20px/30px "Poppins", sans-serif;
  color: #1e2d3c;
  padding: 35px 0px 14px;
}

.service_item:hover {
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}

/*=========== video_area css ===========*/
.video_area {
  background: #f8fcff;
}

.video {
  padding-right: 0px;
  position: relative;
}

.video img {
  max-width: 100%;
}

.video .icon {
  width: 100px;
  height: 100px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 25px;
  color: #8ec549;
  text-align: center;
  line-height: 100px;
}

/*============ faq_area css ===========*/
.faq_area .faq_img {
  padding-top: 110px;
  text-align: right;
}

.faq_area .faq_img img {
  max-width: 100%;
}

.faq_area .chose-item {
  padding-left: 15px;
  padding-right: 75px;
}

.faq_area .chose_content h2 {
  padding-top: 0px;
}

.faq-inner-accordion .panel {
  border-radius: 0px;
  border: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.faq-inner-accordion .panel .panel-heading {
  padding: 0px;
  border-radius: 0px;
}

.faq-inner-accordion .panel .panel-heading .panel-title a {
  font: 600 14px/30px "Poppins", sans-serif;
  color: #1e2d3c;
  display: block;
  text-transform: uppercase;
  padding: 35px 30px;
  position: relative;
  background-color: white;
  -webkit-box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.05);
}

.faq-inner-accordion .panel .panel-heading .panel-title a i {
  position: absolute;
  right: 30px;
  top: 50%;
  margin-top: -10px;
  font-size: 20px;
}

.faq-inner-accordion .panel .panel-heading .panel-title a i + i {
  display: none;
}

.faq-inner-accordion .panel .panel-heading .panel-title a.collapsed i {
  display: none;
}

.faq-inner-accordion .panel .panel-heading .panel-title a.collapsed i + i {
  display: block;
}

.faq-inner-accordion .panel .panel-body {
  font-size: 16px;
  line-height: 28px;
  color: #6f7982;
  background-color: #f8f8f8;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border: 0px !important;
  border-radius: 0px;
  padding: 35px 32px 35px 30px;
}

.faq-inner-accordion .panel + .panel {
  margin-top: 1px;
}

/*=========== service_two_area css ============*/
.service_two_area {
  position: relative;
  overflow: hidden;
  padding: 130px 0px 200px;
}

.service_two_area .service_img {
  position: absolute;
  left: -12px;
}

.service_two_area .features-content {
  padding-right: 100px;
}

.service_two_area .features-content .title {
  font-size: 30px;
}

.first_f {
  padding-bottom: 150px;
}

.first_f .service_img {
  left: auto;
  right: 3px;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

.first_f .features-content {
  padding-right: 0px;
  padding-left: 100px;
}

/*========== easy_steps css ==========*/
.easy_steps {
  padding: 124px 0px 100px;
}

.easy_slider .service_item {
  margin-left: 15px;
  margin-right: 15px;
  padding: 30px;
}

.easy_slider .service_item img {
  width: auto;
  display: inline-block;
}

.easy_slider .service_item a {
  font-size: 14px;
  color: #8ec549;
  text-transform: uppercase;
  font-weight: 600;
  padding-top: 10px;
  display: inline-block;
}

.easy_slider .service_item a i {
  padding-left: 5px;
}

.social_f {
  padding-bottom: 200px;
}

.social_f .service_img {
  -webkit-box-shadow: none;
  box-shadow: none;
}

/*====================================================*/
/*====================================================*/
/* shop-product-area
=========================================*/
.shop-product-area {
  padding: 120px 0px;
}

.shop-product-area .display-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.title-icon {
  font: 500 13px/15px "Poppins", sans-serif;
  color: #333333;
  -ms-flex-item-align: center;
  align-self: center;
}

.short-list {
  font: 500 13px/15px "Poppins", sans-serif;
  color: #333333;
}

.short-list .list {
  padding-right: 20px;
}

.product-option {
  width: 185px !important;
}

.product-option .btn-default {
  border-radius: 0px;
  font: 500 12px/24px "Poppins", sans-serif;
  color: #333333 !important;
  border-color: #ececec !important;
  padding: 12px 15px;
  border-radius: 0px;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.product-option .btn-default:hover, .product-option .btn-default:focus {
  background: transparent;
  outline: none !important;
}

.product-items .shop-product {
  text-align: center;
  font-family: "Poppins", sans-serif;
  margin-top: 40px;
}

.product-items .shop-product .product-image {
  background: #ececec;
  padding: 43px 0px;
  position: relative;
  z-index: 1;
}

.product-items .shop-product .product-image:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0;
  position: absolute;
  z-index: 0;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.product-items .shop-product .product-image img {
  max-width: 100%;
}

.product-items .shop-product .product-image .preview-meta {
  position: absolute;
  text-align: center;
  top: 50%;
  left: 0;
  margin-top: -24px;
  width: 100%;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.product-items .shop-product .product-image .preview-meta li {
  display: inline-block;
  -webkit-transform: translateY(50px);
  -ms-transform: translateY(50px);
  transform: translateY(50px);
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.product-items .shop-product .product-image .preview-meta li a {
  background: rgba(255, 255, 255, 0.9);
  padding: 0px;
  cursor: pointer;
  display: inline-block;
  font-size: 20px;
  -webkit-transition: .2s all;
  -o-transition: .2s all;
  transition: .2s all;
  width: 48px;
  color: #333333;
  height: 48px;
  line-height: 52px;
  border-radius: 50%;
}

.product-items .shop-product .product-image .preview-meta li a:hover {
  background: #33cdf8;
  color: #fff;
}

.product-items .shop-product .product-image .preview-meta li:nth-child(2) {
  -webkit-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

.product-items .shop-product .product-image .preview-meta li:nth-child(3) {
  -webkit-transition: all 0.6s linear;
  -o-transition: all 0.6s linear;
  transition: all 0.6s linear;
}

.product-items .shop-product .product-content {
  padding: 20px 0px;
}

.product-items .shop-product .product-content h4 {
  font-size: 15px;
  line-height: 28px;
  text-transform: uppercase;
  color: #333333;
  font-weight: 500;
  -webkit-transition: .3s all;
  -o-transition: .3s all;
  transition: .3s all;
}

.product-items .shop-product .product-content .rating {
  color: #33cdf8;
  padding: 5px 0px;
}

.product-items .shop-product .product-content .price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 20px;
  letter-spacing: 0.32px;
}

.product-items .shop-product .product-content .price del {
  color: #929292;
  font-size: 12px;
  font-weight: 400;
  padding-right: 6px;
}

.product-items .shop-product:hover .product-image:before {
  opacity: 0.50;
}

.product-items .shop-product:hover .product-image .preview-meta {
  opacity: 1;
}

.product-items .shop-product:hover .product-image .preview-meta li {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

.product-items .shop-product:hover .product-content h4 {
  color: #33cdf8;
}

.pagination {
  text-align: center;
  margin: 100px 0px 0px;
}

.pagination .page-numbers li {
  display: inline-block;
  margin: 0px 2px;
}

.pagination .page-numbers li .page-numbers {
  width: 40px;
  height: 40px;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 40px;
  color: #777777;
  background: #ececec;
  display: inline-block;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.pagination .page-numbers li .page-numbers span {
  font-size: 12px;
}

.pagination .page-numbers li .page-numbers:hover, .pagination .page-numbers li .page-numbers.active {
  background: #33cdf8;
  color: #fff;
}

.pagination .page-numbers li .page-numbers.prev {
  margin-right: 42px;
}

.pagination .page-numbers li .page-numbers.next {
  margin-left: 42px;
}

.portfolio_popup {
  max-width: 940px;
  margin: 0 auto;
  position: relative;
}

.portfolio_popup .popup_row {
  background: #fff;
}

.portfolio_popup .popup_row .pop_img img {
  max-width: 100%;
}

.portfolio_popup .popup_row .popup_content {
  padding: 70px 30px;
}

.portfolio_popup .popup_row .popup_content h2 {
  font: 600 20px/30px "Poppins", sans-serif;
  color: #333;
}

.portfolio_popup .popup_row .popup_content .review_num {
  color: #333333;
  font-size: 12px;
  font-weight: 300;
}

.portfolio_popup .popup_row .popup_content .stock {
  padding-top: 0px;
  padding-bottom: 10px;
}

.portfolio_popup .popup_row .popup_content p {
  font: 400 14px/25px "Poppins", sans-serif;
  padding: 10px 0px 25px;
}

.portfolio_popup .popup_row .popup_content p .light-red {
  color: red;
}

.portfolio_popup .popup_row .popup_content .price {
  font-weight: 400;
  font-size: 13px;
  vertical-align: middle;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  line-height: 65px;
}

.portfolio_popup .popup_row .popup_content .price .new_price {
  font-size: 30px;
  color: #ff3636;
  font-weight: 500;
  margin-left: 30px;
}

.portfolio_popup .popup_row .popup_content .price .old_price {
  color: #666666;
  font-size: 18px;
  text-decoration: line-through;
  margin-left: 30px;
}

.portfolio_popup .popup_row .popup_content .quantity-box {
  float: left;
  position: relative;
  width: 80px;
}

.portfolio_popup .popup_row .popup_content .quantity-box > input {
  height: 44px;
  padding: 0;
  width: 100%;
  text-align: center;
  text-indent: -16px;
}

.portfolio_popup .popup_row .popup_content .quantity-box .qty-control {
  background-color: #ccc;
  color: #fff;
  height: 18px;
  line-height: 14px;
  position: absolute;
  right: 1px;
  text-align: center;
  cursor: pointer;
  top: 1px;
  width: 15px;
}

.portfolio_popup .popup_row .popup_content .quantity-box .qty-control.plus {
  top: auto;
  bottom: 1px;
}

.portfolio_popup .popup_row .popup_content .wided {
  padding: 20px 0px;
  overflow: hidden;
}

.portfolio_popup .popup_row .popup_content .button_group {
  float: right;
}

.portfolio_popup .popup_row .popup_content .button_group .add-cart {
  padding: 5px 20px;
  font: 400 14px/32px "Poppins", sans-serif;
  color: #666;
  border: 1px solid #ededed;
  width: 140px;
  text-align: center;
}

.portfolio_popup .popup_row .popup_content .button_group a {
  width: 45px;
  font-size: 20px;
  color: #666;
  line-height: 42px;
  border: 1px solid #ededed;
  display: block;
  text-align: center;
  float: left;
  margin: 0px 3px;
  -webkit-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}

.portfolio_popup .popup_row .popup_content .button_group a:hover {
  background: #62d5f8;
  color: #fff;
}

.portfolio_popup .popup_row .popup_content .button_group .compare-button {
  display: block;
  margin: 0;
  padding: 0;
  float: left;
}

/*====================================================*/
/*====================================================*/
/* blog_area css
========================================*/
.blog_area {
  padding: 80px 0px;
}

.post-contents {
  margin-top: 50px;
}

.post-contents .post-title {
  font-size: 24px;
  line-height: 36px;
  color: #1e2d3c;
  text-transform: uppercase;
  font-weight: 500;
}

.post-contents p {
  letter-spacing: 0.30px;
  margin-bottom: 40px;
}

.post-contents blockquote {
  padding: 0px 0px 0px 60px;
  border: 0px;
  font-size: 14px;
  color: #6f7982;
  font-family: 'DroidSerif';
  font-weight: 400;
  margin-bottom: 40px;
}

.post-contents .post-meta {
  margin: 15px 0px 18px;
}

.blog-author {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #ececec;
  padding: 20px 0px;
}

.blog-author .media {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 86.2%;
  flex: 0 0 86.2%;
}

.blog-author .media .media-left {
  padding-right: 15px;
}

.blog-author .media .media-body h6 {
  font-size: 11px;
  line-height: 22px;
  font-weight: 600;
  color: #777777;
}

.blog-author .media .media-body h5 {
  font-size: 12px;
  line-height: 24px;
  letter-spacing: 0.60px;
  text-transform: uppercase;
  color: #111111;
  font-weight: 600;
}

.blog-author .share-btn {
  vertical-align: middle;
  -ms-flex-item-align: center;
  align-self: center;
}

.blog-author .share-btn a {
  font: 500 12px/24px "Poppins", sans-serif;
  color: #111111;
  text-transform: uppercase;
  border: 1px solid #ececec;
  padding: 6px 22px;
  display: block;
}

.post-meta li {
  font-size: 14px;
  line-height: 30px;
  color: #333333;
  display: inline-block;
  letter-spacing: 0.28px;
}

.post-meta li a {
  color: #3fcef7;
  padding-left: 5px;
}

.post-meta li + li:before {
  content: "|";
  margin-left: 8px;
  margin-right: 13px;
}

.related-post {
  margin-top: 50px;
  margin-bottom: 45px;
}

.related-post .blog-rlated-item {
  text-align: center;
}

.related-post .blog-rlated-item h3 {
  font-size: 18px;
  line-height: 36px;
  color: #1e2d3c;
  text-transform: uppercase;
  font-weight: 500;
  padding: 20px 0px 0px;
}

.comments {
  border-top: 1px solid #ececec;
}

.comments .comment {
  margin-top: 0px;
}

.comments .comment .media-left {
  padding-right: 20px;
}

.comments .comment .media-body .comment-name {
  display: inline-block;
  font-size: 13px;
  line-height: 24px;
}

.comments .comment .media-body .comment-name h5 {
  text-transform: uppercase;
  font-weight: 600;
  color: #111111;
  letter-spacing: 0.65px;
}

.comments .comment .media-body .comment-name h6 {
  font-weight: 400;
  color: #a8a8a8;
  padding-top: 10px;
}

.comments .comment .media-body p {
  font-size: 14px;
  line-height: 30px;
  padding-top: 12px;
  padding-bottom: 50px;
}

.comments .comment .media-body .reply-btn {
  float: right;
  font-size: 11px;
  line-height: 24px;
  color: #333333;
  font-weight: 600;
  text-transform: uppercase;
  padding-top: 8px;
}

.comments .comment + .comment, .comments .comment.reply {
  border-top: 1px solid #ececec;
  padding-top: 35px;
}

.comment-title {
  font-size: 14px;
  line-height: 28px;
  color: #111111;
  text-transform: uppercase;
  margin: 45px 0px;
}

.comment-contact {
  border-top: 1px solid #ececec;
  padding-right: 100px;
}

.comment-form .form-control {
  font-size: 14px;
  line-height: 40px;
  height: 45px;
  color: #333333;
  border: 1px solid #ececec;
  border-radius: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-transform: capitalize;
  padding: 0px 20px;
  margin-bottom: 30px;
}

.comment-form .form-control.placeholder {
  color: #333333;
}

.comment-form .form-control:-moz-placeholder {
  color: #333333;
}

.comment-form .form-control::-moz-placeholder {
  color: #333333;
}

.comment-form .form-control::-webkit-input-placeholder {
  color: #333333;
}

.comment-form input {
  width: 48.6%;
  float: left;
}

.comment-form input + input {
  margin-left: 2.79%;
}

.comment-form textarea {
  min-height: 190px;
}

.comment-form .sub_btn {
  font-size: 12px;
  line-height: 25px;
  text-transform: uppercase;
  color: #fff;
  background: #3fcef7;
  max-width: 120px;
  height: 40px;
  border-radius: 0px;
  margin-top: 10px;
  padding: 0px;
  text-align: center;
}

.blog-section-left {
  padding-right: 40px;
  padding-top: 5px;
}

.blog-video iframe {
  border: 0px;
  -webkit-box-shadow: none;
  box-shadow: none;
  width: 100%;
  min-height: 375px;
}

.blog-items {
  margin-bottom: 20px;
}

.blog-items .blog-content {
  background-color: white;
  -webkit-box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.07);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.07);
  padding: 15px 40px 15px;
}

.blog-items .blog-content h2 {
  font: 400 24px/30px "Poppins", sans-serif;
  color: #1e2d3c;
  padding-bottom: 15px;
}

.blog-items .blog-content h2:hover {
  color: #3fcef7;
}

.blog-items .blog-content p {
  color: #6f7982;
  padding: 5px 0px 25px;
}

.post-info {
  padding-top: 5px;
  border-top: 1px solid #f0f0f0;
}

.post-info li {
  font: 400 13px/60px "Poppins", sans-serif;
  color: #9a9a9a;
  display: inline-block;
  font-style: normal;
}

.post-info li a, .post-info li span {
  color: #6f7982;
  font-style: italic;
}

.post-info li a:hover, .post-info li span:hover {
  color: #3fcef7;
}

.post-info li + li {
  margin-left: 10px;
}

.post-info li + li:before {
  content: '';
  width: 1px;
  height: 15px;
  display: inline-block;
  background: #6f7982;
  vertical-align: middle;
  margin-right: 10px;
}

.blog-pagination li {
  display: inline-block;
  padding-right: 14px;
}

.blog-pagination li a {
  font: 400 15px/38px "Poppins", sans-serif;
  color: #6f7982;
}

.blog-pagination li a i {
  padding-left: 13px;
  vertical-align: middle;
}

.blog-pagination li a:hover {
  color: #1e2d3c;
}

.blog-pagination li.active a {
  color: #1e2d3c;
  font-weight: 500;
}

.right-sidebar .widget {
  margin-bottom: 50px;
}

.right-sidebar .widget .widget-title {
  font: 600 16px/18px "Poppins", sans-serif;
  color: #111111;
  letter-spacing: 0.80px;
  text-transform: uppercase;
  border-left: 3px solid #3fcef7;
  padding-left: 20px;
  margin-bottom: 35px;
}

.right-sidebar .widget-search .search-form {
  position: relative;
}

.right-sidebar .widget-search .search-form .form-control {
  font: 400 14px/40px "Poppins", sans-serif;
  color: #666666;
  width: 100%;
  border-radius: 0px;
  height: 50px;
  border: 1px solid #ececec;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.right-sidebar .widget-search .search-form button {
  font-size: 14px;
  color: #333333;
  border: 0px;
  background: transparent;
  padding: 0px 15px 0px 0px;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
}

.right-sidebar .widget-category ul li {
  border-bottom: 1px solid #ececec;
}

.right-sidebar .widget-category ul li a {
  font: 500 14px/18px "Poppins", sans-serif;
  padding-bottom: 15px;
  padding-left: 20px;
  position: relative;
  color: #333333;
  display: block;
}

.right-sidebar .widget-category ul li a:before {
  content: "\f105";
  font-family: 'FontAwesome';
  margin-right: 10px;
  position: absolute;
  left: 0;
}

.right-sidebar .widget-category ul li + li {
  margin-top: 15px;
}

.right-sidebar .widget-news .media {
  margin-top: 0px;
}

.right-sidebar .widget-news .media .media-left {
  padding-right: 15px;
}

.right-sidebar .widget-news .media .media-body h6 {
  font: 400 14px/20px "Poppins", sans-serif;
  color: #1e2d3c;
}

.right-sidebar .widget-news .media .media-body .post-time {
  font: 300 13px/30px "Poppins", sans-serif;
  color: #3fcef7;
}

.right-sidebar .widget-news .media + .media {
  margin-top: 30px;
}

.right-sidebar .widget-tags ul {
  overflow: hidden;
  margin: -5px -5px;
}

.right-sidebar .widget-tags ul li {
  float: left;
  margin: 5px;
}

.right-sidebar .widget-tags ul li a {
  font: 400 14px/24px "Poppins", sans-serif;
  color: #1e2d3c;
  text-transform: lowercase;
  display: inline-block;
  border: 1px solid #ececec;
  padding: 5px 10px;
}

.right-sidebar .widget-tags ul li a:hover {
  background: #3fcef7;
  border-color: #3fcef7;
  color: #fff;
}

.right-sidebar .widget-tweets .widget-title {
  margin-bottom: 28px;
}

.right-sidebar .widget-tweets .media {
  margin-top: 0px;
}

.right-sidebar .widget-tweets .media .media-left {
  font-size: 20px;
  color: #3fcef7;
  padding-right: 15px;
  padding-top: 5px;
}

.right-sidebar .widget-tweets .media .media-body {
  font: 300 13px/30px "Poppins", sans-serif;
}

.right-sidebar .widget-tweets .media .media-body p {
  color: #333333;
}

.right-sidebar .widget-tweets .media .media-body p a {
  color: #3fcef7;
}

.right-sidebar .widget-tweets .media .media-body .timestamp {
  color: #a8a8a8;
}

.right-sidebar .widget-tweets .media + .media {
  margin-top: 15px;
}

/*====================================================*/
/*====================================================*/
.footer-top {
  text-align: center;
  background: #1a2139;
}

.footer-top p {
  max-width: 796px;
  margin: 0 auto;
  font-size: 18px;
  line-height: 30px;
  letter-spacing: 0.36px;
  color: rgba(255, 255, 255, 0.5);
  padding: 45px 0px 73px;
}

.f-social li {
  display: inline-block;
  margin: 0px 15px;
}

.f-social li a {
  font-size: 24px;
  color: #8088a6;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.f-social li a:hover {
  color: #fff;
}

.footer_bottom {
  background: #161c31;
  font: 400 15px/21px "Poppins", sans-serif;
  letter-spacing: 0.3px;
  color: rgba(255, 255, 255, 0.3);
  padding: 30px 0px;
}

.footer_bottom a {
  color: #fff;
  opacity: 0.30;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
}

.footer_bottom a:hover {
  opacity: 1;
}

.footer_bottom .footer-menu {
  text-align: right;
}

.footer_bottom .footer-menu li {
  display: inline-block;
}

.footer_bottom .footer-menu li + li {
  margin-left: 38px;
}

.progress {
  width: 150px;
  height: 150px;
  line-height: 150px;
  background: none;
  margin: 0 auto;
  -webkit-box-shadow: none;
  box-shadow: none;
  position: relative;
}

.progress:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 12px solid #fff;
  position: absolute;
  top: 0;
  left: 0;
}

.progress > span {
  width: 50%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  z-index: 1;
}

.progress .progress-left {
  left: 0;
}

.progress .progress-bar {
  width: 100%;
  height: 100%;
  background: none;
  border-width: 12px;
  border-style: solid;
  position: absolute;
  top: 0;
}

.progress .progress-left .progress-bar {
  left: 100%;
  border-top-right-radius: 80px;
  border-bottom-right-radius: 80px;
  border-left: 0;
  -webkit-transform-origin: center left;
  -ms-transform-origin: center left;
  transform-origin: center left;
}

.progress .progress-right {
  right: 0;
}

.progress .progress-right .progress-bar {
  left: -100%;
  border-top-left-radius: 80px;
  border-bottom-left-radius: 80px;
  border-right: 0;
  -webkit-transform-origin: center right;
  -ms-transform-origin: center right;
  transform-origin: center right;
  -webkit-animation: loading-1 1.8s linear forwards;
  animation: loading-1 1.8s linear forwards;
}

.progress .progress-value {
  width: 90%;
  height: 90%;
  border-radius: 50%;
  background: #44484b;
  font-size: 24px;
  color: #fff;
  line-height: 135px;
  text-align: center;
  position: absolute;
  top: 5%;
  left: 5%;
}

.progress.blue .progress-bar {
  border-color: #049dff;
}

.progress.blue .progress-left .progress-bar {
  -webkit-animation: loading-2 1.5s linear forwards 1.8s;
  animation: loading-2 1.5s linear forwards 1.8s;
}

.progress.yellow .progress-bar {
  border-color: #fdba04;
}

.progress.yellow .progress-left .progress-bar {
  -webkit-animation: loading-3 1s linear forwards 1.8s;
  animation: loading-3 1s linear forwards 1.8s;
}

.progress.pink .progress-bar {
  border-color: #ed687c;
}

.progress.pink .progress-left .progress-bar {
  -webkit-animation: loading-4 0.4s linear forwards 1.8s;
  animation: loading-4 0.4s linear forwards 1.8s;
}

.progress.green .progress-bar {
  border-color: #1abc9c;
}

.progress.green .progress-left .progress-bar {
  -webkit-animation: loading-5 1.2s linear forwards 1.8s;
  animation: loading-5 1.2s linear forwards 1.8s;
}

@-webkit-keyframes loading-1 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

@keyframes loading-1 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}

@-webkit-keyframes loading-2 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(144deg);
    transform: rotate(144deg);
  }
}

@keyframes loading-2 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(144deg);
    transform: rotate(144deg);
  }
}

@-webkit-keyframes loading-3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@keyframes loading-3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

@-webkit-keyframes loading-4 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(36deg);
    transform: rotate(36deg);
  }
}

@keyframes loading-4 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(36deg);
    transform: rotate(36deg);
  }
}

@-webkit-keyframes loading-5 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(126deg);
    transform: rotate(126deg);
  }
}

@keyframes loading-5 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(126deg);
    transform: rotate(126deg);
  }
}

@media only screen and (max-width: 990px) {
  .progress {
    margin-bottom: 20px;
  }
}
@media screen and (max-width: 990px) {
  .blog_area {
    padding: 10px 0px;
  }
  .order_form {
    width: 100% !important;
    padding: 0 auto;
  }
  .contact_left {
    width: 100% !important;
    padding: 10px 5px;
    border-right: 1px dashed #eee;
    text-align: center;
  }
  .contact_right {
    width: 100% !important;
    padding: 10px 5px;
    border-right: 1px dashed #eee;
    text-align: center;
  }
  .contact_right p {
    font-size: 12px !important;
    width: 100%;
    text-align: left !important;
  }
  .contact_right p font {
    font-weight: bold;
    font-size: 14px !important;

  }
  /*T329模板*/
  .banner_pad {
    padding-top: 100px !important;
  }
  .first_f .features-content {
    padding-left: 0;
    text-align: center;
  }
  .first_f {
    padding-bottom: 20px !important;
  }
  .service_two_area {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
  .service_two_area .features-content {
    padding-right: 0 !important;
    text-align: center;
  }
  .subscribe_area_two{
    text-align: center;
  }
  .row.m0.footer_bottom {
    text-align: center;
  }
}
/*====================================================*/

/*附加CSS*/
.clear{
  clear: both;
}
.order_form {
  position: relative;
  top:auto;
  left: auto;
  margin: 0 auto;
  width: 920px;
  padding: 20px 20px 0 20px;
  border:1px solid #eee;
  background: #fff;
}
.main_box{
  width: calc(100% - 24px);
  padding: 12px 0;
  margin: 12px auto;
  border-bottom:1px dashed rgba(0,0,0,0.14);
  border-radius: 3px;
}
.main_box h3{
  font-weight: lighter;
  font-size: 14px;
  color: #7b7b7b;
  margin-bottom: 12px;
}
.main_box h3 i{
  font-weight: bold;
  font-size: 18px;
  vertical-align: middle;
  margin-right: 8px;
  color: #51cfe4;
}
.main_box p{
  float: left;
  font-size: 14px;
  line-height: 32px;
  color: #474747;
  overflow: hidden;
  text-overflow: ellipsis;
  /*white-space: nowrap;*/
  margin-right: 24px;
  width:100%;
}

#cardinfo0,#tips0
{
   display: block;

}
.main_box:last-child{
  border-bottom: none;
  margin-bottom: 24px;
}
.main_box li img{
  height: 30px;
}
.main_box p a{
  color: #409ccf;
  font-weight: lighter;
}
.main_box p a:hover{
  text-decoration: underline;
}
.contact_left {
  float: left;
  padding: 40px 80px 40px 40px;
  border-right: 1px dashed #eee;
  text-align: center;
}
.contact_right {
  float: right;
  width: 450px;
  /*padding: 50px;*/
  color: #33334f;
}
.contact_right p {
  line-height: 48px;
  font-size: 16px;
}
.contact_right p font {
  font-weight: bold;
  font-size: 24px;
}
.contact_right p i{
  font-size: 28px;
  margin-right: 10px;
  vertical-align: middle;
  color: #51cfe4;
}
.foot-auth {
  margin: 10px auto;
  text-align: center;
}

.foot-auth img {
  margin: 5px;
}
/*登录注册重构 知宇软件 夏宇*/
.user_form{
  margin: 120px auto 120px auto;
  width: 400px;
  padding: 40px 40px 0 40px;
  border:1px solid #eee;
  background: #fff;
  border-radius: 3px;
}
.user_input{
  height: 44px;
  line-height: 44px;
  padding: 0 12px;
  background: #fff;
  border-radius: 22px;
  border:2px solid #4ab1fe;
  margin-bottom: 36px;
}
.user_input i{
  font-size: 14px;
  color: rgba(118,126,173,0.1);
  margin-right: 12px;
}
.user_input input{
  width: calc(100% - 30px);
  text-align: center;
  border: none;
  background: none;
  color: #33334f;
  font-size: 16px;
}
.user_input input:focus{
  outline: none;
}
input::-webkit-input-placeholder {
  color: #aaa;
  letter-spacing:1px;
}
.user_form button{
  display: block;
  border: none;
  width: 100%;
  height: 44px;
  background: #4ab1fe;
  border-radius: 22px;
  box-shadow: 0 1px 4px rgba(118,126,173,0.3);
  margin-bottom: 12px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}
.user_form button:hover{
  background:#18d6ff;
}
.user_form a{
  display: block;
  margin-bottom: 36px;
  color: #648ff7;
  font-size: 14px;
}
.right_link{
  text-align: right;
}
.user_tab li{
  float: left;
  width: 50%;
  text-align: center;
  height: 44px;
  line-height: 44px;
  margin-bottom: 48px;
}
.user_tab li a{
  font-size: 18px;
  display: block;
  color: #33334f;
  border-bottom: 1px solid rgba(118,126,173,0.1);
}
.user_tab li a:hover{
  color: #648ff7;
}
.user_tab .actived a{
  color: #648ff7;
  border-bottom: 2px solid #4ab1fe;
}
.onError {
  padding: 3px;
  color: red;
  width: 100%;
  float: left;
  line-height: normal;
}
.onFocus {
  padding: 3px;
  color: red;
  width: 100%;
  float: left;
  line-height: normal;
}

 .search_box i{
   color: rgba(118,126,173,0.3);
   font-size: 20px;
   margin-left: 16px;
 }
.search_box input:focus{
  outline: none;
}

.search_box button:hover{
  background: rgba(100,143,247,1);
}
.main_box{
  width: calc(100% - 24px);
  padding: 12px;
  margin: 12px auto;
  border-bottom:1px dashed rgba(0,0,0,0.14);
  border-radius: 3px;
}
.main_box h3{
  font-weight: lighter;
  font-size: 14px;
  color: #767ead;
  margin-bottom: 12px;
}
.main_box h3 i{
  font-size: 16px;
  /* vertical-align: text-bottom; */
  margin-right: 8px;
  color: rgba(100,143,247,1);
}
.main_box p{
  float: left;
  font-size: 14px;
  line-height: 32px;
  color: #33334f;
  /*overflow: hidden;*/
  /*text-overflow: ellipsis;*/
  /*white-space: nowrap;*/
  margin-right: 24px;
}
.main_box:last-child{
  border-bottom: none;
  margin-bottom: 24px;
}
.main_box li img{
  height: 30px;
}
.main_box p a{
  color: rgba(100,143,247,1);
  font-weight: lighter;
}
.main_box p a:hover{
  text-decoration: underline;
}

@media only screen and (min-width: 768px) {
  .order_form {
    margin: 80px auto 100px auto;
    width: 1120px;
    min-height: 300px;
    padding: 20px 15px 20px 15px;
    border-radius: 3px;
    border: 1px solid #eee;
    box-shadow: 0 12px 24px -10px #eee;
    background: #fff;
  }
  .search_box input{
    border: none;
    font-size: 16px;
    margin: 0 12px;
    width: calc(100% - 180px);
    text-align: center;
    color: #33334f;
    height: 20px;
    line-height: 20px;
    display:inline-block;
  }
  .search_box{
    position: relative;
    height: 60px;
    line-height: 60px;
    width: 100%;
    margin: -60px auto 0 auto;
    background: #fff;
    border-radius: 31px;
    color: #33334f;
    border: 1px solid rgba(118,126,173,0.3);
    transition: .2s;
  }
  .search_box button{
    display: block;
    position: absolute;
    right: 10px;
    top:10px;
    height: 38px;
    line-height: 38px;
    width: 70px;
    background: rgba(100,143,247,0.9);
    border: none;
    border-radius: 10px;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
  }
}
@media only screen and (max-width: 768px) {
  .order_form {
    margin: 50px auto 0px auto;
    min-height: 300px;
    padding: 40px 40px 20px 40px;
    border-radius: 3px;
    border: 1px solid #eee;
    box-shadow: 0 12px 24px -10px #eee;
    background: #fff;
  }
  .search_box input {
    border: none;
    font-size: 14px;
    text-align: left;
    color: #33334f;
    height: 20px;
    line-height: 20px;
    width:200px;
  }
  .search_box{
    position: relative;
    height: 40px;
    line-height: 40px;
    width: 100%;
    margin: -60px auto 0 auto;
    background: #fff;
    border-radius: 31px;
    color: #33334f;
    border: 1px solid rgba(118,126,173,0.3);
    transition: .2s;
  }
  .search_box button {
    display: block;
    position: absolute;
    right: 0px;
    top: 0px;
    height: 40px;
    line-height: 40px;
    width: 100px;
    background: rgba(100,143,247,0.9);
    border: none;
    border-radius: 20px;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
  }
}

/*手机适配修改*/
@media screen and  (max-width: 768px) {
  .header {
    padding: 0 !important;
  }
  .navbar-default .navbar-brand {
    margin-top: 15px;
    text-align: left;
  }
  .hero_text {
    padding-top: 34px;
    padding-right: 0;
  }
  .hero_text .hero_title {
    font-size: 38px;
    line-height: 38px;
  }
  .service_item{
    padding: 15px 5px;
  }
  .footer_bottom .right {
    display: none;
  }
  .navbar-brand img {
    max-width: 100%;
    height: auto;
  }
}


