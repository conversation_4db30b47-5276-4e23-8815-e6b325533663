    //动画
	var ani=new WOW().init();
	//tab切换
	var homeNewsTab = new Swiper('.swiper-news',{
		navigation: {
			nextEl: '.arrow-next',
			prevEl: '.arrow-prev',
		},
		on: {
			slideChange: function(){
				$(".tab-title li:eq("+this.activeIndex+")").addClass("on").siblings("li").removeClass("on");
			},		
		},
	});
	$('.tab-title li').on('click', function(e) {
		e.preventDefault();
		// 获取当前索引
		var index = $(this).index();
		$(this).addClass("on").siblings("dl").removeClass("on");
		homeNewsTab.slideTo(index, 0, false);
	});	

