{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-modal="{:url('add')}" data-title="添加标签" class='layui-btn layui-btn-small'> 添加标签 </button>
    <button data-load="{:url('sync')}" class='layui-btn layui-btn-small'> 同步标签 </button>
</div>
{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="animated form-search" action="__SELF__" onsubmit="return false" method="get">

    <div class="row">

        <div class="col-xs-3">
            <div class="form-group">
                <input type="text" name="name" value="{$Think.get.name|default=''}" placeholder="标签" class="input-sm form-control">
            </div>
        </div>

        <div class="col-xs-1">
            <div class="form-group">
                <button type="submit" class="btn btn-sm btn-white"><i class="fa fa-search"></i> 搜索</button>
            </div>
        </div>

    </div>

</form>
<!-- 表单搜索 结束 -->

<form onsubmit="return false;" data-auto="" method="POST">
    <input type="hidden" value="resort" name="action"/>
    <table class="table table-hover">
        <thead>
            <tr>
                <th class='list-table-check-td'>
                    <input data-none-auto="" data-check-target='.list-check-box' type='checkbox'/>
                </th>
                <th class='text-center'>ID</th>
                <th class='text-center'>标签</th>
                <th class='text-center'>类型</th>
                <th class='text-center'>粉丝数</th>
                <th class='text-center'>操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $key=>$vo}
            <tr>
                <td class='list-table-check-td'>
                    <input class="list-check-box" value='{$vo.id}' type='checkbox'/>
                </td>
                <td class='text-center'>{$vo.id|default='0'}</td>
                <td class='text-center'>{$vo.name|default=''}</td>
                <td class='text-center'>{$vo.id < 100 ? "系统标签" : "自定义标签"}</td>
                <td class='text-center'>{$vo.count|default=''}</td>
                <td class='text-center nowrap'>

                    {if auth("$classuri/edit")}
                    {if $vo.id >= 100}
                    <span class="text-explode">|</span>
                    <a data-modal='{:url("$classuri/edit")}?id={$vo.id}' data-title="编辑标签" href="javascript:void(0)">编辑</a>
                    {else}
                    <a href="javascript:void(0)" style="color:#999">编辑</a>
                    {/if}
                    {/if}

                    {if auth("$classuri/del")}
                    <span class="text-explode">|</span>
                    {if $vo.id >= 100}
                    <a data-update="{$vo.id}" data-field='delete' data-action='{:url("$classuri/del")}' href="javascript:void(0)">删除</a>
                    {else}
                    <a href="javascript:void(0)" style="color:#999">删除</a>
                    {/if}
                    {/if}

                </td>
            </tr>
            {/foreach}
            {if empty($list)}
            <tr><td colspan="6" style="text-align:center">没 有 记 录 了 哦 !</td></tr>
            {/if}
        </tbody>
    </table>
    {if isset($page)}<p>{$page}</p>{/if}
</form>
{/block}