{extend name="base"}

{block name="css"}

{/block}
{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-auto">
                                <form class="form-inline" role="form" action="" method="get">
                                    <div class="form-group ml-1">
                                        <input name="username" type="text" class="form-control" placeholder="代理用户名" value="{$Think.get.username|default=''|htmlentities}">
                                    </div>
                                    <button type="submit" class="btn btn-light waves-effect waves-light ml-1"><i class="bx bx-search mr-1"></i>搜索</button>
                                </form>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>代理ID</th>
                                        <th>用户名</th>
                                        <th>客服QQ</th>
                                        <th>店铺名</th>
                                        <th>申请时间</th>
                                        <th>已对接数量</th>
                                        <th class="text-center">今日出卡量</th>
                                        <th class="text-center">累计出卡量</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach $proxylist as $v}

                                    <tr>
                                        <th>{$v.child_user_id}</th>
                                        <th>{$v.child.username}</th>
                                        <th><a href="//wpa.qq.com/msgrd?v=3&uin={$v.child.qq}&Site=" target="_blank"><i class="iconfont icon-qq-white"></i>{$v.child.qq}</a></th>
                                        <th>{$v.child.shop_name|default='未设置'}</th>
                                        <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                                        <td>{$v.goodscount}个</td>
                                        <td class="text-center">{$v.day_card_count}张</td>
                                        <td class="text-center">{$v.all_card_count}张</td>
                                        <td>
                                            {switch name="v.status"}
                                            {case value="-1"}<span class="badge badge-pill badge-danger">已拒绝</span>{/case}
                                            {case value="0"}<span class="badge badge-pill badge-primary">申请中</span>{/case}
                                            {case value="1"}<span class="badge badge-pill badge-success">正常</span>{/case}
                                            {/switch}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm " role="group" aria-label="Basic group">

                                                {switch name="v.status"}
                                                {case value="-1"}<a class="btn btn-light waves-effect waves-light text-success" href="javascript:void(0);" onclick="verify(this, '{$v.id}', 1, '确定要变更同意？')">变更同意</a>{/case}
                                                {case value="0"}
                                                <a class="btn btn-light waves-effect waves-light text-success" href="javascript:void(0);" onclick="verify(this, '{$v.id}', 1, '确定要同意申请？')">同意</a>
                                                <a class="btn btn-light waves-effect waves-light text-danger" href="javascript:void(0);" onclick="verify(this, '{$v.id}', -1, '确定要拒绝申请？')">拒绝</a>
                                                {/case}
                                                {case value="1"}<a class="btn btn-light waves-effect waves-light text-danger" href="javascript:void(0);" onclick="verify(this, '{$v.id}', -1, '请谨慎操作！代理已对接的商品将会下架，确定要变更拒绝？')">变更拒绝</a>{/case}
                                                {/switch}

                                                <a class="btn btn-light waves-effect waves-light text-success" href="{:url('agent/proxyGoods',['id'=>$v.id])}" >对接商品管理</a>
                                            </div>

                                        </td>
                                    </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="...">
                            {$page}
                        </nav>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}
<script>

    function verify(obj, id, status, msg)
    {
        $.confirm({
            title: '温馨提示',
            content: msg,
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('Agent/verifyProxy')}", {
                            id: id,
                            status: status
                        }, function (res) {
                            layer.close(loading);
                            if (res.code == 1) {
                                $.alert(res.msg);
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            } else {
                                $.alert(res.msg);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }
</script>
{/block}
