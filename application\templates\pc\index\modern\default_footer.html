<!-- Modern Footer Component -->
<footer class="bg-gray-900 text-white">
    <!-- Main Footer Content -->
    <div class="container py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div class="lg:col-span-2">
                <div class="mb-6">
                    <img src="{:sysconf('site_logo')}" alt="{:sysconf('site_name')}" class="h-8 w-auto mb-4">
                    <h3 class="text-xl font-semibold mb-3">{:sysconf('site_name')}</h3>
                    <p class="text-gray-400 leading-relaxed">
                        专业的虚拟商品自动发卡平台，为商户提供安全、稳定、高效的交易服务。
                        我们致力于打造最优质的数字商品交易生态。
                    </p>
                </div>
                
                <!-- Social Links -->
                <div class="flex gap-4">
                    <a href="#" class="social-link" data-tooltip="微信公众号">
                        <i data-lucide="message-circle" class="w-5 h-5"></i>
                    </a>
                    <a href="#" class="social-link" data-tooltip="QQ群">
                        <i data-lucide="users" class="w-5 h-5"></i>
                    </a>
                    <a href="#" class="social-link" data-tooltip="客服邮箱">
                        <i data-lucide="mail" class="w-5 h-5"></i>
                    </a>
                    <a href="#" class="social-link" data-tooltip="官方微博">
                        <i data-lucide="share-2" class="w-5 h-5"></i>
                    </a>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div>
                <h4 class="text-lg font-semibold mb-6">快速链接</h4>
                <ul class="space-y-3">
                    <li><a href="/" class="footer-link">网站首页</a></li>
                    <li><a href="/orderquery" class="footer-link">卡密查询</a></li>
                    <li><a href="/complaint" class="footer-link">订单投诉</a></li>
                    <li><a href="/complaintquery" class="footer-link">投诉进度</a></li>
                    <li><a href="/company/contact" class="footer-link">联系我们</a></li>
                    <li><a href="/company/faq" class="footer-link">帮助中心</a></li>
                </ul>
            </div>
            
            <!-- Services -->
            <div>
                <h4 class="text-lg font-semibold mb-6">服务支持</h4>
                <ul class="space-y-3">
                    <li><a href="/register" class="footer-link">商户入驻</a></li>
                    <li><a href="/login" class="footer-link">商户登录</a></li>
                    <li><a href="/api/doc" class="footer-link">API文档</a></li>
                    <li><a href="/company/terms" class="footer-link">服务条款</a></li>
                    <li><a href="/company/privacy" class="footer-link">隐私政策</a></li>
                    <li><a href="/company/about" class="footer-link">关于我们</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Stats Section -->
        <div class="border-t border-gray-800 mt-12 pt-12">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="stat-item">
                    <div class="text-3xl font-bold text-primary-400 mb-2" data-counter="151825">0</div>
                    <div class="text-gray-400">成功寄售</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-primary-400 mb-2" data-counter="8111">0</div>
                    <div class="text-gray-400">服务商户</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-primary-400 mb-2" data-counter="2">0</div>
                    <div class="text-gray-400">年稳健运营</div>
                </div>
                <div class="stat-item">
                    <div class="text-3xl font-bold text-primary-400 mb-2">99.9%</div>
                    <div class="text-gray-400">服务可用性</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bottom Bar -->
    <div class="border-t border-gray-800">
        <div class="container py-6">
            <div class="flex flex-col md:flex-row items-center justify-between gap-4">
                <div class="text-gray-400 text-sm">
                    <p>&copy; 2024 {:sysconf('site_name')}. 保留所有权利.</p>
                </div>
                
                <div class="flex items-center gap-6 text-sm text-gray-400">
                    <span>ICP备案号: 渝ICP备xxxxxxxx号</span>
                    <span>|</span>
                    <span>增值电信业务经营许可证</span>
                </div>
                
                <!-- Back to Top Button -->
                <button id="backToTop" class="back-to-top" data-tooltip="返回顶部">
                    <i data-lucide="arrow-up" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
    </div>
</footer>

<!-- Cookie Notice (if needed) -->
<div id="cookieNotice" class="cookie-notice">
    <div class="container">
        <div class="flex items-center justify-between gap-4 py-4">
            <div class="flex-1">
                <p class="text-sm text-gray-600">
                    我们使用Cookie来改善您的浏览体验。继续使用本网站即表示您同意我们的
                    <a href="/company/privacy" class="text-primary-600 hover:underline">隐私政策</a>。
                </p>
            </div>
            <div class="flex gap-2">
                <button id="acceptCookies" class="btn btn-primary btn-sm">接受</button>
                <button id="declineCookies" class="btn btn-secondary btn-sm">拒绝</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Footer Styles */
.footer-link {
    color: #9ca3af;
    transition: color 0.2s ease;
    display: block;
}

.footer-link:hover {
    color: #60a5fa;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #9ca3af;
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-4px);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 48px;
    height: 48px;
    background-color: var(--primary-600);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 40;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Cookie Notice */
.cookie-notice {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid var(--gray-200);
    box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.1);
    z-index: 50;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-notice.show {
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
    }
    
    .cookie-notice .flex {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .cookie-notice .flex > div:last-child {
        display: flex;
        gap: 0.5rem;
    }
    
    .cookie-notice .btn {
        flex: 1;
    }
}

/* Dark mode styles */
.dark .cookie-notice {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
}

.dark .cookie-notice p {
    color: var(--gray-300);
}
</style>

<script>
// Back to Top functionality
document.addEventListener('DOMContentLoaded', function() {
    const backToTopBtn = document.getElementById('backToTop');
    
    // Show/hide back to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    // Smooth scroll to top
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Cookie notice functionality
    const cookieNotice = document.getElementById('cookieNotice');
    const acceptBtn = document.getElementById('acceptCookies');
    const declineBtn = document.getElementById('declineCookies');
    
    // Check if user has already made a choice
    if (!localStorage.getItem('cookieChoice')) {
        setTimeout(() => {
            cookieNotice.classList.add('show');
        }, 2000);
    }
    
    acceptBtn.addEventListener('click', function() {
        localStorage.setItem('cookieChoice', 'accepted');
        cookieNotice.classList.remove('show');
    });
    
    declineBtn.addEventListener('click', function() {
        localStorage.setItem('cookieChoice', 'declined');
        cookieNotice.classList.remove('show');
    });
});
</script>
