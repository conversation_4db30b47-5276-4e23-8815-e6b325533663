<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="theme-color" content="#1E78FF">	
        <meta name="msapplication-navbutton-color" content="#1E78FF">		
        <meta name="apple-mobile-web-app-status-bar-style" content="#1E78FF">
        <link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/style.css" media="all">		
        <link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/responsive.css" media="all">	
    </head>

    <body>
        <div class="main-page-wrapper">
            <!-- ===================================================
                    Loading Transition
            ==================================================== -->
            {include file="./default_her"}


            <!-- 
            =============================================
                    Search
            ============================================== 
            -->
            <div class="offcanvas offcanvas-top theme-search-form bg-three justify-content-center" tabindex="-1" id="offcanvasTop">
                <button type="button" class="close-btn tran3s" data-bs-dismiss="offcanvas" aria-label="Close"><i class="bi bi-x-lg"></i></button>
                <div class="form-wrapper">
                    <form action="#">
                        <input type="text" placeholder="Search Keyword....">
                    </form>
                </div> <!-- /.form-wrapper -->
            </div>


            <!-- 
            =============================================
                    Theme Main Menu
            ============================================== 
            -->
            <header class="theme-main-menu sticky-menu theme-menu-four">
                {include file="./default_header"}
            </header> <!-- /.theme-main-menu -->



            <!-- 
            =============================================
                    Theme Inner Banner
            ============================================== 
            -->
            <div class="theme-inner-banner">
                <div class="container">
                    <h2 class="intro-title text-center">{$data.title} </h2>
                    <ul class="page-breadcrumb style-none d-flex justify-content-center">
                        <li><a href="index.html">Home</a></li>
                        <li class="current-page">{$data.title} </li>
                    </ul>
                </div>
                <img src="__RES__/theme/maowang51/picture/shape_38.svg" alt="" class="shapes shape-one">
                <img src="__RES__/theme/maowang51/picture/shape_39.svg" alt="" class="shapes shape-two">
            </div> <!-- /.theme-inner-banner -->



            <!--
            =====================================================
                    Blog Details
            =====================================================
            -->
            <div class="blog-details pt-90 mb-150 lg-pt-40 lg-mb-100">
                <div class="container">
                    <div class="row">
                        <div class="col-xxl-11 m-auto">
                            <div class="row">
                                <div class="col-lg">
                                    {$data.content|htmlspecialchars_decode|removeXSS}


                                </div>

                            </div>
                        </div>
                    </div>
                </div> <!-- /.container -->
            </div> <!-- /.blog-details -->



            <!-- 
            =============================================
                    Fancy Short Banner Five
            ============================================== 
            -->
            <div class="fancy-short-banner-five">
                <div class="container">
                    <div class="bg-wrapper">
                        <div class="row align-items-center">
                            <div class="col-lg-6 text-center text-lg-start" data-aos="fade-right">
                                <h3 class="pe-xxl-5 md-pb-20">欢迎加入哦我们</h3>
                            </div>
                            <div class="col-lg-6 text-center text-lg-end" data-aos="fade-left">
                                <a href="/login" class="msg-btn tran3s">加入我们</a>
                            </div>
                        </div>
                    </div> <!-- /.bg-wrapper -->
                </div> <!-- /.container -->
            </div> <!-- /.fancy-short-banner-five -->



            <!--
            =====================================================
                    Footer
            =====================================================
            -->
            <div class="footer-style-four space-fix-one theme-basic-footer">
                <div class="container">
                    <div class="inner-wrapper">
                        {include file="./default_footer"}

                    </div> <!-- /.inner-wrapper -->
                </div>
            </div> <!-- /.footer-style-four -->


            <button class="scroll-top">
                <i class="bi bi-arrow-up-short"></i>
            </button>




            <!-- Optional JavaScript _____________________________  -->

            <!-- jQuery first, then Bootstrap JS -->
            <!-- jQuery -->
            <script src="__RES__/theme/maowang51/js/jquery.min.js"></script>
            <!-- Bootstrap JS -->
            <script src="__RES__/theme/maowang51/js/bootstrap.bundle.min.js"></script>
            <!-- AOS js -->
            <script src="__RES__/theme/maowang51/js/aos.js"></script>
            <!-- Slick Slider -->
            <script src="__RES__/theme/maowang51/js/slick.min.js"></script>
            <!-- js Counter -->
            <script src="__RES__/theme/maowang51/js/jquery.counterup.min.js"></script>
            <script src="__RES__/theme/maowang51/js/jquery.waypoints.min.js"></script>
            <!-- Fancybox -->
            <script src="__RES__/theme/maowang51/js/jquery.fancybox.min.js"></script>
            <!-- isotop -->
            <script src="__RES__/theme/maowang51/js/isotope.pkgd.min.js"></script>

            <!-- Theme js -->
            <script src="__RES__/theme/maowang51/js/theme.js"></script>
        </div> <!-- /.main-page-wrapper -->
    </body>
</html>