{extend name="base"}

{block name="content"}

<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">

                <div class="card">
                    <div class="card-body">
                        <h2>网关地址</h2>
                        <p>URL地址：<code>{:sysconf("site_domain")}submit.php</code></p>
                        <h2>发起支付请求 FORM跳转方式（与易支付相同）</h2>
                        <p>URL地址：<code>网关地址 + ?pid={商户ID}&amp;type={支付方式}&amp;out_trade_no={商户订单号}&amp;notify_url={服务器异步通知地址}&amp;return_url={页面跳转通知地址}&amp;name={商品名称}&amp;money={金额}&amp;sitename={网站名称}&amp;sign={签名字符串}&amp;sign_type=MD5</code></p>
                        <p>请求参数说明：</p>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead><tr><th>字段名</th><th>变量名</th><th>必填</th><th>类型</th><th>示例值</th><th>描述</th></tr></thead>
                                <tbody>
                                    <tr><td>商户ID</td><td>pid</td><td>是</td><td>Int</td><td>10001</td><td></td></tr>
                                    <tr><td>支付方式</td><td>type</td><td>是</td><td>String</td><td>alipay</td><td>alipay:支付宝,wxpay:微信支付,<br>qqpay:QQ钱包</td></tr>
                                    <tr><td>商户订单号</td><td>out_trade_no</td><td>是</td><td>String</td><td>20160806151343349</td><td></td></tr>
                                    <tr><td>异步通知地址</td><td>notify_url</td><td>是</td><td>String</td><td>http://域名/notify_url.php</td><td>服务器异步通知地址</td></tr>
                                    <tr><td>跳转通知地址</td><td>return_url</td><td>是</td><td>String</td><td>http://域名/return_url.php</td><td>页面跳转通知地址</td></tr>
                                    <tr><td>商品名称</td><td>name</td><td>是</td><td>String</td><td>VIP会员</td><td></td></tr>
                                    <tr><td>商品金额</td><td>money</td><td>是</td><td>String</td><td>1.00</td><td></td></tr>
                                    <tr><td>网站名称</td><td>sitename</td><td>是</td><td>String</td><td>某某某平台</td><td></td></tr>
                                    <tr><td>签名字符串</td><td>sign</td><td>是</td><td>String</td><td>202cb962ac59075b964b07152d234b70</td><td>签名算法与<a href="http://doc.open.alipay.com/docs/doc.htm?treeId=62&amp;articleId=104741&amp;docType=1" target="_blank">支付宝签名算法</a>相同</td></tr>
                                    <tr><td>签名类型</td><td>sign_type</td><td>是</td><td>String</td><td>MD5</td><td>默认为MD5【不参与签名】</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <h2>发起支付请求 POST请求返回JSON方式</h2>
                        <p>URL地址：<code>{:sysconf("site_domain")}submit.php</code></p>
                        <p>请求参数说明：</p>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead><tr><th>字段名</th><th>变量名</th><th>必填</th><th>类型</th><th>示例值</th><th>描述</th></tr></thead>
                                <tbody>
                                    <tr><td>商户ID</td><td>pid</td><td>是</td><td>Int</td><td>10001</td><td></td></tr>
                                    <tr><td>支付方式</td><td>type</td><td>是</td><td>String</td><td>alipay</td><td>alipay:支付宝,wxpay:微信支付,<br>qqpay:QQ钱包</td></tr>
                                    <tr><td>商户订单号</td><td>out_trade_no</td><td>是</td><td>String</td><td>20160806151343349</td><td></td></tr>
                                    <tr><td>异步通知地址</td><td>notify_url</td><td>是</td><td>String</td><td>http://域名/notify_url.php</td><td>服务器异步通知地址</td></tr>
                                    <tr><td>跳转通知地址</td><td>return_url</td><td>是</td><td>String</td><td>http://域名/return_url.php</td><td>页面跳转通知地址</td></tr>
                                    <tr><td>商品名称</td><td>name</td><td>是</td><td>String</td><td>VIP会员</td><td></td></tr>
                                    <tr><td>商品金额</td><td>money</td><td>是</td><td>String</td><td>1.00</td><td></td></tr>
                                    <tr><td>网站名称</td><td>sitename</td><td>是</td><td>String</td><td>某某某平台</td><td></td></tr>
                                    <tr><td>签名字符串</td><td>sign</td><td>是</td><td>String</td><td>202cb962ac59075b964b07152d234b70</td><td>签名算法与<a href="http://doc.open.alipay.com/docs/doc.htm?treeId=62&amp;articleId=104741&amp;docType=1" target="_blank">支付宝签名算法</a>相同</td></tr>
                                    <tr><td>签名类型</td><td>sign_type</td><td>是</td><td>String</td><td>MD5</td><td>默认为MD5【不参与签名】</td></tr>
                                    <tr><td>返回方式</td><td>return_type</td><td>是</td><td>String</td><td>json</td><td>填json则返回json格式,不填或其他走FORM跳转方式【不参与签名】</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <p>返回参数说明：</p>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead><tr><th>字段名</th><th>变量名</th><th>实例</th></tr></thead>
                                <tbody>
                                    <tr><td>状态码</td><td>code</td><td>1</td></tr>
                                    <tr><td>说明</td><td>msg</td><td>success</td></tr>
                                    <tr><td>返回数据</td><td>data</td><td></td></tr>
                                    <tr><td>支付链接</td><td>data<br>&nbsp;&nbsp;-pay_url</td><td>http://xxx.com/index/pay/payment?trade_no=xxx</td></tr>
                                </tbody>
                            </table>
                        </div>

                        <h2>支付结果通知</h2>
                        <p>通知类型：服务器异步通知（notify_url）、页面跳转通知（return_url）</p>
                        <p>请求方式：<code>GET</code></p>
                        <p>特别说明：回调成功之后请输出 <code>SUCCESS</code>字符串，如果没有收到商户响应的<code>SUCCESS</code>字符串，系统将通过策略重新通知5次，通知频率为1分钟一次</p>
                        <p>请求参数说明：</p>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover ">
                                <thead><tr><th>字段名</th><th>变量名</th><th>必填</th><th>类型</th><th>示例值</th><th>描述</th></tr></thead>
                                <tbody>
                                    <tr><td>商户ID</td><td>pid</td><td>是</td><td>Int</td><td>1001</td><td></td></tr>
                                    <tr><td>易支付订单号</td><td>trade_no</td><td>是</td><td>String</td><td>20160806151343349021</td><td>TK易支付订单号</td></tr>
                                    <tr><td>商户订单号</td><td>out_trade_no</td><td>是</td><td>String</td><td>20160806151343349</td><td>商户系统内部的订单号</td></tr>
                                    <tr><td>支付方式</td><td>type</td><td>是</td><td>String</td><td>alipay</td><td>alipay:支付宝,tenpay:财付通,<br>qqpay:QQ钱包,wxpay:微信支付</td></tr>
                                    <tr><td>商品名称</td><td>name</td><td>是</td><td>String</td><td>VIP会员</td><td></td></tr>
                                    <tr><td>商品金额</td><td>money</td><td>是</td><td>String</td><td>1.00</td><td></td></tr>
                                    <tr><td>支付状态</td><td>trade_status</td><td>是</td><td>String</td><td>TRADE_SUCCESS</td><td></td></tr>
                                    <tr><td>签名字符串</td><td>sign</td><td>是</td><td>String</td><td>202cb962ac59075b964b07152d234b70</td><td>签名算法与<a href="http://doc.open.alipay.com/docs/doc.htm?treeId=62&amp;articleId=104741&amp;docType=1" target="_blank">支付宝签名算法</a>相同</td></tr>
                                    <tr><td>签名类型</td><td>sign_type</td><td>是</td><td>String</td><td>MD5</td><td>默认为MD5</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <h2 id="md5">签名算法</h2>
                        <p>
                            请对参数按照键名进行降序排序（a-z）sign sign_type 和空值不进行签名！。
                            排序后请操作参数生成或拼接一个url请求字符串 例如 <code>a=b&c=d&e=f</code> (Url值不能携带参数！不要进行urlencode)
                            再将拼接好的请求字符串与平台生成的Key进行MD5加密得出sign签名参数  <code>MD5 ( a=b&c=d&e=f + KEY )</code> (注意：+ 为各语言的拼接符！不是字符！)
                        </p>
                        <hr>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- container-fluid -->
</div>
<!-- End Page-content -->


{/block}
{block name="js"}
<script>



</script>
{/block}
