<?php

namespace app\common\model;

use think\Db;
use think\Model;

class UserAgentDepositOrder extends Model {

    public function channel() {
        return $this->belongsTo('Channel', 'channel_id');
    }

    public function channelAccount() {
        return $this->belongsTo('ChannelAccount', 'channel_account_id');
    }

    public function user() {
        return $this->belongsTo('User', 'user_id');
    }

    public function completeOrder($service, &$order) {
        //判断订单是否已支付
        if ($order->status == 1) {
            return A(0, "当前订单已为已支付状态");
        }

        Db::startTrans();
        try {
            $time = time();
            // 完成订单
            $res = Db::name('user_agent_deposit_order')->where(['id' => $order->id, 'status' => 0])->update(['status' => 1, 'success_at' => $time]);
            if (!$res) {
                Db::rollback();
                return A(0, "不存在当前订单");
            }
            $order->save(); //用于保存流水号
            $total_price = $order->total_price;
            if ($total_price > 0) {
                if ($order->types == "add") {
                    Db::name('user')->where(['id' => $order->user_id])->update(['agentdeposit_money' => Db::raw('agentdeposit_money+' . $total_price)]);
                } elseif ($order->types == "return") {
                    Db::name('user')->where(['id' => $order->user_id])->update(['agentdeposit_money' => Db::raw('agentdeposit_money-' . $total_price), 'money' => Db::raw('money+' . $total_price)]);
                    record_user_money_log("agentdeposit_return", $order->user_id, $total_price, $order->user->money, "保证金退还");
                }
            }


            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            // 记录错误订单
            return A(0, $order->trade_no . $e->getMessage());
        }
        return A(1, "订单设置支付成功");
    }

    public function callbackUrl($service, &$order) {
        return url('merchant/agent/setting');
    }

}
