$(function () {
    var client_box = null;
    $('#open_client, .open_client').click(function (e) {
        var user_id = $(this).data('user');
        if(client_box != null) return false;
        $.ajax({
            url: '/api/v2/check_login',
            type: 'post',
            data: {},
            success: function (status) {
                if(!status) {
                    if(confirm('你还没有登录，是否立即登录？')) {
                        window.location.href = '/member/login?back_url=' + location.href;
                    }
                } else {
                    if(IsPC()) {
                        var w = $(window).width();
                        var h = $(window).height();
                        h = w > 1000 ? '600px' : (h + 'px');
                        w = w > 1000 ? '1000px' : (w + 'px');
                        client_box = layer.open({
                            title: false,
                            type: 2,
                            area: [w, h],
                            fixed: false, //不固定
                            scrollbar: false,
                            content: '/member/do_socket?user_id=' + user_id,
                            cancel: function(){
                                client_box = null;
                            }
                        })
                    } else {
                        window.location.href = '/member/do_socket?user_id=' + user_id + '&back_url=' + location.href;
                    }
                }
            },
            error: function () {
                alert('网络异常');
            }
        })
        e.stopPropagation();
        return false;
    })
});

function IsPC() {
    var userAgentInfo = navigator.userAgent;
    var Agents = ["Android", "iPhone",
        "SymbianOS", "Windows Phone",
        "iPad", "iPod"];
    var flag = true;
    for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = false;
            break;
        }
    }
    return flag;
}