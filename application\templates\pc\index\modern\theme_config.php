<?php
/**
 * Modern Theme Configuration
 * 现代化主题配置文件
 */

return [
    // 主题基本信息
    'theme_info' => [
        'name' => 'Modern Theme',
        'version' => '1.0.0',
        'author' => 'AI Assistant',
        'description' => '现代化设计的发卡平台主题，支持响应式布局和深色模式',
        'preview' => '__RES__/theme/modern/images/preview.jpg',
        'tags' => ['现代化', '响应式', '深色模式', '移动优先'],
    ],

    // 主题设置
    'theme_settings' => [
        // 颜色配置
        'colors' => [
            'primary' => '#3b82f6',
            'secondary' => '#6b7280',
            'success' => '#10b981',
            'warning' => '#f59e0b',
            'error' => '#ef4444',
            'background' => '#f9fafb',
            'surface' => '#ffffff',
            'text_primary' => '#111827',
            'text_secondary' => '#6b7280',
        ],

        // 字体配置
        'typography' => [
            'font_family' => 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            'font_sizes' => [
                'xs' => '0.75rem',
                'sm' => '0.875rem',
                'base' => '1rem',
                'lg' => '1.125rem',
                'xl' => '1.25rem',
                '2xl' => '1.5rem',
                '3xl' => '1.875rem',
                '4xl' => '2.25rem',
            ],
            'font_weights' => [
                'normal' => 400,
                'medium' => 500,
                'semibold' => 600,
                'bold' => 700,
            ],
        ],

        // 间距配置
        'spacing' => [
            '1' => '0.25rem',
            '2' => '0.5rem',
            '3' => '0.75rem',
            '4' => '1rem',
            '5' => '1.25rem',
            '6' => '1.5rem',
            '8' => '2rem',
            '10' => '2.5rem',
            '12' => '3rem',
            '16' => '4rem',
            '20' => '5rem',
        ],

        // 圆角配置
        'border_radius' => [
            'sm' => '0.375rem',
            'md' => '0.5rem',
            'lg' => '0.75rem',
            'xl' => '1rem',
            '2xl' => '1.5rem',
            'full' => '9999px',
        ],

        // 阴影配置
        'shadows' => [
            'sm' => '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            'md' => '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
            'lg' => '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
            'xl' => '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        ],

        // 动画配置
        'animations' => [
            'duration_fast' => '150ms',
            'duration_normal' => '250ms',
            'duration_slow' => '350ms',
            'easing' => 'ease-in-out',
        ],
    ],

    // 布局配置
    'layout' => [
        // 容器最大宽度
        'container_max_width' => '1200px',
        
        // 响应式断点
        'breakpoints' => [
            'sm' => '640px',
            'md' => '768px',
            'lg' => '1024px',
            'xl' => '1280px',
            '2xl' => '1536px',
        ],

        // 网格配置
        'grid' => [
            'columns' => 12,
            'gap' => '1.5rem',
        ],

        // 导航栏配置
        'navbar' => [
            'height' => '4rem',
            'sticky' => true,
            'blur_background' => true,
        ],

        // 底部导航配置（移动端）
        'bottom_nav' => [
            'height' => '4rem',
            'items' => [
                ['icon' => 'home', 'text' => '首页', 'url' => '/'],
                ['icon' => 'search', 'text' => '查询', 'url' => '/orderquery'],
                ['icon' => 'message-square', 'text' => '投诉', 'url' => '/complaint'],
                ['icon' => 'phone', 'text' => '联系', 'url' => '/company/contact'],
            ],
        ],
    ],

    // 组件配置
    'components' => [
        // 按钮配置
        'button' => [
            'sizes' => [
                'sm' => ['padding' => '0.5rem 1rem', 'font_size' => '0.75rem'],
                'md' => ['padding' => '0.75rem 1.5rem', 'font_size' => '0.875rem'],
                'lg' => ['padding' => '1rem 2rem', 'font_size' => '1rem'],
            ],
            'variants' => [
                'primary' => ['bg' => 'primary', 'text' => 'white'],
                'secondary' => ['bg' => 'gray-100', 'text' => 'gray-700'],
                'outline' => ['bg' => 'transparent', 'border' => 'primary', 'text' => 'primary'],
            ],
        ],

        // 卡片配置
        'card' => [
            'padding' => '1.5rem',
            'border_radius' => 'lg',
            'shadow' => 'sm',
            'hover_shadow' => 'md',
            'hover_transform' => 'translateY(-2px)',
        ],

        // 表单配置
        'form' => [
            'input_padding' => '0.75rem',
            'input_border_radius' => 'md',
            'label_font_weight' => 'medium',
            'label_margin_bottom' => '0.5rem',
        ],

        // 模态框配置
        'modal' => [
            'backdrop_blur' => true,
            'backdrop_opacity' => 0.5,
            'border_radius' => 'xl',
            'max_width' => '32rem',
        ],
    ],

    // 功能配置
    'features' => [
        // 深色模式
        'dark_mode' => [
            'enabled' => true,
            'auto_detect' => true,
            'toggle_button' => true,
            'storage_key' => 'theme_preference',
        ],

        // 动画
        'animations' => [
            'enabled' => true,
            'respect_reduced_motion' => true,
            'page_transitions' => true,
            'hover_effects' => true,
        ],

        // 懒加载
        'lazy_loading' => [
            'enabled' => true,
            'images' => true,
            'components' => false,
            'threshold' => 0.1,
        ],

        // PWA支持
        'pwa' => [
            'enabled' => false,
            'manifest' => '/manifest.json',
            'service_worker' => '/sw.js',
        ],

        // 性能优化
        'performance' => [
            'critical_css_inline' => true,
            'defer_non_critical_css' => true,
            'preload_fonts' => true,
            'image_optimization' => true,
        ],
    ],

    // SEO配置
    'seo' => [
        'meta_tags' => [
            'viewport' => 'width=device-width, initial-scale=1.0',
            'theme_color' => '#3b82f6',
            'apple_mobile_web_app_capable' => 'yes',
            'apple_mobile_web_app_status_bar_style' => 'default',
        ],
        'structured_data' => true,
        'open_graph' => true,
        'twitter_cards' => true,
    ],

    // 开发配置
    'development' => [
        'debug_mode' => false,
        'show_grid' => false,
        'show_breakpoints' => false,
        'performance_monitor' => false,
    ],

    // 自定义CSS变量
    'custom_css_vars' => [
        // 可以在这里添加自定义的CSS变量
        // '--custom-color' => '#your-color',
    ],

    // 自定义JavaScript配置
    'custom_js_config' => [
        // 可以在这里添加自定义的JavaScript配置
        // 'customOption' => 'value',
    ],
];
