{extend name="./layout"}

{block name="content"}
<style>
    select {
        width: 100%;
        height: 40px;
    }
</style>
<div class="choose-item">
    <div class="choose-left left">商品分类：</div>
    <div class="choose-rigt left choose-rigt-type">
        <span class="choose-item-type on"> {$category.name} </span>
    </div>
</div>
<div class="choose-item">
    <div class="choose-left left">商品名称：</div>
    <div class="choose-rigt left choose-rigt-lianxi">
            <select class="iphone-s" name="goodid" id="goodid" onchange="selectgoodid()">
                <option value="">请选择商品</option>
            </select>
        </span>
    </div>
</div>
<script>
    $(function () {
            selectcateid();
        })
    </script>
<input type="hidden" name="cateid" id="cateid" value="{$category.id}">
{/block}