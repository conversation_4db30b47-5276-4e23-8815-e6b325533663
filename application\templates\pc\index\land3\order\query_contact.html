<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>订单查询 - {:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- Bootstrap -->
        <link href="__RES__/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="__RES__/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Slider -->               
        <link rel="stylesheet" href="__RES__/theme/landrick/css/owl.carousel.min.css"/> 
        <link rel="stylesheet" href="__RES__/theme/landrick/css/owl.theme.default.min.css"/> 
        <!-- Main Css -->
        <link href="__RES__/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="__RES__/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">
        <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
    </head>

    <body>
        {include file="./default_header"}
        <!-- Hero Start -->
        <section class="bg-half bg-light d-table w-100">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-12 text-center">
                        <div class="page-next-level">
                            <div class="alert alert-light alert-pills shadow" role="alert">
                                <span class="badge badge-pill badge-danger mr-1">订单查询</span>
                                <span class="content"> 轻松查询订单，即刻享受卡密自动交易</span>
                            </div>

                            <div class="page-next">
                                <nav aria-label="breadcrumb" class="d-inline-block">
                                    <ul class="breadcrumb bg-white rounded shadow mb-0">
                                        <li class="breadcrumb-item"></li>
                                        <li class="breadcrumb-item" aria-current="page">订单查询</li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div><!--end col-->
                </div><!--end row-->
            </div> <!--end container-->
        </section><!--end section-->
        <!-- Hero End -->


        <!-- Start Section -->
        <section class="section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="section-title text-center mb-4 pb-2">

                            <div class="row projects-wrapper">  
                                {foreach $order as $v}            
                                <div class="col-lg-4 col-md-6 col-12 mt-4 pt-2 business">
                                    <div class="card blog border-0 work-container work-classic shadow rounded-md overflow-hidden">
                                        <div class="card-body">
                                            <div class="content">
                                                <div>
                                                    <a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="badge badge-primary">{$v.trade_no}</a>
                                                    {switch name="$v.status"}
                                                    {case value="0"}<span class="badge badge-light">未支付</span>{/case}
                                                    {case value="1"}<span class="badge badge-success">已支付</span>{/case}
                                                    {case value="2"}<span class="badge badge-dark">已关闭</span>{/case}
                                                    {case value="3"}<span class="badge badge-warning">已退款</span>{/case}
                                                    {/switch}
                                                </div>
                                                <h5 class="mt-3"><a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="text-dark title">{$v.goods_name} </a></h5>
                                                <p class="text-muted">¥{$v.goods_price} X {$v.quantity}<a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="text-primary h6">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i data-feather="arrow-right" class="fea icon-sm"></i></a></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {/foreach}

                            </div>
                        </div><!--end col-->
                    </div><!--end row-->

                </div><!--end container-->

            </div><!--end container-->

        </section><!--end section-->
        <!-- End Section -->

        {include file="./default_footer"}

        <!-- javascript -->
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- SLIDER -->
        <script src="__RES__/theme/landrick/js/owl.carousel.min.js "></script>
        <script src="__RES__/theme/landrick/js/owl.init.js "></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="__RES__/app/js/clipboard.js"></script>
        <script src="__RES__/app/js/layer.js"></script>

    </body>
</html>