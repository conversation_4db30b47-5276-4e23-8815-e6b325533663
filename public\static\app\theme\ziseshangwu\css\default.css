﻿html, body, div, span, header,  applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dd, dl, dt, li, ol, ul, fieldset, form, label, legend{

margin: 0; padding: 0; border: 0; outline: 0; font-weight: inherit; font-style: inherit; font-family: inherit; font-size:100%; text-align: left;

vertical-align: baseline;

}
*{
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: Arial, '微软雅黑';
}
img {
border:none;
 -webkit-user-select: none;
-moz-user-select: none;
-ms-user-select: none;
user-select: none;
}
q:before, q:after, blockquote:before, blockquote:after {
content: "";
}
/* 基础样式----------------------------------------*/
body{
text-align:center; width:100%; position: relative; overflow-x: hidden;
}
/*--- 链接样式 ---*/
a{
text-decoration:none;
}
a:hover, a:active{
text-decoration: none;
}
ul{ list-style-type:none;}
ul, li {
    list-style: none;
}
blockquote {
margin: 0 0 18px 18px; font-style: italic;
}
strong {font-weight:bold;}
em {font-style:italic;}
/* 常用样式----------------------------------------*/
.left {float:left;}
.right {float:right;}
.clear {clear:both;}
.center{
	width: 1200px;
    margin: 0 auto;
}
.payment_item8,.about,.process,.advantage,.contact_box,footer,a,p,span,div{
	font-family: Arial, '微软雅黑';
}
.cursor{
	cursor: pointer;
}
