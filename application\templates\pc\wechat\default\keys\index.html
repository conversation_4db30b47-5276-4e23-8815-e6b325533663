{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-open="{:url('add')}" class='layui-btn layui-btn-small'>添加规则</button>
    <button data-update data-field='delete' data-action='{:url("$classuri/del")}'
            class='layui-btn layui-btn-small layui-btn-danger'>删除规则
    </button>
</div>
{/block}

{block name='content'}
<form onsubmit="return false;" data-auto="true" action="__SELF__" method="post">
    {if !empty($list)}
    <input type="hidden" value="resort" name="action"/>
    <table class="table table-hover">
        <thead>
            <tr>
                <th class='list-table-check-td'>
                    <input data-none-auto="" data-check-target='.list-check-box' type='checkbox'/>
                </th>
                <th class='list-table-sort-td'>
                    <button type="submit" class="layui-btn layui-btn-normal layui-btn-mini">排 序</button>
                </th>
                <th class="text-left">关键字</th>
                <th class="text-center">回复类型</th>
                <th class="text-center">回复内容</th>
                <th class="text-center">更新时间</th>
                <th class="text-center">状态</th>
                <th class="text-center">操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $key=>$vo}
            <tr>
                <td class='list-table-check-td'>
                    <input class="list-check-box" value='{$vo.id}' type='checkbox'/>
                </td>
                <td class='list-table-sort-td'>
                    <input name="_{$vo.id}" value="{$vo.sort}" class="list-sort-input"/>
                </td>
                <td class="text-left">
                    {if !empty($vo.qrc)}
                    <i class="fa fa-qrcode fa-lg pointer" data-tips-image="{$vo.qrc}" data-tips-text="关键字二维码"></i>
                    {/if}
                    {$vo.keys}
                </td>
                <td class="text-center">{$vo.type}</td>
                <td class="text-center">
                    {if $vo.type eq '音乐'}
                    <a data-phone-view='{:url("@wechat/review")}?type=music&title={$vo.music_title|urlencode}&desc={$vo.music_desc|urlencode}'
                       class="btn btn-link"><i class="fa fa-eye"></i> 预览
                    </a>
                    {elseif $vo.type eq '文字'}
                    <a data-phone-view='{:url("@wechat/review")}?type=text&content={$vo.content|urlencode}'
                       class="btn btn-link"><i class="fa fa-eye"></i> 预览
                    </a>
                    {elseif $vo.type eq '图片'}
                    <a data-phone-view='{:url("@wechat/review")}?type=image&content={$vo.image_url|urlencode}'
                       class="btn btn-link"><i class="fa fa-eye"></i> 预览
                    </a>
                    {elseif $vo.type eq '图文'}
                    <a data-phone-view='{:url("@wechat/review")}?type=news&content={$vo.news_id}'
                       class="btn btn-link"><i class="fa fa-eye"></i> 预览
                    </a>
                    {elseif $vo.type eq '视频'}
                    <a data-phone-view='{:url("@wechat/review")}?type=video&title={$vo.video_title|urlencode}&desc={$vo.video_desc|urlencode}&url={$vo.video_url|urlencode}'
                       class="btn btn-link"><i class="fa fa-eye"></i> 预览
                    </a>
                    {else}
                    {$vo.content}
                    {/if}
                </td>
                <td class="text-center">{$vo.create_at}</td>
                <td class='text-center'>
                    {if $vo.status eq 0}
                    <span>已禁用</span>
                    {elseif $vo.status eq 1}
                    <span style="color:#090">使用中</span>
                    {/if}
                </td>
                <td class='text-center nowrap'>

                    {if auth("$classuri/edit")}
                    <span class="text-explode">|</span>
                    <a data-open='{:url("edit")}?id={$vo.id}'>编辑</a>
                    {/if}

                    {if $vo.status eq 1 and auth("$classuri/forbid")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='status' data-value='0' data-action='{:url("$classuri/forbid")}'
                       href="javascript:void(0)">禁用</a>
                    {elseif auth("$classuri/resume")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='status' data-value='1' data-action='{:url("$classuri/resume")}'
                       href="javascript:void(0)">启用</a>
                    {/if}

                    {if auth("$classuri/del")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='delete' data-action='{:url("del")}'
                       href="javascript:void(0)">删除</a>
                    {/if}

                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
    {if isset($page)}<p>{$page}</p>{/if}
    {else}
    <p class="help-block">暂时无记录</p>
    {/if}

</form>

{/block}

{block name="script"}
<script>

    $(function () {
        /**
         * 默认类型事件
         * @type String
         */
        $('body').off('change', 'select[name=type]').on('change', 'select[name=type]', function () {
            var value = $(this).val(), $form = $(this).parents('form');
            var $current = $form.find('[data-keys-type="' + value + '"]').removeClass('hide');
            $form.find('[data-keys-type]').not($current).addClass('hide');
            switch (value) {
                case 'news':
                    return $('[name="news_id"]').trigger('change');
                case 'text':
                    return $('[name="content"]').trigger('change');
                case 'image':
                    return $('[name="image_url"]').trigger('change');
                case 'video':
                    return $('[name="video_url"]').trigger('change');
                case 'music':
                    return $('[name="music_url"]').trigger('change');
                case 'voice':
                    return $('[name="voice_url"]').trigger('change');
            }
        });

        function showReview(params) {
            params = params || {};
            $('#phone-preview').attr('src', '{"@wechat/review"|app_url}&' + $.param(params));
        }

        // 图文显示预览
        $('body').off('change', '[name="news_id"]').on('change', '[name="news_id"]', function () {
            showReview({type: 'news', content: this.value});
        });
        // 文字显示预览
        $('body').off('change', '[name="content"]').on('change', '[name="content"]', function () {
            showReview({type: 'text', content: this.value});
        });
        // 图片显示预览
        $('body').off('change', '[name="image_url"]').on('change', '[name="image_url"]', function () {
            showReview({type: 'image', content: this.value});
        });
        // 音乐显示预览
        var musicSelector = '[name="music_url"],[name="music_title"],[name="music_desc"],[name="music_image"]';
        $('body').off('change', musicSelector).on('change', musicSelector, function () {
            var params = {type: 'music'}, $parent = $(this).parents('form');
            params.title = $parent.find('[name="music_title"]').val();
            params.url = $parent.find('[name="music_url"]').val();
            params.image = $parent.find('[name="music_image"]').val();
            params.desc = $parent.find('[name="music_desc"]').val();
            showReview(params);
        });
        // 视频显示预览
        var videoSelector = '[name="video_title"],[name="video_url"],[name="video_desc"]';
        $('body').off('change', videoSelector).on('change', videoSelector, function () {
            var params = {type: 'video'}, $parent = $(this).parents('form');
            params.title = $parent.find('[name="video_title"]').val();
            params.url = $parent.find('[name="video_url"]').val();
            params.desc = $parent.find('[name="video_desc"]').val();
            showReview(params);
        });

        // 默认事件触发
        $('select[name=type]').map(function () {
            $(this).trigger('change');
        });

        /*! 删除关键字 */
        $('[data-delete]').on('click', function () {
            var id = this.getAttribute('data-delete');
            var url = this.getAttribute('data-action');
            $.msg.confirm('确定要删除这条记录吗？', function () {
                $.form.load(url, {id: id}, 'POST', function (ret) {
                    if (ret.code === "SUCCESS") {
                        window.location.reload();
                    }
                });
            })
        });
    });
</script>
{/block}