sudo: false
language: php

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0

before_script:
  - export QINIU_TEST_ENV="travis"
  - travis_retry composer self-update
  - travis_retry composer install --no-interaction --prefer-source --dev

script:
  - ./vendor/bin/phpcs --standard=PSR2 src
  - ./vendor/bin/phpcs --standard=PSR2 examples
  - ./vendor/bin/phpcs --standard=PSR2 tests
  - ./vendor/bin/phpunit --coverage-text --coverage-clover=coverage.clover tests/Qiniu/Tests/

after_script:
  - wget https://scrutinizer-ci.com/ocular.phar
  - php ocular.phar code-coverage:upload --format=php-clover coverage.clover
