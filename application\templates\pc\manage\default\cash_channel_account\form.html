<style>
    .rate_type{
	border: 1px solid #C9C9C9;
    background-color: #fff;
	color: #555;
}
.layui-btn-normal {
    background-color: #1E9FFF;
	color:#fff;
}
</style>
<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">账号备注</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$account.name|default=''}" required="required" title="请输入账号名"
                placeholder="请输入账号名" class="layui-input">
        </div>
    </div>

    <div class="hr-line-dashed"></div>

    {foreach $fields as $k => $v}
    <div class="layui-form-item">
        <label class="layui-form-label">{$v['name']}</label>
        <div class="layui-input-block">
            <textarea class="layui-textarea" name="params[{$k}]" cols="30" rows="1" placeholder="请输入{$v['name']}">{$v['value']|default=''}</textarea>
        </div>
    </div>
    {/foreach}

    <div class="hr-line-dashed"></div>
    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="status" style="display:inline">
                <option value="1" {if isset($account) && 1==$account['status']}selected{/if}>开启 </option> <option value="0"
                    {if isset($account) && 0==$account['status']}selected{/if}>关闭 </option> </select> </div> </div>
                    <div class="layui-form-item text-center">
                    <input type="hidden" name="account_id" value="{$account.id|default=""}">
                    <button class="layui-btn" type='submit'>保存</button>
                    <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消吗？" data-close>取消</button>
        </div>

</form>