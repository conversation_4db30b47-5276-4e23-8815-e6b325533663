{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-modal='{:url("add")}' data-title="添加分类" class='layui-btn layui-btn-small'><i
            class='fa fa-plus'></i> 添加分类
    </button>
</div>
{/block}

{block name="content"}

<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
        <tr>
            <th>ID</th>
            <th>分类名</th>
            <th>别名</th>
            <th>分类备注</th>
            <th>状态</th>
            <th>添加时间</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
            {foreach $categorys as $v}
            <tr>
            <td>{$v.id}</td>
            <td>{$v.name}</td>
            <td>{$v.alias}</td>
            <td>{$v.remark}</td>
            <td>
                {if $v.status==1}
                <a style="color:green" data-tips="确定不可用吗？ " data-update="{$v.id}" data-field='status' data-value='0' data-action='{:url("change_status")}'
                   href="javascript:void(0)"><i class="glyphicon glyphicon-ok"></i></a>
                {else/}
                <a style="color:red" data-tips="确定可用吗？" data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("change_status")}'
                   href="javascript:void(0)"><i class="glyphicon glyphicon-remove"></i></a>
                {/if}
            </td>
            <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
            <td>
                <a data-title="编辑" data-modal='{:url("edit")}?cate_id={$v.id}' href="javascript:void(0)">编辑</a>
                <a data-title="删除"  href="javascript:void(0)" onclick="del(this,'{$v.id}')">删除</a>
            </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
<script>
    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        //日期范围
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });
    function del(obj, id) {
        layer.confirm('确认要删除此分类吗？', function (index) {
            $.ajax({
                url:"/manage/article_category/del",
                type:'post',
                data:'id='+id,
                success:function(res){
                    if(res.code == 1){
                        $(obj).parents("tr").remove();
                        layer.msg('已删除!',{icon:1,time:1000});
                    } else {
                        layer.msg('删除失败!',{icon:1,time:1000});
                    }
                }
            });
        });
    }
</script>
{/block}
