<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>{:plugconf('tradetask','activity_name')} - {:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- Bootstrap -->
        <link href="__RES__/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="__RES__/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Magnific -->
        <link href="__RES__/theme/landrick/css/magnific-popup.css" rel="stylesheet" type="text/css" />
        <!-- Main Css -->
        <link href="__RES__/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="__RES__/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">
        <link href="__RES__/merchant/default/css/icons.min.css" rel="stylesheet" type="text/css">

        <style>
            .badge{
                color:#fff;
            }
            #topnav.nav-sticky .buy-button .login-btn-light,#topnav .buy-button .login-btn-light{
                display: inline-block;
            }
        </style>

    </head>

    <body>
        <!-- Loader-->
        <div id="preloader">
            <div id="status">
                <div class="spinner">
                    <div class="double-bounce1"></div>
                    <div class="double-bounce2"></div>
                </div>
            </div>
        </div> 
        <!-- Loader -->

        <!-- Navbar STart -->
        <header id="topnav" class="defaultscroll sticky">
            <div class="container">
                <div>
                    <a class="logo" href="/">
                        <img src="{:sysconf('site_logo')}" height="24" alt="">
                    </a>
                </div>                 
                <div class="buy-button">
                    <a href="/">  <div class="btn btn-light login-btn-light"  style="font-size: 13px;padding: 6px 20px;">首页</div></a>
                </div>
            </div>
        </header>

        <section class="mt-5 section d-table w-100">
            <div class="container">

                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="section-title text-center mb-4 pb-2">
                            <h4 class="title mb-4">{:plugconf('tradetask','activity_name')}</h4>
                        </div>
                    </div><!--end col-->
                </div>


                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="card shadow rounded border-0">
                            <div class="card-body">
                                {:stripslashes(removeXSS(htmlspecialchars_decode(plugconf('tradetask','activity_content'))))}

                                <style>
                                    tr td b{
                                        color:#D53D3D
                                    }
                                    tr:nth-child(1) td b{
                                        color:#8CA2BD;
                                    } 
                                    tr:nth-child(2) td b{
                                        color:#5080B8;
                                    } 
                                    tr:nth-child(3) td b{
                                        color:#E9743B;
                                    } 
                                    tr:nth-child(4) td b{
                                        color:#D53D3D;
                                    } 
                                </style>
                                
                                
                                <div class="table-responsive">
                                    <table class="table mb-0">
                                        <thead>
                                            <tr>
                                                <th>任务名称</th>
                                                <th>任务目标</th>
                                                <th>奖励金额</th>
                                                <th>任务时长</th>
                                                <th>任务介绍</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $tradetask as $v}
                                            <tr>
                                                <td><b>{$v.name}</b></td>
                                                <td>￥{$v.target}</td>
                                                <td>￥{$v.reward}</td>
                                                <td>{$v.duration}天</td>
                                                <td>{$v.desc}</td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>


        <!-- javascript -->
        <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- Magnific -->
        <script src="__RES__/theme/landrick/js/jquery.magnific-popup.min.js"></script>
        <script src="__RES__/theme/landrick/js/isotope.js"></script>
        <script src="__RES__/theme/landrick/js/portfolio.init.js"></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>

    </body>
</html>