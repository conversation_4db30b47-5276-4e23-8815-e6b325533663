<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">用户账号</label>
        <div class="layui-input-block">
            {if $vo and $vo.username}
            <input type="text" readonly="" disabled="" name="username" value='{$vo.username|default=""}'
                   required="required" title="请输入用户名称" placeholder="请输入用户名称" class="layui-input disabled">
            {else}
            <input type="text" name="username" value='{$vo.username|default=""}' required="required"
                   title="请输入用户名称" placeholder="请输入用户名称" class="layui-input">
            {/if}
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">访问授权</label>
        <div class="layui-input-block">
            {foreach $authorizes as $authorize}
            {if in_array($authorize['id'],$vo['authorize'])}
            <label class="think-checkbox">
                <input type="checkbox" checked name="authorize[]" value="{$authorize.id}" lay-ignore> {$authorize.title}
            </label>
            {else}
            <label class="think-checkbox">
                <input type="checkbox" name="authorize[]" value="{$authorize.id}" lay-ignore> {$authorize.title}
            </label>
            {/if}
            {/foreach}
            {if empty($authorizes)}
            <span class="color-desc" style="line-height:36px">未配置权限</span>
            {/if}
        </div>
    </div>

    <div class="hr-line-dashed"></div>

    <div class="layui-form-item text-center">
        {if isset($vo['id'])}<input type='hidden' value='{$vo.id}' name='id'/>{/if}
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>
    <script>window.form.render();</script>
</form>
