{extend name='./content'}

{block name="button"}

<div class="nowrap pull-right" style="margin-top:10px">
    <button data-load="{:url('sync')}" class='layui-btn layui-btn-small'>同步粉丝</button>
</div>

<div class="nowrap pull-right" style="margin-top:10px;margin-right:10px">
    <button data-update="" data-action="{:url('backadd')}" class='layui-btn layui-btn-small'>移入黑名单</button>
</div>

{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="animated form-search" action="__SELF__" onsubmit="return false" method="get">

    <div class="row">

        <div class="col-xs-2">
            <div class="form-group">
                <input type="text" name="nickname" value="{$Think.get.nickname|default=''}" placeholder="昵称" class="input-sm form-control">
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">
                <select name="sex" class="input-sm form-control">
                    <option value="">- 性别 -</option>
                    <!--{if $Think.get.sex eq 1}-->
                    <option selected value="1">- 男 -</option>
                    <!--{else}-->
                    <option value="1">- 男 -</option>
                    <!--{/if}-->
                    <!--{if $Think.get.sex eq 2}-->
                    <option selected value="2">- 女 -</option>
                    <!--{else}-->
                    <option value="2">- 女 -</option>
                    <!--{/if}-->
                </select>
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">
                <select name="tag" class="input-sm form-control">
                    <option value="">- 粉丝标签 -</option>
                    <!--{foreach $tags as $key=>$tag}-->
                    <!--{if $Think.get.tag eq $key}-->
                    <option selected value="{$key}">{$tag}</option>
                    <!--{else}-->
                    <option value="{$key}">{$tag}</option>
                    <!--{/if}-->
                    <!--{/foreach}-->
                </select>
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">
                <input type="text" name="country" value="{$Think.get.country|default=''}" placeholder="国家" class="input-sm form-control">
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">
                <input type="text" name="province" value="{$Think.get.province|default=''}" placeholder="省份" class="input-sm form-control">
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">
                <input type="text" name="city" value="{$Think.get.city|default=''}" placeholder="城市" class="input-sm form-control">
            </div>
        </div>

        <div class="col-xs-1">
            <div class="form-group">
                <button type="submit" class="btn btn-sm btn-white"><i class="fa fa-search"></i> 搜索</button>
            </div>
        </div>

    </div>
</form>
<!-- 表单搜索 结束 -->

<form onsubmit="return false;" data-auto="" method="POST">
    {if !empty($list)}
    <input type="hidden" value="resort" name="action"/>
    <table class="table table-hover">
        <thead>
            <tr>
                <th class='list-table-check-td'>
                    <input data-none-auto="" data-check-target='.list-check-box' type='checkbox'/>
                </th>
                <th class='text-left'>用户昵称</th>
                <th class='text-left'>性别</th>
                <th class='text-center'>标签</th>
                <th class='text-left'>区域</th>
                <th class='text-center'>关注时间</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $key=>$vo}
            <tr>
                <td class='list-table-check-td'>
                    <input class="list-check-box" value='{$vo.id}' type='checkbox'/>
                </td>
                <td class='text-left'>
                    <img style="width:25px;height:25px;border-radius:50%;margin-right:10px" data-tips-image src="{$vo.headimgurl}"/>
                    {$vo.nickname|default="<span style='color:#999'>未设置微信昵称</span>"}
                </td>
                <td class='text-left'>{$vo.sex==1?'男':($vo.sex==2?'女':'未知')}</td>
                <td>
                    <span>
                        <a data-add-tag='{$vo.id}' data-used-id='{:join(",",array_keys($vo.tags_list))}' id="tag-fans-{$vo.id}" href='javascript:void(0)'
                           style='font-size:12px;font-weight:400;border-radius:50%;background:#9f9f9f' class='label label-default'>+</a>
                    </span>

                    {if empty($vo.tags_list)}
                    <span style='color:#999'>尚未设置标签</span>
                    {else}
                    {foreach $vo.tags_list as $k=>$tag}
                    <span>
                        <a href='javascript:void(0)' style='font-size:12px;font-weight:400;background:#9f9f9f' class='label label-default'>{$tag}</a>
                    </span>
                    {/foreach}
                    {/if}

                </td>
                <td class='text-left'>{$vo.country|default='<span style="color:#999">未设置区域信息</span>'}{$vo.province}{$vo.city}</td>
                <td class='text-center'>{$vo.subscribe_at}</td>
            </tr>
            {/foreach}
        </tbody>
    </table>
    {if isset($page)}<p>{$page}</p>{/if}
    {else}
    <p class="help-block">暂时无记录</p>
    {/if}
</form>

<div id="tags-box" class="hide">
    <form>
        <div class="row" style='max-height:230px;overflow:auto;margin-right:0'>
            {foreach $tags as $key=>$tag}
            <div class="col-xs-4">
                <label style="overflow:hidden;text-overflow:ellipsis;display:block;white-space:nowrap">
                    <input value="{$key}" type="checkbox" /> {$tag}
                </label>
            </div>
            {/foreach}
        </div>
        <div class="text-center" style="margin-top:10px">
            <div class="hr-line-dashed" style="margin-top:0"></div>
            <button type="button" data-event="confirm" class="layui-btn layui-btn-small">保存数据</button>
            <button type="button" data-event="cancel" class="layui-btn layui-btn-danger layui-btn-small" type='button'>取消编辑</button>
        </div>
    </form>
</div>
{/block}

{block name="script"}
{if auth("$classuri/tagset")}
<script>
    // 添加标签
    $('body').find('[data-add-tag]').map(function () {
        var self = this;
        var fans_id = this.getAttribute('data-add-tag');
        var used_ids = (this.getAttribute('data-used-id') || '').split(',');
        var $content = $(document.getElementById('tags-box').innerHTML);
        for (var i in used_ids) {
            $content.find('[value="' + used_ids[i] + '"]').attr('checked', 'checked');
        }
        $content.attr('fans_id', fans_id);
        // 标签面板关闭
        $content.on('click', '[data-event="cancel"]', function () {
            $(self).popover('hide');
        });
        // 标签面板确定
        $content.on('click', '[data-event="confirm"]', function () {
            var tags = [];
            $content.find('input:checked').map(function () {
                tags.push(this.value);
            });
            $.form.load('{:url("tagset")}', {fans_id: $content.attr('fans_id'), 'tags': tags.join(',')}, 'post');
        });
        // 限制每个表单最多只能选择三个
        $content.on('click', 'input', function () {
            ($content.find('input:checked').size() > 3) && (this.checked = false);
        });
        // 标签选择面板
        $(this).data('content', $content).on('shown.bs.popover', function () {
            $('[data-add-tag]').not(this).popover('hide');
        }).popover({
            html: true,
            trigger: 'click',
            content: $content,
            title: '标签编辑（最多选择三个标签）',
            template: '<div class="popover" style="max-width:500px" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content" style="width:500px"></div></div>'
        });
    });
</script>
{/if}
{/block}
