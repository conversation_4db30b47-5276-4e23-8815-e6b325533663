{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">

            {foreach $goodsList as $k=> $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">店铺名：{$v.pgoods.user.shop_name}<a href="//wpa.qq.com/msgrd?v=3&uin={$v.pgoods.user.qq}&Site=" target="_blank"><i class="iconfont icon-qq-white"></i>咨询客服</a>
                        <a href="{:url('agent/poolGoods',['type'=>1,'key'=>$v.pgoods.user.agent_key])}" target="_blank"><i class="iconfont icon-qq-white"></i>店铺直达</a>
                    </h6>

                    {if $v.status==1}
                    <span onclick="change_status('{$v.id}', 0)" class="badge rounded-pill bg-success float-end">上架中</span>
                    {else/}
                    <span onclick="change_status('{$v.id}', 1)" class="badge rounded-pill bg-danger float-end">已下架</span>
                    {/if}

                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>商品名称：</span>
                        <span>{$v.name}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>售价：</span>
                        <span>{$v.price}</span>
                    </div>

                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>商品分类：</span>
                        <span>
                            {$v.category.name|default="未分类"}
                        </span>
                    </div>
                    <div class="order-wrap-text">
                        <span>库存：</span>
                        <span>
                            {if $v->cards_stock_count > 0}
                            {$v->cards_stock_count}张
                            {else}
                            <span class="text-danger">断货</span>
                            {/if}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>原商品名：</span>
                        <span>{$v.pgoods.name}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>供货价格：</span>
                        <span>
                            {$v.cost_price}元
                        </span>
                    </div>
                </div>


                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">

                    <a class="me-1" href="javascript:void(0);" onclick="$.x_show('商品链接', '{:url(\'agent/link\',[\'id\'=>$v.id])}', '90%');"><span class="goods-warp-btn">链接</span></a>
                    <a class="me-1" href="{:url('agent/edit',['id'=>$v.id])}"><span class="goods-warp-btn">编辑</span></a>
                    <a href="javascript:void(0);" onclick="del(this, '{$v.id}')"><span class="goods-warp-btn goods-warp-btn-delete" >删除</span></a>
                </div>
            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}

<script>

    function change_status(id, status)
    {
        $.post("{:url('agent/changeStatus')}", {
            id: id,
            status: status
        }, function (res) {
            location.reload();
        });
    }

    function del(obj, id)
    {
        $.confirm({
            title: '确定删除吗？',
            content: '确定要删除此代理商品吗！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('agent/del')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("删除成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }

</script>
{/block}


