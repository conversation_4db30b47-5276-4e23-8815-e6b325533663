{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >
    <div class="col-sm-8 col-sm-offset-2">
        <div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
            <p style="font-size:16px;" class="text-center">配套独立版客服系统(新)</p>
        </div>
    </div>


    <div class="col-sm-12">
        <div class="form-group">
            <label class="col-sm-2 control-label">功能是否开启</label>
            <div class='col-sm-8'>
                <select name="status" class="layui-input" >
                    <option value="0" {if plugconf('chat','status')=='0'}selected{/if}>关闭</option>
                    <option value="1" {if plugconf('chat','status')=='1'}selected{/if}>开启</option>
                </select>
            </div>
        </div>

        <div class="hr-line-dashed"></div>

        <div class="form-group">
            <label class="col-sm-2 control-label">客服系统域名</label>
            <div class="col-sm-8">
                <input type="text"  name="domain"  autocomplete="off" class="layui-input"  value="{:plugconf('chat','domain')}">
                <p class="help-block">例如：https://www.kefu.com，必须http://或者https://开头</p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">客服系统通信密钥</label>
            <div class="col-sm-8">
                <input type="text"  name="apikey"  autocomplete="off" class="layui-input"  value="{:plugconf('chat','apikey')}">
                <p class="help-block">前往客服系统后台获取，需与客服系统密钥匹配</p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">平台前端客服Script代码</label>
            <div class="col-sm-8">
                <textarea name="ptkefu" id="" cols="30" rows="5" class="layui-textarea">{:plugconf('chat','ptkefu')}</textarea>
                <p class="help-block">请手动在客服平台添加账号，获取script代码</p>
            </div>
        </div>


    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>


</form>




<div class="row">
    <div class="col-sm-12 text-center">
        <div class="hr-line-dashed"></div>
        <a target="_blank" class="layui-btn" href="{:plugconf('chat','domain')}/uchat/login/index">登录客服系统后台</a>
        <a target="_blank" class="layui-btn" href="{:plugconf('chat','domain')}/uchat/set/meal">套餐管理</a>
        <a target="_blank" class="layui-btn" href="{:plugconf('chat','domain')}/uchat/recharge/index">订单管理</a>

    </div>
</div>

<div class="row" style='margin-top:16px'>
    <div class="col-sm-8 col-sm-offset-2">
        <div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
            <p style="font-size:13px">提示：请前往内部QQ群文件下载独立版客服系统！</p>
        </div>
    </div>
</div>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });

</script>
{/block}