{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->

            {foreach $messages as $k=> $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">{if $v.from_id==0}<span class="badge bg-danger">系统</span>{else/}{$v.fromUser.username}{/if}</h6>

                    {if $v.status==1}
                    <span class="badge rounded-pill bg-success float-end">已读</span>
                    {else/}
                    <span class="badge rounded-pill bg-light float-end">未读</span>
                    {/if}

                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>标题：</span>
                        <span>{$v.title}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div>
                        {$v.content}
                    </div>

                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>发送时间：</span>
                        <span>  
                            {$v.create_at|date="Y-m-d H:i:s",###}
                        </span>
                    </div>
                </div>


                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">
                    {if $v.status==0}
                    <a class="me-1" href="javascript:void(0);" onclick="change_status(this, '{$v.id}')"><span class="goods-warp-btn">标记已读</span></a>
                    {/if}
                    <a href="javascript:void(0);" onclick="del(this, '{$v.id}')"><span class="goods-warp-btn goods-warp-btn-delete" >删除</span></a>
                </div>

            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}
<script>
    function change_status(obj, id)
    {
        $.post("{:url('message/changeStatus')}", {
            id: id,
            status: 1
        }, function (res) {
            if (res.code != 1) {
                $.alert(res.msg);
            } else {
                setTimeout(function () {
                    location.reload();
                }, 200);
            }
        });
    }

    function del(obj, id)
    {

        $.confirm({
            title: '确定删除吗？',
            content: '你将无法恢复该操作！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('message/del')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert('删除失败' + res.msg);
                            } else {
                                $.alert('删除成功');
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });

    }

</script>
{/block}


