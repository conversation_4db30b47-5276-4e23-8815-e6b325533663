<svg width="770" height="644" viewBox="0 0 770 644" fill="none" xmlns="http://www.w3.org/2000/svg">
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M108.962 619.562H111.76L135.143 619.04L136.648 619.164L169.503 618.42L168.618 616.215L197.038 597.617C196.905 597.316 197.817 597.033 199.429 596.812C202.394 596.47 205.377 596.313 208.362 596.342L547.853 596.98C554.254 596.98 559.363 597.555 559.168 598.246L587.19 613.54L584.613 622.449C583.584 622.789 582.516 623 581.435 623.078L665.597 627.754L659.701 630.906L655.717 632.465L557.937 631.438L570.563 632.182L570.997 633.262L590.97 634.75L587.349 636.521L161.278 642.8L148.627 642.986L122.066 643.384L119.844 642.065L133.124 639.86L132.159 639.107L144.271 637.415L145.661 637.221L156.586 635.697L157.967 635.503L166.033 634.378L173.948 633.067L174.187 633.023L177.534 632.465H179.995H177.525L179.951 632.35H180.677L183.333 631.987L209.548 631.624L209.885 631.562L210.239 631.615L234.87 631.287L234.958 631.243H235.401L363.548 629.472L90.0601 626.753V620.111L108.962 619.562Z" fill="#792A36"/>
</g>
<path d="M108.414 605.561C104.491 582.651 119.923 553.533 163.306 540.126C196.144 529.977 231.62 531.527 259.748 535.928C260.792 524.025 260.987 512.063 260.333 500.133C259.447 482.156 267.929 468.42 285.61 459.299C311.214 446.104 354.818 443.712 405.24 452.745C453.934 461.468 498.751 479.18 525.224 500.142L523.196 502.701C466.382 457.705 338.404 435.725 287.124 462.203C270.682 470.678 262.767 483.386 263.608 499.965C264.272 512.126 264.068 524.318 262.998 536.45C291.851 541.277 312.224 548.849 312.631 549.008L311.471 552.063C311.081 551.913 291.046 544.465 262.67 539.665C259.27 571.059 249.301 596.342 233.187 613.992C216.702 632.022 194.232 641.71 166.396 642.791C165.201 642.791 164.014 642.862 162.854 642.862C133.522 642.897 112.327 628.489 108.414 605.561ZM164.289 543.252C122.81 556.066 107.953 583.51 111.628 605.012C115.408 627.152 136.887 640.701 166.272 639.55C193.205 638.505 214.905 629.171 230.779 611.804C246.45 594.66 256.145 569.925 259.447 539.187C231.647 534.803 196.649 533.245 164.289 543.252Z" fill="url(#paint0_linear_292_86830)"/>
<path d="M434.881 304.26C449.543 361.708 490.854 386.77 522.93 397.645C577.026 416.047 639.09 406.182 658.036 391.304C665.757 385.246 672.326 374.336 678.674 363.789C686.191 351.311 693.292 339.515 700.525 338.869C703.402 338.603 706.271 340.161 709.317 343.615C721.712 357.785 722.269 379.472 714.965 390.905C710.742 397.512 704.341 399.938 696.877 397.724C689.945 395.67 666.483 393.544 652.193 404.897C643.844 411.53 640.011 421.52 640.799 434.582C642.782 467.446 681.074 485.946 682.703 486.752L684.101 483.803C683.721 483.626 645.925 465.312 644.057 434.387C643.339 422.45 646.757 413.39 654.221 407.457C667.342 397.034 689.715 398.999 695.948 400.85C704.801 403.507 712.716 400.496 717.719 392.668C725.687 380.269 725.173 356.686 711.769 341.463C708.042 337.23 704.164 335.264 700.259 335.61C691.406 336.415 683.862 348.893 675.903 362.106C669.706 372.405 663.304 383.05 656.053 388.736C637.664 403.171 577.079 412.646 524.002 394.563C492.704 383.936 452.385 359.458 438.122 303.428L434.881 304.26Z" fill="url(#paint1_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.234 225.638L186.379 373.202L188.548 425.098C188.671 427.781 189.822 430.313 191.762 432.17C193.702 434.027 196.283 435.065 198.968 435.069H559.124C561.833 435.071 564.437 434.02 566.386 432.138C568.335 430.256 569.477 427.69 569.571 424.982L575.919 225.47C575.969 224.069 575.737 222.672 575.236 221.363C574.735 220.054 573.976 218.858 573.003 217.849C572.031 216.839 570.866 216.035 569.576 215.485C568.287 214.936 566.9 214.651 565.499 214.648H190.664C189.247 214.65 187.845 214.94 186.544 215.5C185.243 216.06 184.069 216.879 183.093 217.906C182.118 218.934 181.361 220.15 180.87 221.479C180.379 222.808 180.162 224.223 180.234 225.638Z" fill="url(#paint2_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M561.532 226.187L558.336 410.539L198.774 410.175L192.187 225.435L561.532 226.187Z" fill="url(#paint3_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M569.571 425.018V420.439L188.265 418.234L188.583 425.124C188.583 425.124 188.486 434.37 199.022 435.096H559.124C559.124 435.096 569.235 434.529 569.571 425.018Z" fill="url(#paint4_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M218.739 426.665C218.739 427.282 218.922 427.884 219.264 428.397C219.606 428.909 220.093 429.309 220.663 429.545C221.232 429.781 221.859 429.843 222.463 429.722C223.068 429.602 223.623 429.305 224.059 428.869C224.495 428.433 224.792 427.878 224.912 427.273C225.032 426.668 224.97 426.042 224.735 425.472C224.499 424.902 224.099 424.416 223.587 424.073C223.074 423.731 222.472 423.548 221.855 423.548C221.029 423.55 220.238 423.879 219.654 424.463C219.07 425.047 218.741 425.839 218.739 426.665Z" fill="#FFFB00"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M412.765 435.096L404.7 473.105H357.067L349.25 435.096H412.765Z" fill="url(#paint5_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M409.959 448.317L353.163 454.1L349.25 435.096H412.765L409.959 448.317Z" fill="#401734"/>
</g>
<path d="M440.424 473.105H324.149V480.367H440.424V473.105Z" fill="url(#paint6_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M324.149 473.105L356.27 469.235H405.523L440.433 473.105H324.149Z" fill="url(#paint7_radial_292_86830)"/>
<g style="mix-blend-mode:soft-light" opacity="0.8">
<path fill-rule="evenodd" clip-rule="evenodd" d="M351.569 480.367V473.105H326.195L327.753 480.367H351.569Z" fill="#FBF9FA"/>
</g>
<g style="mix-blend-mode:soft-light" opacity="0.8">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.234 225.629L186.37 373.203L188.264 388.187L194.789 379.756L189.291 223.256H380.768L389.312 214.692H190.664C189.251 214.693 187.854 214.981 186.555 215.537C185.257 216.093 184.085 216.907 183.11 217.93C182.135 218.952 181.378 220.161 180.883 221.485C180.389 222.808 180.168 224.218 180.234 225.629Z" fill="#FBF9FA"/>
</g>
<g style="mix-blend-mode:soft-light" opacity="0.8">
<path fill-rule="evenodd" clip-rule="evenodd" d="M572.015 348.238L561.532 355.562L559.7 411.761H469.189L448.188 435.096H559.124C561.57 435.108 563.944 434.276 565.847 432.74C567.751 431.204 569.066 429.058 569.571 426.665L572.015 348.238Z" fill="#FBF9FA"/>
</g>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M369.285 225.798L197.33 369.634L192.187 225.435L369.285 225.798Z" fill="#FBF9FA"/>
</g>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M559.195 361.079L486.047 410.468L558.336 410.539L559.195 361.079Z" fill="#FBF9FA"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M202.837 246.37L191.753 236.239C191.753 236.239 167.175 236.079 154.337 234.281V251.161C154.337 251.161 173.107 255.296 190.814 254.49L202.837 246.37Z" fill="url(#paint8_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M202.837 278.791L193.293 271.237L158.321 273.415L161.624 290.277L193.948 287.071L202.837 278.791Z" fill="url(#paint9_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M563.764 331.793L559.948 361.601C559.948 361.601 583.737 363.275 603.994 370.138L610.139 336.805C610.139 336.805 593.76 330.978 563.764 331.793Z" fill="url(#paint10_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M572.121 331.607L570.828 362.664L559.938 361.601L563.763 331.784L572.121 331.607Z" fill="#953345"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M182.784 288.178L182.111 271.936L193.293 271.237L202.837 278.791L193.948 287.071L182.784 288.178Z" fill="#953345"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.615 236.07L181.394 254.694L190.814 254.49L202.837 246.378L191.753 236.239L180.615 236.07Z" fill="#953345"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M92.7442 216.152C92.7442 216.152 78.2508 190.595 67.4583 179.436L48.6621 196.537C48.6621 196.537 67.8213 212.477 77.5249 227.302L92.7442 216.152Z" fill="url(#paint11_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M563.365 458.794C563.365 458.794 586.853 481.261 601.843 489.656L616.318 465.551C616.318 465.551 590.953 454.764 575.946 442.136L563.365 458.794Z" fill="url(#paint12_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M644.163 526.815L566.481 503.17L216.136 476.018L113.947 488.691L644.163 526.815Z" fill="url(#paint13_linear_292_86830)"/>
<path style="mix-blend-mode:screen" d="M222.369 513.266C225.811 513.266 228.602 505.887 228.602 496.785C228.602 487.683 225.811 480.305 222.369 480.305C218.926 480.305 216.136 487.683 216.136 496.785C216.136 505.887 218.926 513.266 222.369 513.266Z" fill="url(#paint14_radial_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M309.895 483.289L454.28 507.864L589.846 510.281L566.481 503.17L309.895 483.289Z" fill="#AC2A41"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M638.727 542.703L635.061 550.54L113.947 522.166V512.256L638.727 542.703Z" fill="url(#paint15_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M523.648 518.146L131.38 485.627L113.947 488.691L523.648 518.146Z" fill="url(#paint16_linear_292_86830)"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M216.136 476.018V496.033L566.287 521.67L566.482 503.17L216.136 476.018Z" fill="url(#paint17_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M638.727 542.703L644.163 526.815L113.947 488.691V512.256L638.727 542.703Z" fill="url(#paint18_linear_292_86830)"/>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M192.984 516.844L196.799 494.253L127.786 488.673L130.096 512.469L192.984 516.844Z" fill="#FBF9FA"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M621.259 541.693L625.889 525.505L579.479 522.166L578.106 539.187L621.259 541.693Z" fill="#AC2A41"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M178.871 492.809L155.931 514.267L146.608 523.946L113.947 522.166V488.691L155.524 482.767L178.871 492.809Z" fill="#AC2A41"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M572.05 571.201L575.379 562.301L141.402 599.052L143.448 605.765L572.05 571.201Z" fill="url(#paint19_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M575.379 562.301L492.501 528.578L194.551 550.691L141.402 599.052L575.379 562.301Z" fill="url(#paint20_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M557.344 559.706L154.975 593.535L152.752 584.121L199.862 548.202L486.728 526.913L556.574 549.371L557.344 559.706Z" fill="url(#paint21_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M556.574 549.371L152.752 584.121L199.862 548.202L486.728 526.913L556.574 549.371Z" fill="url(#paint22_radial_292_86830)"/>
<path d="M163.908 575.611L540.859 544.314L538.707 543.624L165.191 574.637L163.908 575.611Z" fill="url(#paint23_linear_292_86830)"/>
<path d="M175.25 566.968L524.232 538.966L522.276 538.337L176.525 566.002L175.25 566.968Z" fill="url(#paint24_linear_292_86830)"/>
<path d="M182.758 561.424L512.767 535.281L510.775 534.644L183.803 560.467L182.758 561.424Z" fill="url(#paint25_linear_292_86830)"/>
<path d="M190.425 554.445L500.726 531.411L498.53 530.703L191.47 553.498L190.425 554.445Z" fill="url(#paint26_linear_292_86830)"/>
<path d="M194.551 550.691L490.181 528.02L488.632 527.524L196.791 550.088L194.551 550.691Z" fill="url(#paint27_linear_292_86830)"/>
<path d="M500.203 540.931L500.531 540.134L490.978 536.211L491.005 536.663L490.845 537.044L500.203 540.931Z" fill="url(#paint28_linear_292_86830)"/>
<path d="M473.219 528.706L472.885 529.508L480.748 532.783L481.081 531.982L473.219 528.706Z" fill="url(#paint29_linear_292_86830)"/>
<path d="M526.401 551.851L528.49 551.789L513.935 545.722L513.607 546.519L526.401 551.851Z" fill="url(#paint30_linear_292_86830)"/>
<path d="M452.866 529.093L452.473 529.867L454.707 531.002L455.1 530.228L452.866 529.093Z" fill="url(#paint31_linear_292_86830)"/>
<path d="M489.243 548.601L489.367 548.592L489.349 548.131L489.526 547.777L478.07 541.906L477.946 541.914L477.972 542.366L477.777 542.738L489.243 548.601Z" fill="url(#paint32_linear_292_86830)"/>
<path d="M469.951 538.726L470.066 538.718L470.048 538.266L470.234 537.903L461.593 533.501L461.194 534.272L469.951 538.726Z" fill="url(#paint33_linear_292_86830)"/>
<path d="M197.006 559.471L190.942 565.082L191.532 565.719L197.595 560.108L197.006 559.471Z" fill="url(#paint34_linear_292_86830)"/>
<path d="M207.994 549.367L204.417 552.673L205.006 553.31L208.583 550.005L207.994 549.367Z" fill="url(#paint35_linear_292_86830)"/>
<path d="M174.01 582.616L182.633 573.964L182.04 573.326L171.912 582.704L174.01 582.616Z" fill="url(#paint36_linear_292_86830)"/>
<path d="M219.37 551.519L213.307 558.237L213.951 558.819L220.014 552.1L219.37 551.519Z" fill="url(#paint37_linear_292_86830)"/>
<path d="M201.713 572.343L208.964 564.337L208.654 564.054L208.628 563.611L208.46 563.629L208.327 563.762L201.067 571.767L201.713 572.343Z" fill="url(#paint38_linear_292_86830)"/>
<path d="M224.318 546.091L222.362 548.249L223.005 548.832L224.961 546.673L224.318 546.091Z" fill="url(#paint39_linear_292_86830)"/>
<path d="M225.45 563.115L225.697 563.098L228.991 557.598L228.256 557.146L225.069 562.46L225.432 562.672L225.45 563.115Z" fill="url(#paint40_linear_292_86830)"/>
<path d="M232.887 551.107L234.852 547.83L234.109 547.387L232.143 550.655L232.887 551.107Z" fill="url(#paint41_linear_292_86830)"/>
<path d="M216.481 578.657L221.147 570.687L220.403 570.235L215.277 578.746L216.481 578.657Z" fill="url(#paint42_linear_292_86830)"/>
<path d="M247.398 546.812L248.221 544.801L247.424 544.474L246.601 546.484L247.398 546.812Z" fill="url(#paint43_linear_292_86830)"/>
<path d="M240.571 561.237L237.473 568.83L238.276 569.158L241.374 561.565L240.571 561.237Z" fill="url(#paint44_linear_292_86830)"/>
<path d="M243.121 556.659L243.405 556.642L246.078 550.071L245.273 549.743L242.705 556.066L243.095 556.225L243.121 556.659Z" fill="url(#paint45_linear_292_86830)"/>
<path d="M258.07 554.846L256.387 560.007L257.212 560.276L258.895 555.115L258.07 554.846Z" fill="url(#paint46_linear_292_86830)"/>
<path d="M260.926 548.937L261.98 545.66L261.156 545.395L260.103 548.671L260.926 548.937Z" fill="url(#paint47_linear_292_86830)"/>
<path d="M252.25 575.549L254.773 567.809L253.949 567.534L250.966 575.673L252.25 575.549Z" fill="url(#paint48_linear_292_86830)"/>
<path d="M274.428 553.861L275.667 547.795L274.817 547.626L273.586 553.693L274.428 553.861Z" fill="url(#paint49_linear_292_86830)"/>
<path d="M275.613 543.63L275.471 544.306L276.32 544.485L276.462 543.809L275.613 543.63Z" fill="url(#paint50_linear_292_86830)"/>
<path d="M272.557 558.785L271.058 566.17L271.908 566.343L273.408 558.957L272.557 558.785Z" fill="url(#paint51_linear_292_86830)"/>
<path d="M291.818 544.883L291.653 548.195L292.52 548.238L292.686 544.926L291.818 544.883Z" fill="url(#paint52_linear_292_86830)"/>
<path d="M290.737 566.255L290.356 573.862L291.224 573.906L291.604 566.299L290.737 566.255Z" fill="url(#paint53_linear_292_86830)"/>
<path d="M289.486 552.555L289.248 557.544L290.115 557.586L290.353 552.597L289.486 552.555Z" fill="url(#paint54_linear_292_86830)"/>
<path d="M435.728 530.403L435.27 531.14L442.608 535.704L443.067 534.967L435.728 530.403Z" fill="url(#paint55_linear_292_86830)"/>
<path d="M466.799 549.663L466.341 550.4L476.425 556.668L476.883 555.931L466.799 549.663Z" fill="url(#paint56_linear_292_86830)"/>
<path d="M450.437 539.509L449.979 540.246L456.693 544.422L457.151 543.685L450.437 539.509Z" fill="url(#paint57_linear_292_86830)"/>
<path d="M424.133 536.382L423.612 537.077L429.957 541.839L430.478 541.144L424.133 536.382Z" fill="url(#paint58_linear_292_86830)"/>
<path d="M419.264 533.749L419.432 533.741L419.406 533.298L419.654 532.961L419.397 532.766L418.883 533.466L419.264 533.749Z" fill="url(#paint59_linear_292_86830)"/>
<path d="M443.868 552.249L444.435 551.585L436.192 545.386L435.669 546.077L443.868 552.249Z" fill="url(#paint60_linear_292_86830)"/>
<path d="M404.698 534.289L404.149 534.962L408.034 538.128L408.582 537.456L404.698 534.289Z" fill="url(#paint61_linear_292_86830)"/>
<path d="M427.581 553.02L426.97 553.637L433.651 560.254L434.262 559.638L427.581 553.02Z" fill="url(#paint62_linear_292_86830)"/>
<path d="M414.547 542.333L413.999 543.006L419.326 547.345L419.874 546.672L414.547 542.333Z" fill="url(#paint63_linear_292_86830)"/>
<path d="M394.889 538.519L394.266 539.123L399.133 544.148L399.756 543.544L394.889 538.519Z" fill="url(#paint64_linear_292_86830)"/>
<path d="M410.136 555.508L410.755 554.897L410.304 554.437L404.08 548.025L403.46 548.627L410.136 555.508Z" fill="url(#paint65_linear_292_86830)"/>
<path d="M383.283 544.909L382.586 545.426L385.967 549.985L386.664 549.468L383.283 544.909Z" fill="url(#paint66_linear_292_86830)"/>
<path d="M391.649 556.279V556.261L390.782 556.323L391.649 556.279Z" fill="url(#paint67_linear_292_86830)"/>
<path d="M378.759 540.258L379.449 539.745L376.944 536.362L376.554 536.574L376.218 536.849L378.759 540.258Z" fill="url(#paint68_linear_292_86830)"/>
<path d="M364.399 539.55L363.653 540.002L363.713 540.101L364.459 539.649L364.399 539.55Z" fill="url(#paint69_linear_292_86830)"/>
<path d="M368.551 548.438L367.796 548.863L371.41 555.281L372.165 554.856L368.551 548.438Z" fill="url(#paint70_linear_292_86830)"/>
<path d="M365.354 546.741L366.107 546.325L363.123 541.002L362.371 541.427L365.354 546.741Z" fill="url(#paint71_linear_292_86830)"/>
<path d="M351.1 552.692L351.906 552.382L350.1 547.635L349.285 547.945L351.1 552.692Z" fill="url(#paint72_linear_292_86830)"/>
<path d="M347.195 542.49L348.01 542.189L346.655 538.638L345.876 539.01L345.938 539.143L347.195 542.49Z" fill="url(#paint73_linear_292_86830)"/>
<path d="M333.617 543.343L332.777 543.562L334.23 549.15L335.07 548.931L333.617 543.343Z" fill="url(#paint74_linear_292_86830)"/>
<path d="M332.719 539.793L331.879 540.011L331.928 540.2L332.768 539.982L332.719 539.793Z" fill="url(#paint75_linear_292_86830)"/>
<path d="M337.297 560.848L338.147 560.724L336.332 553.675L335.491 553.896L337.297 560.848Z" fill="url(#paint76_linear_292_86830)"/>
<path d="M318.269 540.935L317.403 540.999L317.669 544.594L318.534 544.53L318.269 540.935Z" fill="url(#paint77_linear_292_86830)"/>
<path d="M318.963 550.26L318.098 550.324L318.46 555.217L319.325 555.153L318.963 550.26Z" fill="url(#paint78_linear_292_86830)"/>
<path d="M303.444 542.015L302.577 542.026L302.58 542.3L303.448 542.29L303.444 542.015Z" fill="url(#paint79_linear_292_86830)"/>
<path d="M303.67 556.39L302.803 556.404L302.916 563.293L303.783 563.279L303.67 556.39Z" fill="url(#paint80_linear_292_86830)"/>
<path d="M303.458 545.647L302.59 545.66L302.677 551.487L303.544 551.474L303.458 545.647Z" fill="url(#paint81_linear_292_86830)"/>
<path d="M552.67 560.095L552.643 562.69L161.739 595.74L159.703 593.181L552.67 560.095Z" fill="url(#paint82_linear_292_86830)"/>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M194.241 590.232L190.921 580.836L160.801 583.43L166.626 592.366L194.241 590.232Z" fill="#FBF9FA"/>
</g>
<g style="mix-blend-mode:soft-light" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M204.44 593.712L206.52 600.682L167.91 603.799L165.023 597.051L204.44 593.712Z" fill="#FBF9FA"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M678.949 449.327C678.949 449.327 672.052 459.591 680.454 482.093C680.613 482.483 680.773 482.837 680.932 483.218C681.091 483.599 681.215 483.962 681.375 484.352C690.999 506.323 703.102 508.882 703.102 508.882C706.238 505.333 709.642 502.029 713.283 498.999C726.028 488.507 741.087 481.201 757.215 477.683L758.454 477.435C761.146 476.862 763.869 476.443 766.608 476.178H766.909C771.868 458.882 767.078 447.644 767.078 447.644C767.078 447.644 763.129 437.336 747.529 428.383L747.29 428.569C745.135 430.285 742.886 431.879 740.552 433.342C740.198 433.581 739.853 433.812 739.499 434.033C729.76 440.064 711.751 448.884 689.033 449.593C685.722 449.646 682.375 449.575 678.949 449.327Z" fill="url(#paint83_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M678.948 449.327C682.253 450.014 685.622 450.338 688.997 450.292C699.728 450.354 718.117 447.848 739.463 433.998C739.817 433.776 740.162 433.546 740.516 433.307C742.736 431.849 744.981 430.257 747.254 428.534L747.493 428.348C747.493 428.348 719.48 434.281 694.69 443.084C689.156 445.041 683.809 447.122 678.948 449.327Z" fill="url(#paint84_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M755.568 458.342L751.778 448.99L727.192 459.157L730.565 467.481L755.568 458.342Z" fill="#AC88BD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M712.442 498.601C705.881 504.348 703.092 508.9 703.092 508.9C708.254 507.04 713.717 504.738 719.144 502.24C742.969 491.259 766.874 476.187 766.874 476.187H766.573C763.74 476.532 761.03 476.957 758.418 477.444L757.179 477.692C734.983 482.093 720.702 491.277 712.442 498.601Z" fill="url(#paint85_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M753.239 457.846L731.707 465.896L729.184 459.688L750.486 451.045L753.239 457.846Z" fill="url(#paint86_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M747.652 479.986L742.916 462.965L741.428 463.514L746.528 480.313L747.652 479.986Z" fill="#AC88BD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M739.49 454.074L731.397 438.78L730.609 439.205L737.834 454.649L739.49 454.074Z" fill="#AC88BD"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M678.949 449.327C678.949 449.327 672.052 459.591 680.454 482.093C680.613 482.483 680.773 482.837 680.932 483.218C681.091 483.599 681.215 483.962 681.375 484.352C690.999 506.323 703.101 508.882 703.101 508.882C708.263 507.023 713.726 504.72 719.153 502.223L713.283 498.999L712.477 498.556L712.035 498.317C712.035 498.317 690.786 483.696 689.015 450.292C689.015 450.044 689.015 449.805 689.015 449.557L694.708 443.101C689.157 445.041 683.809 447.122 678.949 449.327Z" fill="#AC437C"/>
</g>
<path style="mix-blend-mode:screen" d="M202.194 607.4C227.908 605.656 248.255 596.88 247.639 587.799C247.023 578.718 225.678 572.77 199.964 574.515C174.249 576.259 153.902 585.035 154.518 594.116C155.134 603.197 176.479 609.145 202.194 607.4Z" fill="url(#paint87_radial_292_86830)"/>
<path style="mix-blend-mode:screen" d="M518.893 575.851C540.442 574.39 557.597 568.592 557.212 562.902C556.826 557.213 539.045 553.785 517.496 555.247C495.948 556.709 478.792 562.506 479.178 568.196C479.564 573.886 497.345 577.313 518.893 575.851Z" fill="url(#paint88_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M44.3845 427.409C49.313 429.345 54.6252 431.137 60.321 432.784L75.0445 393.651C75.0445 393.651 24.2868 400.443 34.6279 372.653C49.5993 332.422 184.068 319.235 184.068 319.235L183.333 301.435C183.333 301.435 86.3594 315.48 47.4921 333.732L46.7484 334.095C-16.7673 364.294 -11.827 405.269 44.3845 427.409Z" fill="url(#paint89_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M52.6631 393.81C52.6631 393.81 38.3822 405.889 45.8369 427.063L99.3304 458.386C99.3304 458.386 122.677 432.784 115.789 385.202L52.6631 393.81Z" fill="url(#paint90_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M115.789 385.246C115.789 385.246 75.3631 410.388 99.3298 458.431C99.3298 458.386 127.068 431.589 115.789 385.246Z" fill="url(#paint91_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M115.532 387.903C115.532 387.903 136.338 390.144 169.238 418.438C169.238 418.438 171.602 422.193 166.281 432.784L116.108 415.436C116.108 415.436 117.746 398.663 115.532 387.903Z" fill="#765287"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M169.238 418.438L162.828 420.209L116.55 396.759L115.355 386.451C115.355 386.451 138.197 390.303 169.238 418.438Z" fill="#9F86AB"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M109.636 390.073L50.7768 395.758C50.7768 395.758 44.9512 401.461 44.9512 406.385H97.1343C97.1343 406.385 101.836 395.422 109.636 390.073Z" fill="url(#paint92_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M39.8249 390.073C39.8249 390.073 17.4607 376.842 2.81689 372.202L4.70271 368.119C4.70271 368.119 20.046 371.821 35.4867 386.424L39.8249 390.073Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.6851 360.246C47.1399 351.866 48.4485 342.755 47.4921 333.741L50.0685 332.572C50.0685 332.572 52.7246 341.127 46.1641 358.148L43.6851 360.246Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.9023 343.226C73.9023 343.226 87.0322 335.787 90.0159 319.705L92.3798 319.111C92.3798 319.111 91.9902 327.923 78.3911 341.516L73.9023 343.226Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M123.527 328.932C131.729 324.147 138.276 316.973 142.297 308.369L138.941 308.998C134.448 316.837 128.505 323.75 121.429 329.366L123.527 328.932Z" fill="#401734"/>
</g>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M182.607 317.34C182.607 317.34 77.2494 326.559 39.8252 358.484C39.8252 358.484 70.742 320.865 182.111 311.416L182.607 317.34Z" fill="url(#paint93_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M169.238 303.64L160.066 322.335L184.077 319.235L183.342 301.435L169.238 303.64Z" fill="#5C1748"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M44.3846 427.409C45.27 427.196 49.9889 429.499 49.9889 429.499L45.8012 427.728L42.4279 410.574C42.4279 410.574 13.9459 407.156 7.96089 380.783C2.40083 356.341 40.7192 337.168 46.7574 334.131C-16.7672 364.294 -11.8269 405.269 44.3846 427.409Z" fill="#401734"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M45.3585 352.568C45.3585 352.568 39.825 363.293 34.5748 365.764C33.6983 365.029 34.2118 355.137 45.3585 352.568Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M156.984 375.124L240.261 369.634L235.56 470.333L232.275 471.644L229.584 375.124L148.29 379.1L156.984 375.124Z" fill="#8B0C42"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M232.276 471.644L229.584 375.124L141.402 371.91L157.224 466.215L225.29 464.825L232.276 471.644Z" fill="url(#paint94_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M202.873 416.454C202.873 417.519 203.189 418.56 203.78 419.446C204.372 420.331 205.212 421.021 206.196 421.429C207.18 421.836 208.262 421.943 209.306 421.735C210.35 421.527 211.31 421.015 212.062 420.262C212.815 419.508 213.328 418.549 213.536 417.505C213.743 416.46 213.637 415.378 213.229 414.394C212.822 413.41 212.132 412.569 211.247 411.977C210.361 411.386 209.321 411.07 208.256 411.07C206.828 411.07 205.459 411.637 204.45 412.647C203.44 413.657 202.873 415.026 202.873 416.454Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M185.414 416.454C185.414 417.519 185.73 418.56 186.321 419.446C186.913 420.331 187.753 421.021 188.737 421.429C189.721 421.836 190.803 421.943 191.847 421.735C192.891 421.527 193.851 421.015 194.603 420.262C195.356 419.508 195.869 418.549 196.077 417.505C196.284 416.46 196.178 415.378 195.77 414.394C195.363 413.41 194.673 412.569 193.788 411.977C192.902 411.386 191.862 411.07 190.797 411.07C189.369 411.07 188 411.637 186.991 412.647C185.981 413.657 185.414 415.026 185.414 416.454Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M168.787 416.454C168.787 417.519 169.102 418.56 169.694 419.446C170.285 420.331 171.126 421.021 172.11 421.429C173.093 421.836 174.176 421.943 175.22 421.735C176.264 421.527 177.223 421.015 177.976 420.262C178.729 419.508 179.241 418.549 179.449 417.505C179.657 416.46 179.55 415.378 179.143 414.394C178.735 413.41 178.045 412.569 177.16 411.977C176.275 411.386 175.234 411.07 174.17 411.07C172.742 411.07 171.373 411.637 170.363 412.647C169.354 413.657 168.787 415.026 168.787 416.454Z" fill="white"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M157.224 466.215L170.77 452.816L155.506 449.504L157.224 466.215Z" fill="#5C1748"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99.0996 453.861L99.4892 447.538L167.804 448.123L170.77 452.816L166.236 450.664C166.236 450.664 136.311 457.173 99.0996 453.861Z" fill="#9F86AB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M167.804 436.132C167.804 436.132 124.563 432.315 99.4896 418.783C99.4896 418.783 92.6988 428.1 97.4001 446.909C97.4001 446.909 117.976 454.623 167.804 448.123V436.132Z" fill="url(#paint95_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99.0996 453.861C99.0996 453.861 132.38 456.128 166.236 450.664L170.77 452.816C170.77 452.816 133.23 460.494 100.153 457.961L99.0996 453.861Z" fill="url(#paint96_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99.4897 418.783L109.556 416.932L166.91 433.865L167.795 436.132C167.795 436.132 123.634 434.981 99.4897 418.783Z" fill="#9F86AB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M240.262 369.634L229.584 375.124L141.402 371.91L151.664 369.634H240.262Z" fill="#FF6842"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M572.75 324.885C572.75 324.885 623.499 337.832 665.863 327.4C669.5 326.512 673.085 325.424 676.603 324.141C694.204 317.756 709.255 306.332 716.913 286.947C738.277 232.82 708.059 176.143 694.832 155.367C691.486 150.054 689.237 147.131 689.237 147.131C689.237 147.131 688.723 147.078 687.838 147.061C683.987 147.008 673.15 148.106 666.51 160.84C666.51 160.84 669.927 164.71 674.69 171.379C689.166 191.605 715.939 237.629 696.461 279.11C672.831 329.508 573.299 306.74 573.299 306.74L572.75 324.885Z" fill="url(#paint97_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M660.382 107.9L619.868 82.7232L612.219 89.1614L636.301 125.78L660.382 107.9Z" fill="url(#paint98_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M655.026 107.9L613.529 84.7512L615.3 80.3233C615.3 80.3233 638.39 79.7299 665.881 103.765L655.026 107.9Z" fill="#9F86AB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M676.461 101.763C676.461 101.763 657.142 82.5018 623.339 79.1543L615.327 80.341C615.327 80.341 640.462 82.1122 665.907 103.782L676.461 101.763Z" fill="url(#paint99_radial_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M597.097 310.477C597.097 310.477 620.117 314.179 636.053 331.332L633.229 331.465C633.229 331.465 622.976 319.155 592.998 309.972L597.097 310.477Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M653.079 308.892C653.079 308.892 673.973 303.844 697.727 312.726L695.824 314.178C695.824 314.178 676.585 308.121 650.529 309.405L653.079 308.892Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M689.98 289.214C689.98 289.214 704.863 272.946 721.561 272.087L722.402 268.093C710.949 270.269 700.583 276.297 693.026 285.176L689.98 289.214Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M722.774 226.727C722.774 226.727 711.344 226.657 703.296 248.283L703.189 243.323C703.189 243.323 708.634 227.896 722.384 224.345L722.774 226.727Z" fill="#401734"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M701.224 166.171C701.224 166.171 691.883 178.685 693.256 204.03L691.264 199.452C691.264 199.452 692.149 175.612 699.347 162.824L701.224 166.171Z" fill="#401734"/>
</g>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M588.305 319.226C588.305 319.226 650.794 331.093 689.024 302.675C724.5 276.302 707.767 217.916 698.957 203.242C698.957 203.242 717.497 257.608 690.803 290.454C661.808 326.134 588.305 313.851 588.305 313.851V319.226Z" fill="url(#paint100_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M595.105 329.03L585.038 308.892L573.334 306.74L572.758 324.894L595.105 329.03Z" fill="#5C1748"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M703.208 273.725C703.208 273.725 706.085 286.238 694.876 291.933C694.876 291.933 695.868 283.83 703.208 273.725Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M666.155 327.347L666.979 327.188L666.155 327.347Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M587.739 132.555L679.321 91.331L635.398 0.169373L544.046 42.2256L587.739 132.555Z" fill="url(#paint101_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M635.398 0.169373H645.872L681.631 90.3392L679.321 91.3311L635.398 0.169373Z" fill="url(#paint102_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M572.75 55.1108L598.7 102.649L649.165 80.5092L628.537 29.3404L572.75 55.1108ZM575.743 56.7668L627.979 33.7417L644.871 78.9063L600.223 98.3891L575.743 56.7668Z" fill="#FF6154"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M644.889 78.9241L641.418 69.6255L617.328 58.2723L615.823 77.2592L606.323 76.2762L600.24 98.3803L644.889 78.9241Z" fill="#FF6154"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M592.077 60.0701C592.077 61.17 592.403 62.2453 593.014 63.1598C593.625 64.0744 594.494 64.7872 595.509 65.2082C596.525 65.6291 597.643 65.7392 598.722 65.5246C599.8 65.31 600.791 64.7804 601.569 64.0026C602.346 63.2248 602.876 62.2339 603.09 61.155C603.305 60.0762 603.195 58.958 602.774 57.9418C602.353 56.9256 601.641 56.057 600.726 55.4459C599.812 54.8348 598.737 54.5086 597.637 54.5086C596.163 54.5086 594.748 55.0945 593.706 56.1375C592.663 57.1805 592.077 58.5951 592.077 60.0701Z" fill="#FF6154"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M681.631 90.3392L677.736 88.0367L587.739 132.555H594.166L681.631 90.3392Z" fill="#8B0C42"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M633.707 127.348C633.707 127.348 619.213 111.903 609.164 84.7512C609.164 84.7512 603.374 85.4951 600.807 93.607C600.807 93.607 608.102 127.188 625.889 147.025L633.707 127.348Z" fill="url(#paint103_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M633.707 127.348L639.568 119.617L613.529 84.7512H609.165C609.165 84.7512 619.205 112.886 633.707 127.348Z" fill="#9F86AB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M625.889 147.025L674.726 171.343C679.781 161.85 686.359 157.962 690.609 156.412C691.968 155.865 693.394 155.502 694.85 155.332L676.434 101.754C631.591 106.891 625.889 147.025 625.889 147.025Z" fill="url(#paint104_radial_292_86830)"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M679.639 164.87L631.75 129.544C629.659 134.346 627.884 139.281 626.438 144.315L676.018 169.333L679.639 164.87Z" fill="url(#paint105_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M687.448 157.856C692.362 166.207 725.031 218.226 717.55 266.304C711.999 302.152 690.502 318.553 676.62 324.15C694.221 317.765 709.272 306.341 716.93 286.956C738.294 232.829 708.077 176.152 694.85 155.376L676.434 101.799L687.873 147.025L689.98 155.35L687.448 157.856Z" fill="#401734"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M694.876 155.359C694.876 155.359 681.596 160.805 674.752 171.379C674.752 171.379 677.665 157.838 694.876 155.359Z" fill="url(#paint106_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M650.484 105.368L613.529 84.7512L634.689 113.187L650.484 105.368Z" fill="#5C1748"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.2">
<path fill-rule="evenodd" clip-rule="evenodd" d="M574.192 279.712L575.928 212.496L497.122 213.045L487.286 214.533L488.454 280.757L574.192 279.712Z" fill="#401734"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M625.96 197.893C625.96 197.893 561.098 143.713 554.573 142.686C548.048 141.659 492.235 197.016 492.235 197.016L557.22 238.391L625.96 197.893Z" fill="url(#paint107_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M492.235 197.016L625.96 197.893L557.167 236.504L492.235 197.016Z" fill="#D9D0E2"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M622.4 262.877L625.96 197.893L559.806 234.884L492.235 197.016L493.404 263.063L622.4 262.877Z" fill="url(#paint108_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M615.008 258.077C615.008 258.077 586.924 232.643 567.721 230.624L615.008 258.077Z" fill="#D9D0E2"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M548.287 231.102C548.287 231.102 515.325 243.872 499.176 259.308L548.287 231.102Z" fill="#D9D0E2"/>
</g>
<g style="mix-blend-mode:multiply" opacity="0.5">
<path fill-rule="evenodd" clip-rule="evenodd" d="M620.948 200.691L573.05 217.101L548.128 217.261L492.235 197.016L550.854 230.031L567.703 230.597L620.948 200.691Z" fill="#D9D0E2"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M622.401 262.877C622.401 262.877 570.997 228.959 559.611 227.843C548.225 226.727 493.404 263.036 493.404 263.036L622.401 262.877Z" fill="url(#paint109_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M217.844 149.815L224.467 153.269L231.665 148.38L257.004 171.396L275.136 161.23L292.374 179.818L315.411 163.391L294.322 172.326L272.745 155.385H252.515L241.722 137.7L249.744 129.792L247.769 124.886L217.844 149.815Z" fill="url(#paint110_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M339.528 190.87H225.644L224.918 186.69L339.528 188.444V190.87Z" fill="#B8A9D4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M339.528 190.87L343.627 98.6282H339.528L337.784 190.87H339.528Z" fill="#B8A9D4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M334.278 173.141L315.411 158.157L292.781 168.979L275.862 148.221L260.775 155.483L239.208 132.945L247.769 124.886L219.925 124.044L217.844 149.815L228.477 143.262L257.473 168.979L274.941 158.334L292.418 177.471L315.411 163.391L334.278 176.542V173.141Z" fill="url(#paint111_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M395.935 155.589C395.935 155.589 453.28 108.759 424.762 83.1748C421.264 80.0107 416.987 77.8352 412.371 76.8718C407.754 75.9084 402.964 76.1919 398.493 77.6931C362.769 89.675 395.935 155.589 395.935 155.589ZM397.493 97.6984C398.653 91.579 404.177 87.4965 409.808 88.5592C415.439 89.6219 419.122 95.4401 417.962 101.568C416.803 107.697 411.287 111.77 405.629 110.716C399.972 109.663 396.324 103.827 397.493 97.6984Z" fill="#79448A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M397.741 146.042C397.741 146.042 453.82 105.908 424.86 82.6613C421.212 79.7584 416.933 77.7552 412.368 76.8136C407.803 75.872 403.08 76.0186 398.582 77.2415C363.265 87.0537 397.741 146.042 397.741 146.042ZM398.024 94.9088C398.589 92.2743 400.177 89.9719 402.438 88.5073C404.699 87.0427 407.448 86.5358 410.083 87.098C415.74 88.1607 419.512 93.3856 418.494 98.77C417.476 104.154 412.057 107.626 406.409 106.581C400.76 105.536 397.006 100.293 398.024 94.9088Z" fill="url(#paint112_radial_292_86830)"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M395.935 155.589L397.705 146.042L387.966 125.116L385.744 129.101C385.744 129.101 392.207 148.548 395.935 155.589Z" fill="url(#paint113_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.6992 56.0253L83.3779 110.046L148.399 77.0755L121.254 16.4664L50.6992 56.0253Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M83.3779 110.063L91.6472 110.816L144.273 83.1151L148.399 77.0932L83.3779 110.063Z" fill="url(#paint114_radial_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M78.0571 59.6561L99.5802 90.4566L118.819 55.7596L78.0571 59.6561Z" fill="#5C1748"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77.3042 58.6377L96.2155 84.4524L113.533 50.7118L77.3042 58.6377Z" fill="url(#paint115_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M113.533 50.7118L114.525 58.1684L98.8094 84.9218L96.2153 84.4524L113.533 50.7118Z" fill="url(#paint116_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.5045 488.771L7.8457 544.235L68.2007 556.633L82.8269 505.269L16.5045 488.771Z" fill="url(#paint117_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M68.2185 556.659L61.6403 559.112L12.5648 548.229L7.86353 544.235L68.2185 556.659Z" fill="url(#paint118_radial_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M60.0551 517.508L51.5467 514.718L46.4736 545.395L51.4494 546.643L60.0551 517.508Z" fill="#5C1748"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M54.2738 543.172L63.6409 515.011L54.805 511.716L48.8643 541.817L54.2738 543.172Z" fill="url(#paint119_linear_292_86830)"/>
<g style="mix-blend-mode:multiply" opacity="0.3">
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.2409 512.558L32.2727 513.089L29.2979 542.818L34.2736 544.058L40.2409 512.558Z" fill="#5C1748"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0629 538.877L41.534 508.404L33.6542 507.536L31.7861 537.557L37.0629 538.877Z" fill="url(#paint120_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0629 538.877L35.7171 540.622L32.158 539.727L31.7861 537.557L37.0629 538.877Z" fill="url(#paint121_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M54.2742 543.172L51.4499 544.545L48.7495 543.872L48.8646 541.817L54.2742 543.172Z" fill="url(#paint122_linear_292_86830)"/>
<path d="M280.368 63.0102C295.852 54.4543 314.664 46.2039 322.135 43.1483C341.49 44.6761 347.602 49.7689 351.677 52.3153C321.456 59.4452 264.884 71.5661 280.368 63.0102Z" fill="#F2B60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M319.537 44.2401C319.537 44.2401 339.794 45.8165 347.452 51.6436C355.11 57.4707 362.919 73.73 362.919 73.73C362.919 73.73 381.627 45.6836 377.262 36.757C372.897 27.8303 326.708 41.5834 319.537 44.2401Z" fill="url(#paint123_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M283.462 221.444C302.656 248.84 301.435 282.731 280.885 297.103C260.334 311.476 228.105 300.914 208.951 273.494C199.376 259.79 194.87 244.466 195.341 230.778L196.069 233.977L254.125 220.778L247.983 193.984C261.045 198.246 273.887 207.739 283.462 221.444Z" fill="#C6D6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M282.508 222.132C301.165 248.833 300.047 281.841 279.999 295.856C259.95 309.871 228.568 299.564 209.908 272.853C200.583 259.495 196.202 244.57 196.652 231.231L197.222 233.735L253.788 220.872L247.986 195.354C260.668 199.498 273.176 208.753 282.508 222.132Z" fill="#C6D7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M281.551 222.774C299.719 248.776 298.624 280.915 279.096 294.555C259.569 308.194 229.034 298.178 210.86 272.154C201.778 259.158 197.528 244.616 197.946 231.63L198.362 233.45L253.451 220.965L247.933 196.694C260.271 200.721 272.466 209.767 281.551 222.774Z" fill="#C7D8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M280.59 223.44C298.267 248.745 297.207 280.011 278.214 293.282C259.221 306.554 229.49 296.807 211.809 271.491C194.128 246.175 195.203 214.917 214.196 201.645L220.298 228.464L253.097 221.005L247.882 198.081C259.94 202.006 271.758 210.791 280.59 223.44Z" fill="#C7D9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M279.64 224.103C296.784 248.724 295.79 279.106 277.321 292.013C258.853 304.92 229.959 295.432 212.814 270.811C195.669 246.19 196.663 215.808 215.125 202.915L220.931 228.372L252.828 221.125L247.909 199.49C259.534 203.314 271.047 211.805 279.64 224.103Z" fill="#C8DBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M278.682 224.781C295.374 248.669 294.374 278.202 276.44 290.741C258.506 303.28 230.43 294.067 213.722 270.161C197.013 246.255 198.037 216.725 215.975 204.197L221.437 228.214L252.42 221.181L247.763 200.836C259.141 204.548 270.34 212.83 278.682 224.781Z" fill="#C9DCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M277.732 225.444C293.933 248.634 292.957 277.298 275.547 289.472C258.136 301.646 230.887 292.695 214.682 269.494C198.477 246.293 199.453 217.63 216.867 205.466L222.024 228.09L252.087 221.25L247.762 202.195C258.767 205.811 269.629 213.843 277.732 225.444Z" fill="#C9DDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M276.77 226.111C292.482 248.603 291.54 276.393 274.657 288.214C257.774 300.035 231.362 291.342 215.636 268.842C199.91 246.342 200.877 218.556 217.749 206.739L222.572 227.954L251.743 221.322L247.693 203.563C258.383 207.077 268.911 214.871 276.77 226.111Z" fill="#CADEFF"/>
<path d="M273.762 286.941C290.116 275.514 291.033 248.587 275.811 226.799C260.589 205.012 234.992 196.613 218.638 208.041C202.285 219.468 201.367 246.395 216.589 268.183C231.812 289.97 257.409 298.369 273.762 286.941Z" fill="#CADFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M274.863 227.451C289.591 248.537 288.707 274.584 272.876 285.651C257.045 296.717 232.284 288.584 217.584 267.477C202.884 246.37 203.736 220.333 219.56 209.281C235.383 198.228 260.123 206.368 274.863 227.451Z" fill="#CBE0FF"/>
<path d="M271.994 284.385C287.291 273.695 288.147 248.506 273.906 228.122C259.665 207.739 235.719 199.88 220.422 210.569C205.125 221.259 204.269 246.448 218.51 266.832C232.751 287.215 256.696 295.074 271.994 284.385Z" fill="#CCE1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M272.951 228.781C286.697 248.461 285.874 272.775 271.098 283.099C256.322 293.422 233.209 285.838 219.46 266.147C205.71 246.456 206.537 222.152 221.313 211.829C236.088 201.505 259.205 209.101 272.951 228.781Z" fill="#CCE2FF"/>
<path d="M270.211 281.833C284.456 271.878 285.258 248.427 272.002 229.452C258.745 210.478 236.45 203.166 222.205 213.121C207.959 223.076 207.157 246.527 220.414 265.501C233.67 284.476 255.965 291.788 270.211 281.833Z" fill="#CDE3FF"/>
<path d="M269.316 280.57C283.03 270.986 283.798 248.402 271.03 230.127C258.263 211.853 236.794 204.807 223.079 214.391C209.365 223.975 208.597 246.558 221.365 264.833C234.132 283.108 255.601 290.153 269.316 280.57Z" fill="#CDE4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M270.1 230.735C282.381 248.297 281.646 270.02 268.449 279.235C255.253 288.451 234.614 281.677 222.373 264.091C210.131 246.504 210.838 224.802 224.024 215.59C237.209 206.378 257.808 213.212 270.1 230.735Z" fill="#CEE5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M269.142 231.448C280.923 248.315 280.221 269.165 267.563 278.023C254.904 286.882 235.08 280.373 223.297 263.495C211.513 246.616 212.221 225.788 224.876 216.919C237.531 208.05 257.365 214.626 269.142 231.448Z" fill="#CEE6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.17 232.118C279.471 248.284 278.794 268.264 266.652 276.737C254.511 285.209 235.566 278.981 224.223 262.792C212.879 246.603 213.613 226.653 225.751 218.17C237.889 209.687 256.883 215.959 268.17 232.118Z" fill="#CFE7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M267.208 232.82C278.006 248.281 277.362 267.388 265.745 275.496C254.127 283.603 235.993 277.701 225.176 262.175C214.358 246.649 215.022 227.607 226.625 219.492C238.228 211.377 256.444 217.277 267.208 232.82Z" fill="#D0E8FF"/>
<path d="M264.876 274.196C275.957 266.453 276.582 248.213 266.272 233.456C255.962 218.7 238.622 213.014 227.541 220.758C216.46 228.501 215.834 246.741 226.144 261.497C236.454 276.254 253.795 281.939 264.876 274.196Z" fill="#D0E9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M265.312 234.121C275.126 248.177 274.52 265.511 263.985 272.926C253.45 280.341 236.92 274.883 227.124 260.81C217.327 246.738 217.905 229.424 228.443 222.02C238.981 214.615 255.486 220.07 265.312 234.121Z" fill="#D1EAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M264.35 234.788C273.686 248.142 273.116 264.65 263.103 271.654C253.09 278.658 237.388 273.508 228.052 260.154C218.717 246.8 219.29 230.303 229.314 223.296C239.338 216.288 255.026 221.431 264.35 234.788Z" fill="#D1EBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M263.4 235.451C272.218 248.057 271.699 263.745 262.207 270.374C252.714 277.003 237.845 272.136 229.01 259.476C220.175 246.817 220.703 231.196 230.203 224.554C239.703 217.911 254.557 222.806 263.4 235.451Z" fill="#D2ECFF"/>
<path d="M259.062 269.39C268.003 263.156 268.521 248.446 260.218 236.534C251.915 224.621 237.936 220.017 228.994 226.25C220.053 232.483 219.536 247.193 227.839 259.106C236.142 271.018 250.121 275.623 259.062 269.39Z" fill="#D3EDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M261.492 236.792C269.341 248.034 268.867 261.936 260.436 267.843C252.005 273.75 238.777 269.411 230.925 258.157C223.073 246.904 223.537 233.005 231.982 227.106C240.427 221.206 253.633 225.552 261.492 236.792Z" fill="#D3EEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M260.531 237.458C267.901 248 267.45 261.032 259.539 266.564C251.629 272.095 239.242 268.025 231.893 257.477C224.545 246.929 224.975 233.903 232.888 228.382C240.802 222.861 253.172 226.913 260.531 237.458Z" fill="#D4EFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M259.581 238.121C266.45 247.969 266.033 260.127 258.625 265.301C251.218 270.475 239.681 266.671 232.801 256.827C225.921 246.983 226.338 234.824 233.76 229.658C241.181 224.491 252.704 228.288 259.581 238.121Z" fill="#D4F0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M258.623 238.799C265.021 247.931 264.616 259.223 257.761 264.012C250.907 268.8 240.17 265.289 233.78 256.143C227.39 246.997 227.783 235.708 234.638 230.92C241.493 226.131 252.236 229.664 258.623 238.799Z" fill="#D5F1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M257.673 239.462C263.556 247.892 263.2 258.319 256.869 262.742C250.538 267.166 240.639 263.914 234.741 255.476C228.844 247.039 229.2 236.612 235.531 232.188C241.862 227.765 251.776 231.024 257.673 239.462Z" fill="#D6F2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M256.712 240.129C262.126 247.89 261.793 257.447 255.987 261.47C250.181 265.494 241.106 262.574 235.703 254.81C230.299 247.045 230.61 237.495 236.427 233.468C242.244 229.441 251.308 232.4 256.712 240.129Z" fill="#D6F3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M255.761 240.792C260.665 247.826 260.366 256.51 255.094 260.201C249.822 263.892 241.567 261.178 236.663 254.143C231.76 247.109 232.047 238.429 237.323 234.748C242.599 231.067 250.85 233.771 255.761 240.792Z" fill="#D7F4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M254.79 241.426C259.211 247.749 258.937 255.562 254.199 258.886C249.461 262.209 242.011 259.763 237.601 253.437C233.19 247.111 233.44 239.293 238.192 235.977C242.944 232.661 250.379 235.135 254.79 241.426Z" fill="#D7F5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M253.842 242.136C257.774 247.761 257.533 254.701 253.32 257.66C249.106 260.619 242.496 258.442 238.564 252.817C234.633 247.192 234.87 240.241 239.087 237.293C243.304 234.345 249.922 236.507 253.842 242.136Z" fill="#D8F6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M252.892 242.799C256.33 247.715 256.116 253.797 252.412 256.383C248.708 258.97 242.942 257.074 239.511 252.143C236.08 247.212 236.276 241.149 239.99 238.558C243.705 235.968 249.453 237.882 252.892 242.799Z" fill="#D8F7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M251.941 243.498C254.89 247.716 254.71 252.924 251.551 255.14C248.393 257.355 243.463 255.717 240.482 251.509C237.501 247.3 237.713 242.082 240.886 239.874C244.058 237.665 248.996 239.254 251.941 243.498Z" fill="#D9F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M250.984 244.139C253.439 247.65 253.286 251.998 250.648 253.839C248.011 255.679 243.878 254.323 241.433 250.81C238.988 247.296 239.12 242.954 241.757 241.114C244.395 239.273 248.525 240.618 250.984 244.139Z" fill="#DAF9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M250.023 244.806C251.988 247.618 251.87 251.094 249.767 252.566C247.663 254.039 244.349 252.959 242.384 250.147C240.418 247.334 240.536 243.859 242.65 242.383C244.764 240.907 248.057 241.993 250.023 244.806Z" fill="#DAFAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M249.072 245.469C250.545 247.573 250.453 250.19 248.87 251.287C247.288 252.384 244.817 251.584 243.341 249.469C241.865 247.354 241.953 244.763 243.532 243.655C245.111 242.547 247.6 243.365 249.072 245.469Z" fill="#DBFBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M248.112 246.136C249.095 247.542 249.037 249.285 247.978 250.018C246.92 250.75 245.275 250.212 244.292 248.806C243.309 247.4 243.37 245.667 244.426 244.924C245.481 244.181 247.129 244.729 248.112 246.136Z" fill="#DBFCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M247.165 246.81C247.238 247.024 247.251 247.255 247.201 247.476C247.151 247.697 247.04 247.9 246.881 248.061C246.722 248.223 246.52 248.337 246.3 248.39C246.08 248.443 245.849 248.434 245.633 248.363C245.418 248.293 245.227 248.163 245.081 247.989C244.935 247.816 244.841 247.605 244.808 247.38C244.776 247.156 244.807 246.927 244.897 246.719C244.988 246.511 245.135 246.333 245.321 246.204C245.842 245.828 246.671 246.101 247.165 246.81Z" fill="#DCFDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M245.976 254.035C236.316 264.6 217.611 263.253 204.154 250.946C190.696 238.638 187.645 220.16 197.304 209.595C206.964 199.031 225.669 200.377 239.122 212.677C252.575 224.977 255.631 243.463 245.976 254.035Z" fill="#F2950D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M245.497 253.596C236.028 263.959 217.676 262.603 204.5 250.572C191.325 238.541 188.317 220.385 197.778 210.027C207.24 199.669 225.612 201.023 238.787 213.054C251.962 225.085 254.966 243.233 245.497 253.596Z" fill="#F2960D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M245.022 253.164C235.739 263.317 217.749 261.988 204.839 250.203C191.928 238.417 188.981 220.615 198.257 210.466C207.532 200.317 225.525 201.635 238.44 213.428C251.355 225.221 254.294 243.008 245.022 253.164Z" fill="#F2970D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M244.544 252.725C235.451 262.676 217.836 261.376 205.181 249.821C192.526 238.266 189.646 220.844 198.743 210.901C207.84 200.957 225.451 202.25 238.094 213.802C250.736 225.353 253.629 242.778 244.544 252.725Z" fill="#F2980D"/>
<path d="M205.519 249.449C217.905 260.758 235.158 262.032 244.056 252.295C252.954 242.557 250.127 225.495 237.742 214.186C225.357 202.877 208.103 201.603 199.205 211.34C190.307 221.078 193.134 238.14 205.519 249.449Z" fill="#F2990D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M243.591 251.854C234.882 261.39 217.992 260.141 205.883 249.089C193.774 238.037 190.991 221.315 199.681 211.781C208.37 202.246 225.272 203.499 237.388 214.546C249.505 225.594 252.285 242.328 243.591 251.854Z" fill="#F29A0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M243.113 251.415C234.589 260.741 218.067 259.525 206.209 248.696C194.352 237.867 191.647 221.529 200.17 212.203C208.694 202.877 225.209 204.097 237.066 214.927C248.924 225.756 251.628 242.093 243.113 251.415Z" fill="#F29B0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M242.661 251.022C234.323 260.138 218.175 258.952 206.569 248.345C194.963 237.739 192.338 221.805 200.662 212.666C208.987 203.526 225.14 204.74 236.746 215.346C248.352 225.953 250.987 241.85 242.661 251.022Z" fill="#F29C0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M242.16 250.544C234.013 259.459 218.227 258.298 206.91 247.944C195.593 237.59 192.991 221.98 201.1 213.087C209.209 204.195 225.03 205.346 236.354 215.695C247.679 226.045 250.299 241.634 242.16 250.544Z" fill="#F29D0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M241.678 250.117C233.715 258.802 218.309 257.678 207.236 247.571C196.164 237.464 193.625 222.227 201.601 213.513C209.578 204.799 224.971 205.952 236.038 216.051C247.106 226.151 249.635 241.404 241.678 250.117Z" fill="#F29E0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M241.192 249.682C233.436 258.177 218.376 257.067 207.576 247.202C196.776 237.336 194.305 222.448 202.073 213.957C209.841 205.466 224.884 206.564 235.689 216.437C246.493 226.311 248.963 241.179 241.192 249.682Z" fill="#F29F0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M240.725 249.246C233.155 257.531 218.465 256.443 207.93 246.824C197.395 237.204 195 222.66 202.555 214.384C210.109 206.108 224.812 207.148 235.358 216.802C245.903 226.457 248.299 240.949 240.725 249.246Z" fill="#F2A00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M240.247 248.807C232.867 256.89 218.551 255.831 208.264 246.447C197.977 237.062 195.68 222.881 203.033 214.824C210.387 206.766 224.736 207.795 235.011 217.176C245.286 226.558 247.634 240.719 240.247 248.807Z" fill="#F2A10D"/>
<path d="M208.602 246.076C218.615 255.219 232.565 256.247 239.76 248.372C246.956 240.498 244.672 226.702 234.659 217.559C224.646 208.416 210.696 207.388 203.501 215.263C196.305 223.137 198.589 236.933 208.602 246.076Z" fill="#F2A20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M239.294 247.936C232.285 255.6 218.707 254.596 208.957 245.699C199.207 236.802 196.978 223.359 203.986 215.694C210.994 208.03 224.58 209.03 234.322 217.932C244.064 226.833 246.297 240.264 239.294 247.936Z" fill="#F2A30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M238.815 247.497C231.997 254.959 218.768 253.958 209.3 245.317C199.831 236.677 197.642 223.588 204.46 216.126C211.278 208.664 224.507 209.665 233.983 218.301C243.459 226.937 245.633 240.035 238.815 247.497Z" fill="#F2A40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M238.341 247.065C231.716 254.314 218.868 253.369 209.639 244.948C200.409 236.527 198.314 223.814 204.939 216.565C211.563 209.317 224.419 210.257 233.641 218.683C242.863 227.108 244.968 239.805 238.341 247.065Z" fill="#F2A50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.866 246.634C231.432 253.68 218.916 252.779 209.99 244.582C201.063 236.384 198.983 224.051 205.422 217.012C211.86 209.973 224.372 210.867 233.299 219.064C242.225 227.261 244.296 239.579 237.866 246.634Z" fill="#F2A60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.388 246.194C231.117 253.045 219.024 252.134 210.327 244.193C201.631 236.251 199.643 224.273 205.892 217.436C212.14 210.599 224.255 211.497 232.932 219.439C241.609 227.382 243.632 239.35 237.388 246.194Z" fill="#F2A70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M236.886 245.768C230.828 252.404 219.075 251.532 210.643 243.836C202.211 236.141 200.285 224.516 206.347 217.889C212.409 211.261 224.154 212.117 232.59 219.821C241.027 227.524 242.967 239.12 236.886 245.768Z" fill="#F2A80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M236.431 245.316C230.572 251.765 219.2 250.898 211.013 243.449C202.826 236.001 200.971 224.713 206.822 218.269C212.674 211.824 224.057 212.694 232.245 220.143C240.432 227.591 242.303 238.89 236.431 245.316Z" fill="#F2A90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M235.956 244.884C230.27 251.101 219.259 250.291 211.378 243.054C203.497 235.818 201.667 224.945 207.346 218.733C213.025 212.521 224.044 213.326 231.924 220.563C239.805 227.799 241.631 238.665 235.956 244.884Z" fill="#F2AA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M235.478 244.445C229.982 250.46 219.341 249.672 211.702 242.694C204.063 235.716 202.309 225.188 207.805 219.173C213.301 213.159 223.934 213.951 231.586 220.932C239.237 227.913 240.966 238.435 235.478 244.445Z" fill="#F2AB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M235.004 244.014C229.701 249.814 219.415 249.056 212.041 242.324C204.666 235.593 202.981 225.413 208.284 219.613C213.586 213.812 223.86 214.567 231.239 221.306C238.618 228.045 240.302 238.205 235.004 244.014Z" fill="#F2AC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M234.525 243.574C229.413 249.173 219.502 248.444 212.383 241.943C205.264 235.442 203.646 225.643 208.762 220.052C213.879 214.461 223.778 215.186 230.897 221.688C238.016 228.189 239.634 237.988 234.525 243.574Z" fill="#F2AD0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M234.051 243.143C229.124 248.532 219.576 247.829 212.699 241.587C205.822 235.345 204.279 225.89 209.206 220.501C214.132 215.112 223.673 215.819 230.558 222.057C237.442 228.295 238.97 237.758 234.051 243.143Z" fill="#F2AF0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M233.572 242.703C228.836 247.891 219.658 247.209 213.064 241.192C206.47 235.175 204.974 226.102 209.715 220.923C214.456 215.743 223.622 216.421 230.211 222.431C236.8 228.44 238.274 237.546 233.572 242.703Z" fill="#F2B00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M233.072 242.246C228.53 247.219 219.718 246.571 213.397 240.795C207.076 235.019 205.633 226.305 210.176 221.331C214.718 216.358 223.53 217.006 229.856 222.789C236.181 228.573 237.64 237.298 233.072 242.246Z" fill="#F2B10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M232.581 241.855C228.186 246.641 219.783 245.992 213.731 240.47C207.678 234.947 206.285 226.583 210.637 221.811C214.989 217.04 223.431 217.666 229.487 223.196C235.544 228.726 236.976 237.069 232.581 241.855Z" fill="#F2B20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M232.133 241.398C227.974 245.955 219.906 245.389 214.099 240.063C208.293 234.736 206.98 226.795 211.142 222.225C215.304 217.655 223.369 218.233 229.184 223.556C234.998 228.878 236.304 236.843 232.133 241.398Z" fill="#F2B30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M231.681 241.005C227.703 245.345 219.996 244.785 214.447 239.709C208.897 234.632 207.649 227.032 211.629 222.68C215.61 218.327 223.314 218.899 228.863 223.976C234.413 229.052 235.639 236.614 231.681 241.005Z" fill="#F2B40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M231.18 240.527C227.428 244.655 220.056 244.127 214.785 239.319C209.513 234.512 208.312 227.242 212.099 223.104C215.887 218.965 223.231 219.499 228.495 224.311C233.759 229.123 234.975 236.384 231.18 240.527Z" fill="#F2B50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M230.706 240.095C227.109 244.032 220.131 243.512 215.127 238.938C210.124 234.364 208.977 227.472 212.574 223.535C216.171 219.598 223.106 220.133 228.138 224.65C233.17 229.166 234.303 236.158 230.706 240.095Z" fill="#F2B60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M230.227 239.656C226.816 243.383 220.217 242.9 215.466 238.569C210.715 234.237 209.641 227.702 213.052 223.974C216.463 220.247 223.071 220.726 227.814 225.062C232.557 229.398 233.639 235.929 230.227 239.656Z" fill="#F2B70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M229.749 239.217C226.528 242.742 220.276 242.293 215.813 238.195C211.349 234.096 210.313 227.927 213.526 224.406C216.74 220.885 223.004 221.337 227.429 225.458C231.854 229.579 232.974 235.699 229.749 239.217Z" fill="#F2B80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M229.274 238.785C226.247 242.097 220.373 241.665 216.155 237.813C211.936 233.962 210.978 228.157 214.005 224.845C217.032 221.534 222.914 221.961 227.12 225.81C231.326 229.658 232.302 235.474 229.274 238.785Z" fill="#F2B90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M228.796 238.346C225.959 241.456 220.447 241.049 216.502 237.439C212.556 233.829 211.642 228.387 214.497 225.308C217.352 222.228 222.846 222.604 226.804 226.217C230.762 229.831 231.638 235.244 228.796 238.346Z" fill="#F2BA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M228.322 237.914C225.67 240.815 220.564 240.42 216.84 237.07C213.116 233.72 212.307 228.616 214.958 225.716C217.609 222.816 222.754 223.189 226.439 226.561C230.125 229.933 230.973 235.014 228.322 237.914Z" fill="#F2BB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M227.843 237.475C225.382 240.174 220.608 239.822 217.167 236.697C213.726 233.572 212.985 228.889 215.417 226.157C217.848 223.424 222.656 223.817 226.093 226.934C229.529 230.052 230.308 234.784 227.843 237.475Z" fill="#F2BC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M227.369 237.044C225.094 239.532 220.69 239.202 217.529 236.314C214.368 233.427 213.643 229.071 215.911 226.587C218.178 224.102 222.59 224.428 225.758 227.312C228.927 230.195 229.636 234.559 227.369 237.044Z" fill="#F2BD0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M226.895 236.612C224.805 238.891 220.768 238.595 217.872 235.953C214.976 233.311 214.312 229.309 216.401 227.029C218.491 224.75 222.516 225.043 225.416 227.693C228.316 230.343 228.972 234.329 226.895 236.612Z" fill="#F2BE0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M226.416 236.173C224.536 238.229 220.85 237.975 218.21 235.563C215.57 233.152 214.972 229.531 216.871 227.453C218.77 225.376 222.429 225.655 225.065 228.059C227.701 230.463 228.307 234.099 226.416 236.173Z" fill="#F2BF0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M225.968 235.716C224.262 237.579 220.955 237.342 218.587 235.172C216.22 233.002 215.657 229.708 217.38 227.875C219.104 226.042 222.386 226.253 224.761 228.419C227.137 230.584 227.643 233.87 225.968 235.716Z" fill="#F2C00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M225.459 235.294C223.943 236.956 221.006 236.74 218.896 234.82C216.785 232.901 216.309 229.986 217.806 228.294C219.304 226.601 222.255 226.86 224.366 228.78C226.477 230.699 226.971 233.644 225.459 235.294Z" fill="#F2C10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M224.985 234.863C223.655 236.315 221.068 236.102 219.238 234.439C217.408 232.776 216.973 230.216 218.303 228.763C219.632 227.311 222.212 227.529 224.038 229.184C225.863 230.839 226.307 233.415 224.985 234.863Z" fill="#F2C20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M224.498 234.428C223.367 235.674 221.162 235.505 219.584 234.065C218.006 232.625 217.645 230.441 218.808 229.177C219.97 227.914 222.143 228.1 223.726 229.548C225.308 230.996 225.642 233.185 224.498 234.428Z" fill="#F2C30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M224.024 233.996C223.079 235.033 221.249 234.893 219.931 233.691C218.613 232.489 218.31 230.671 219.256 229.634C220.201 228.598 222.039 228.733 223.356 229.935C224.674 231.137 224.978 232.955 224.024 233.996Z" fill="#F2C40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M223.546 233.557C222.793 234.38 221.323 234.278 220.266 233.314C219.208 232.35 218.974 230.901 219.734 230.074C220.494 229.246 221.957 229.353 223.01 230.309C224.063 231.265 224.305 232.73 223.546 233.557Z" fill="#F2C50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M223.071 233.125C222.868 233.489 222.557 233.78 222.181 233.958C221.805 234.137 221.383 234.193 220.974 234.121C220.564 234.048 220.187 233.85 219.895 233.553C219.604 233.256 219.412 232.876 219.347 232.465C219.282 232.054 219.346 231.633 219.531 231.261C219.716 230.888 220.013 230.583 220.38 230.386C220.747 230.19 221.166 230.113 221.579 230.166C221.991 230.219 222.377 230.399 222.683 230.682C223.054 230.957 223.302 231.368 223.375 231.824C223.448 232.281 223.339 232.748 223.071 233.125Z" fill="#F2C60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.592 232.686C222.458 232.927 222.252 233.12 222.003 233.239C221.754 233.358 221.474 233.396 221.202 233.348C220.931 233.301 220.681 233.17 220.487 232.973C220.293 232.777 220.165 232.526 220.121 232.253C220.076 231.981 220.118 231.702 220.24 231.454C220.362 231.207 220.558 231.003 220.8 230.872C221.043 230.741 221.321 230.688 221.594 230.722C221.868 230.756 222.125 230.874 222.328 231.06C222.576 231.242 222.743 231.515 222.792 231.819C222.842 232.123 222.77 232.434 222.592 232.686Z" fill="#F2C70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.101 232.224C221.984 232.324 221.832 232.375 221.678 232.364C221.524 232.353 221.381 232.282 221.28 232.166C221.212 232.127 221.154 232.073 221.11 232.008C221.066 231.943 221.037 231.869 221.026 231.792C221.014 231.714 221.02 231.635 221.043 231.56C221.066 231.486 221.106 231.417 221.159 231.359C221.212 231.302 221.278 231.257 221.351 231.229C221.424 231.2 221.502 231.188 221.58 231.194C221.658 231.199 221.734 231.222 221.802 231.261C221.87 231.3 221.928 231.354 221.973 231.418C222.095 231.51 222.177 231.645 222.201 231.795C222.224 231.946 222.188 232.099 222.101 232.224Z" fill="#F2C80D"/>
<path style="mix-blend-mode:multiply" opacity="0.97" fill-rule="evenodd" clip-rule="evenodd" d="M318.89 305.752C312.967 311.163 295.216 308.807 274.313 299.509C273.215 269.939 260.386 243.664 253.011 230.885C258.018 233.334 262.764 236.283 267.177 239.688C285.512 253.857 304.424 266.149 312.1 275.111C324.911 290.077 324.884 300.244 318.89 305.752Z" fill="url(#paint124_linear_292_86830)"/>
<path d="M314.031 366.645L309.54 423.215L363.598 427.509L368.089 370.939L314.031 366.645Z" fill="#B4B8FF"/>
<path d="M339.573 433.861C356.681 428.457 364.718 405.604 357.525 382.818C350.331 360.033 330.63 345.942 313.522 351.346C296.414 356.75 288.376 379.602 295.57 402.388C302.764 425.174 322.464 439.265 339.573 433.861Z" fill="#B4B8FF"/>
<path d="M341.842 433.971C358.757 428.607 366.68 405.992 359.539 383.46C352.397 360.927 332.895 347.009 315.98 352.373C299.065 357.737 291.142 380.351 298.284 402.884C305.425 425.417 324.927 439.335 341.842 433.971Z" fill="#B5B9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M359.086 383.595C366.169 405.796 358.315 428.086 341.653 433.364C324.99 438.642 305.769 424.942 298.731 402.741C291.692 380.539 299.501 358.258 316.172 352.971C332.844 347.684 352.047 361.402 359.086 383.595Z" fill="#B5BAFF"/>
<path d="M341.454 432.772C357.87 427.566 365.559 405.617 358.627 383.746C351.695 361.875 332.768 348.366 316.351 353.572C299.935 358.777 292.246 380.727 299.178 402.598C306.11 424.468 325.037 437.978 341.454 432.772Z" fill="#B6BBFF"/>
<path d="M341.273 432.173C357.442 427.045 365.015 405.427 358.188 383.888C351.361 362.348 332.719 349.044 316.55 354.171C300.381 359.298 292.807 380.916 299.634 402.456C306.461 423.995 325.103 437.3 341.273 432.173Z" fill="#B6BCFF"/>
<path d="M341.082 431.564C357.005 426.515 364.463 405.229 357.741 384.021C351.019 362.812 332.662 349.712 316.74 354.761C300.818 359.81 293.359 381.096 300.081 402.305C306.803 423.514 325.16 436.613 341.082 431.564Z" fill="#B7BDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M357.288 384.17C363.902 405.043 356.562 425.996 340.891 430.964C325.221 435.932 307.15 423.047 300.537 402.165C293.923 381.283 301.254 360.339 316.925 355.371C332.596 350.403 350.666 363.297 357.288 384.17Z" fill="#B8BEFF"/>
<path d="M338.244 429.697C353.615 424.842 360.835 404.306 354.37 383.83C347.906 363.353 330.204 350.689 314.833 355.545C299.462 360.4 292.242 380.935 298.707 401.412C305.171 421.889 322.873 434.552 338.244 429.697Z" fill="#B8BFFF"/>
<path d="M340.501 429.776C355.678 424.963 362.788 404.678 356.382 384.467C349.976 364.256 332.48 351.773 317.304 356.586C302.127 361.398 295.017 381.684 301.423 401.894C307.829 422.105 325.325 434.588 340.501 429.776Z" fill="#B9C0FF"/>
<path d="M340.32 429.167C355.245 424.435 362.236 404.482 355.935 384.602C349.634 364.723 332.428 352.443 317.503 357.176C302.578 361.909 295.587 381.861 301.888 401.741C308.189 421.621 325.395 433.9 340.32 429.167Z" fill="#B9C1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M355.482 384.737C361.68 404.291 354.809 423.915 340.13 428.564C325.451 433.214 308.532 421.143 302.334 401.598C296.137 382.054 303.016 362.42 317.677 357.736C332.339 353.051 349.285 365.192 355.482 384.737Z" fill="#BAC2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M355.031 384.888C361.122 404.105 354.367 423.393 339.935 427.971C325.504 432.549 308.877 420.674 302.75 401.457C296.623 382.24 303.414 362.943 317.845 358.373C332.277 353.804 348.939 365.706 355.031 384.888Z" fill="#BAC3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M354.579 385.029C360.573 403.919 353.924 422.87 339.749 427.369C325.575 431.868 309.222 420.196 303.237 401.306C297.252 382.417 303.892 363.465 318.067 358.967C332.242 354.468 348.594 366.14 354.579 385.029Z" fill="#BBC4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M354.136 385.189C360.015 403.742 353.49 422.383 339.555 426.811C325.619 431.239 309.567 419.726 303.689 401.129C297.81 382.532 304.326 363.935 318.262 359.507C332.197 355.079 348.249 366.591 354.136 385.189Z" fill="#BCC5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M353.685 385.313C359.457 403.538 353.047 421.825 339.368 426.164C325.69 430.504 309.912 419.248 304.14 401.023C298.367 382.798 304.769 364.51 318.447 360.171C332.126 355.832 347.903 367.087 353.685 385.313Z" fill="#BCC6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M353.233 385.454C358.9 403.352 352.614 421.311 339.183 425.571C325.752 429.831 310.258 418.779 304.583 400.881C298.908 382.984 305.211 365.033 318.642 360.773C332.073 356.514 347.558 367.566 353.233 385.454Z" fill="#BDC7FF"/>
<path d="M338.99 424.963C352.176 420.781 358.353 403.154 352.787 385.592C347.221 368.029 332.018 357.181 318.832 361.363C305.646 365.544 299.469 383.171 305.035 400.734C310.602 418.297 325.804 429.144 338.99 424.963Z" fill="#BDC8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M352.33 385.738C357.793 402.971 351.737 420.275 338.802 424.367C325.867 428.458 310.949 417.831 305.486 400.598C300.023 383.364 306.088 366.06 319.023 361.969C331.958 357.877 346.894 368.513 352.33 385.738Z" fill="#BEC9FF"/>
<path d="M338.61 423.773C351.298 419.75 357.241 402.788 351.884 385.887C346.528 368.986 331.9 358.547 319.212 362.57C306.525 366.594 300.582 383.556 305.939 400.457C311.295 417.357 325.923 427.796 338.61 423.773Z" fill="#BFCAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M351.427 386.074C356.686 402.643 350.852 419.266 338.421 423.215C325.991 427.165 311.639 416.919 306.389 400.359C301.139 383.798 306.965 367.158 319.404 363.217C331.843 359.277 346.177 369.461 351.427 386.074Z" fill="#BFCBFF"/>
<path d="M338.23 422.574C350.419 418.709 356.128 402.412 350.981 386.173C345.834 369.935 331.781 359.904 319.592 363.769C307.403 367.634 301.695 383.932 306.842 400.17C311.988 416.409 326.042 426.439 338.23 422.574Z" fill="#C0CCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M350.533 386.313C355.571 402.254 349.975 418.194 338.041 421.967C326.106 425.739 312.365 415.927 307.292 400.022C302.219 384.117 307.841 368.141 319.785 364.369C331.728 360.596 345.487 370.408 350.533 386.313Z" fill="#C0CDFF"/>
<path d="M337.839 421.376C349.529 417.669 355.004 402.04 350.069 386.469C345.134 370.897 331.656 361.278 319.966 364.985C308.276 368.692 302.8 384.321 307.736 399.893C312.671 415.465 326.149 425.083 337.839 421.376Z" fill="#C1CEFF"/>
<path d="M335.23 420.114C346.634 416.512 351.991 401.279 347.196 386.09C342.401 370.901 329.269 361.508 317.865 365.11C306.461 368.712 301.103 383.945 305.899 399.134C310.694 414.323 323.826 423.716 335.23 420.114Z" fill="#C2CFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M349.178 386.738C353.906 401.652 348.665 416.618 337.465 420.169C326.265 423.72 313.365 414.51 308.637 399.597C303.91 384.684 309.16 369.718 320.351 366.166C331.542 362.615 344.45 371.834 349.178 386.738Z" fill="#C2D0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M348.727 386.88C353.349 401.466 348.222 416.104 337.279 419.567C326.336 423.03 313.711 414.032 309.089 399.455C304.468 384.879 309.594 370.231 320.546 366.769C331.498 363.306 344.105 372.303 348.727 386.88Z" fill="#C3D1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M348.276 387.031C352.791 401.28 347.78 415.573 337.085 418.965C326.389 422.357 314.065 413.563 309.541 399.314C305.017 385.065 310.037 370.763 320.732 367.371C331.427 363.979 343.76 372.791 348.276 387.031Z" fill="#C3D2FF"/>
<path d="M336.889 418.379C347.339 415.065 352.235 401.097 347.824 387.18C343.413 373.264 331.366 364.668 320.916 367.982C310.466 371.296 305.57 385.264 309.981 399.181C314.392 413.098 326.439 421.693 336.889 418.379Z" fill="#C4D3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M347.381 387.314C351.684 400.899 346.903 414.537 336.704 417.769C326.504 421.001 314.747 412.615 310.444 399.021C306.141 385.428 310.913 371.808 321.113 368.566C331.312 365.325 343.069 373.729 347.381 387.314Z" fill="#C4D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M346.894 387.456C351.091 400.739 346.434 414.023 336.482 417.167C326.531 420.311 315.057 412.137 310.86 398.88C306.663 385.623 311.32 372.312 321.263 369.169C331.206 366.025 342.724 374.199 346.894 387.456Z" fill="#C5D5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M346.478 387.597C350.569 400.518 346.027 413.492 336.323 416.565C326.62 419.638 315.437 411.659 311.347 398.738C307.257 385.817 311.79 372.844 321.493 369.771C331.197 366.698 342.379 374.677 346.478 387.597Z" fill="#C6D6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M346.009 387.739C350.002 400.332 345.575 412.969 336.119 415.971C326.663 418.974 315.756 411.189 311.772 398.596C307.788 386.003 312.214 373.366 321.661 370.373C331.108 367.38 342.033 375.146 346.009 387.739Z" fill="#C6D7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.575 387.845C349.462 400.111 345.15 412.411 335.942 415.298C326.734 418.185 316.128 410.64 312.241 398.384C308.354 386.127 312.666 373.818 321.874 370.931C331.081 368.044 341.688 375.624 345.575 387.845Z" fill="#C7D8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.124 388.023C348.904 399.951 344.707 411.933 335.756 414.767C326.805 417.601 316.473 410.242 312.693 398.313C308.912 386.384 313.109 374.402 322.06 371.568C331.011 368.735 341.343 376.094 345.124 388.023Z" fill="#C7D9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M344.672 388.173C348.346 399.765 344.273 411.411 335.561 414.165C326.85 416.919 316.818 409.737 313.144 398.171C309.47 386.606 313.543 374.934 322.255 372.171C330.966 369.408 340.998 376.572 344.672 388.173Z" fill="#C8DBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M344.238 388.315C347.78 399.579 343.849 410.888 335.385 413.572C326.92 416.255 317.173 409.294 313.605 398.03C310.037 386.765 313.994 375.447 322.458 372.773C330.922 370.098 340.697 377.05 344.238 388.315Z" fill="#C9DCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M343.778 388.456C347.24 399.393 343.397 410.366 335.181 412.969C326.965 415.573 317.474 408.816 314.047 397.914C310.621 387.013 314.428 376.005 322.635 373.402C330.843 370.798 340.307 377.52 343.778 388.456Z" fill="#C9DDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M343.353 388.598C346.708 399.225 342.981 409.852 335.022 412.367C327.062 414.882 317.881 408.347 314.526 397.737C311.17 387.128 314.889 376.483 322.848 373.968C330.807 371.453 339.962 377.998 343.353 388.598Z" fill="#CADEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M342.875 388.731C346.124 399.004 342.52 409.312 334.809 411.756C327.097 414.2 318.2 407.86 314.95 397.587C311.701 387.314 315.304 377.006 323.016 374.562C330.727 372.117 339.616 378.467 342.875 388.731Z" fill="#CADFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M342.467 388.882C345.619 398.827 342.122 408.798 334.659 411.172C327.195 413.545 318.589 407.399 315.437 397.454C312.286 387.509 315.783 377.537 323.246 375.173C330.71 372.808 339.271 378.99 342.467 388.882Z" fill="#CBE0FF"/>
<path d="M334.426 410.57C341.641 408.282 345.022 398.638 341.976 389.029C338.931 379.42 330.612 373.485 323.397 375.773C316.182 378.061 312.801 387.706 315.847 397.315C318.892 406.923 327.211 412.858 334.426 410.57Z" fill="#CCE1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M341.52 389.174C344.46 398.446 341.201 407.771 334.234 409.967C327.266 412.163 319.236 406.425 316.296 397.171C313.357 387.916 316.615 378.573 323.583 376.368C330.551 374.163 338.581 379.875 341.52 389.174Z" fill="#CCE2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M341.069 389.315C343.911 398.26 340.759 407.24 334.048 409.365C327.337 411.49 319.581 405.973 316.748 397.029C313.915 388.085 317.058 379.096 323.778 376.97C330.498 374.845 338.236 380.362 341.069 389.315Z" fill="#CDE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.617 389.457C343.353 398.074 340.325 406.717 333.853 408.772C327.381 410.826 319.926 405.495 317.199 396.878C314.472 388.262 317.5 379.618 323.963 377.573C330.426 375.527 337.89 380.84 340.617 389.457Z" fill="#CDE4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.174 389.617C342.83 397.897 339.882 406.212 333.667 408.214C327.452 410.215 320.272 405.026 317.677 396.701C315.083 388.377 317.961 380.105 324.185 378.104C330.409 376.103 337.545 381.31 340.174 389.617Z" fill="#CEE5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M339.723 389.741C342.237 397.711 339.439 405.681 333.472 407.567C327.505 409.454 320.617 404.547 318.102 396.595C315.588 388.642 318.377 380.654 324.3 378.768C330.223 376.882 337.199 381.788 339.723 389.741Z" fill="#CEE6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M339.271 389.882C341.688 397.507 339.006 405.15 333.286 406.965C327.567 408.781 320.962 404.078 318.554 396.453C316.146 388.828 318.811 381.186 324.539 379.37C330.267 377.555 336.854 382.266 339.271 389.882Z" fill="#CFE7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M338.819 390.024C341.13 397.312 338.563 404.636 333.091 406.372C327.62 408.107 321.307 403.6 318.996 396.312C316.685 389.023 319.253 381.708 324.725 379.973C330.196 378.237 336.509 382.736 338.819 390.024Z" fill="#D0E8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M338.368 390.166C340.573 397.126 338.129 404.114 332.905 405.77C327.682 407.426 321.652 403.113 319.448 396.17C317.243 389.227 319.696 382.222 324.919 380.566C330.143 378.91 336.163 383.214 338.368 390.166Z" fill="#D0E9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M337.916 390.316C340.015 396.94 337.686 403.6 332.711 405.167C327.735 406.735 321.998 402.652 319.899 396.028C317.801 389.404 320.13 382.744 325.105 381.168C330.081 379.592 335.818 383.683 337.916 390.316Z" fill="#D1EAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M337.465 390.502C339.466 396.799 337.253 403.113 332.525 404.609C327.797 406.106 322.343 402.218 320.351 395.931C318.359 389.643 320.572 383.311 325.3 381.815C330.028 380.318 335.473 384.161 337.465 390.502Z" fill="#D1EBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M337.013 390.6C338.908 396.559 336.81 402.546 332.33 403.972C327.85 405.398 322.688 401.705 320.82 395.736C318.952 389.767 321.024 383.789 325.504 382.373C329.984 380.956 335.128 384.631 337.013 390.6Z" fill="#D2ECFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M336.571 390.741C338.342 396.374 336.367 402.024 332.144 403.37C327.921 404.716 323.034 401.227 321.254 395.594C319.475 389.962 321.449 384.312 325.681 382.966C329.913 381.62 334.783 385.109 336.571 390.741Z" fill="#D3EDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M336.119 390.883C337.792 396.196 335.933 401.51 331.949 402.767C327.965 404.025 323.379 400.757 321.705 395.453C320.032 390.148 321.882 384.826 325.867 383.568C329.851 382.31 334.437 385.578 336.119 390.883Z" fill="#D3EEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M335.668 391.025C337.244 395.993 335.491 400.987 331.763 402.165C328.036 403.343 323.724 400.279 322.157 395.311C320.59 390.343 322.325 385.348 326.053 384.17C329.78 382.992 334.092 386.074 335.668 391.025Z" fill="#D4EFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M335.216 391.166C336.686 395.807 335.057 400.465 331.569 401.572C328.08 402.679 324.07 399.801 322.6 395.169C321.13 390.538 322.768 385.871 326.248 384.772C329.727 383.674 333.747 386.526 335.216 391.166Z" fill="#D4F0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M334.765 391.308C336.128 395.621 334.614 399.942 331.383 400.97C328.151 401.997 324.415 399.331 323.051 395.027C321.688 390.724 323.202 386.393 326.434 385.366C329.665 384.339 333.401 387.004 334.765 391.308Z" fill="#D5F1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M334.313 391.459C335.579 395.435 334.181 399.429 331.188 400.367C328.196 401.306 324.76 398.862 323.503 394.886C322.246 390.91 323.645 386.916 326.628 385.968C329.612 385.02 333.056 387.482 334.313 391.459Z" fill="#D6F2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M333.862 391.6C335.021 395.24 333.738 398.897 331.002 399.765C328.266 400.633 325.105 398.384 323.919 394.744C322.732 391.104 324.043 387.438 326.779 386.57C329.514 385.702 332.728 387.952 333.862 391.6Z" fill="#D6F3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M333.41 391.742C334.464 395.054 333.295 398.384 330.807 399.172C328.319 399.96 325.495 397.914 324.406 394.594C323.317 391.273 324.521 387.961 327.009 387.172C329.497 386.384 332.365 388.43 333.41 391.742Z" fill="#D7F4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M332.959 391.884C333.906 394.868 332.861 397.861 330.621 398.57C328.381 399.278 325.805 397.436 324.858 394.452C323.91 391.467 324.955 388.483 327.195 387.766C329.435 387.048 332.02 388.899 332.959 391.884Z" fill="#D7F5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M332.516 392.025C333.357 394.682 332.418 397.339 330.426 397.968C328.434 398.596 326.15 396.958 325.309 394.31C324.468 391.662 325.397 388.997 327.39 388.368C329.382 387.739 331.675 389.377 332.516 392.025Z" fill="#D8F6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M332.064 392.167C332.799 394.487 331.985 396.816 330.241 397.365C328.496 397.914 326.496 396.48 325.761 394.168C325.026 391.857 325.84 389.519 327.576 388.97C329.311 388.421 331.33 389.847 332.064 392.167Z" fill="#D8F7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M331.613 392.273C332.241 394.266 331.542 396.258 330.055 396.701C328.567 397.144 326.841 395.94 326.203 393.956C325.566 391.972 326.274 389.971 327.77 389.528C329.267 389.085 330.958 390.325 331.613 392.273Z" fill="#D9F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M331.161 392.45C331.683 394.115 331.108 395.771 329.86 396.17C328.611 396.568 327.204 395.541 326.655 393.885C326.106 392.229 326.717 390.564 327.956 390.166C329.196 389.767 330.639 390.794 331.161 392.45Z" fill="#DAF9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M330.71 392.601C331.135 393.92 330.666 395.258 329.674 395.568C328.682 395.878 327.531 395.063 327.106 393.743C326.681 392.424 327.151 391.087 328.151 390.768C329.152 390.449 330.294 391.273 330.71 392.601Z" fill="#DAFAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M330.258 392.743C330.443 393.142 330.464 393.597 330.319 394.012C330.174 394.427 329.872 394.769 329.479 394.965C328.735 395.205 327.877 394.594 327.558 393.602C327.416 393.414 327.32 393.196 327.279 392.965C327.238 392.733 327.253 392.495 327.323 392.271C327.392 392.046 327.514 391.841 327.678 391.673C327.843 391.505 328.045 391.379 328.268 391.304C328.491 391.23 328.728 391.21 328.961 391.246C329.193 391.281 329.413 391.372 329.603 391.51C329.794 391.648 329.948 391.83 330.055 392.039C330.161 392.249 330.215 392.481 330.214 392.716L330.258 392.743Z" fill="#DBFBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M329.807 392.884C329.817 393.068 329.775 393.251 329.686 393.412C329.597 393.573 329.465 393.706 329.304 393.795C329.143 393.885 328.961 393.928 328.777 393.919C328.594 393.91 328.416 393.849 328.265 393.744C328.114 393.639 327.995 393.494 327.923 393.325C327.85 393.156 327.826 392.97 327.854 392.788C327.882 392.606 327.96 392.436 328.08 392.296C328.2 392.157 328.356 392.054 328.532 391.999C328.818 391.954 329.109 392.021 329.347 392.186C329.584 392.351 329.749 392.601 329.807 392.884Z" fill="#DBFCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M329.364 393.026C329.421 393.161 329.425 393.312 329.376 393.45C329.327 393.587 329.228 393.702 329.098 393.77C328.851 393.85 328.567 393.646 328.461 393.309C328.399 393.176 328.391 393.024 328.439 392.886C328.487 392.747 328.587 392.632 328.718 392.566C328.966 392.486 329.258 392.698 329.364 393.026Z" fill="#DCFDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M309.55 423.269C309.55 423.269 314.455 438.208 332.799 439.129C351.144 440.05 358.262 427.139 358.262 427.139C358.262 427.139 340.759 418.938 309.55 423.269Z" fill="url(#paint125_radial_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.97" fill-rule="evenodd" clip-rule="evenodd" d="M318.182 392.052C318.182 392.052 345.274 388.731 360.148 419.957L365.947 398.074L330.728 370.488C330.728 370.488 323.645 381.983 318.182 392.052Z" fill="url(#paint126_linear_292_86830)"/>
<path d="M366.59 396.792C400.672 385.985 416.637 340.422 402.248 295.026C387.86 249.63 348.567 221.59 314.486 232.398C280.404 243.205 264.439 288.767 278.827 334.164C293.215 379.56 332.508 407.6 366.59 396.792Z" fill="#C0CDFF"/>
<path d="M366.047 395.078C399.416 384.497 415.045 339.885 400.957 295.436C386.869 250.986 348.397 223.53 315.029 234.111C281.66 244.693 266.03 289.304 280.118 333.754C294.207 378.204 332.678 405.66 366.047 395.078Z" fill="#C1CEFF"/>
<path d="M365.504 393.365C398.164 383.008 413.463 339.346 399.674 295.842C385.886 252.339 348.232 225.468 315.572 235.825C282.912 246.182 267.613 289.844 281.401 333.347C295.19 376.851 332.844 403.721 365.504 393.365Z" fill="#C2CFFF"/>
<path d="M364.961 391.651C396.912 381.519 411.88 338.806 398.391 296.249C384.903 253.692 348.067 227.407 316.115 237.539C284.163 247.671 269.196 290.384 282.684 332.941C296.172 375.498 333.009 401.783 364.961 391.651Z" fill="#C2D0FF"/>
<path d="M364.417 389.937C395.656 380.031 410.289 338.269 397.1 296.659C383.912 255.048 347.897 229.346 316.658 239.252C285.42 249.158 270.787 290.921 283.975 332.531C297.164 374.142 333.179 399.843 364.417 389.937Z" fill="#C3D1FF"/>
<path d="M363.877 388.232C394.407 378.551 408.707 337.734 395.817 297.065C382.927 256.397 347.729 231.276 317.199 240.958C286.668 250.639 272.368 291.456 285.258 332.124C298.148 372.793 333.347 397.913 363.877 388.232Z" fill="#C3D2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M394.527 297.516C407.116 337.234 393.154 377.103 363.335 386.552C333.516 396.002 299.129 371.498 286.539 331.762C273.949 292.026 287.92 252.174 317.739 242.716C347.558 233.258 381.937 257.745 394.527 297.516Z" fill="#C4D3FF"/>
<path d="M362.79 384.805C391.899 375.574 405.533 336.657 393.243 297.882C380.953 259.106 347.393 235.154 318.285 244.385C289.176 253.615 275.542 292.532 287.832 331.308C300.122 370.084 333.682 394.035 362.79 384.805Z" fill="#C4D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M391.959 298.287C403.947 336.11 390.649 374.083 362.247 383.09C333.844 392.096 301.104 368.726 289.116 330.894C277.128 293.062 290.426 255.097 318.829 246.099C347.231 237.102 379.971 260.455 391.959 298.287Z" fill="#C5D5FF"/>
<path d="M361.704 381.377C389.396 372.596 402.368 335.578 390.678 298.695C378.988 261.812 347.063 239.031 319.371 247.812C291.68 256.593 278.708 293.611 290.398 330.495C302.088 367.378 334.013 390.159 361.704 381.377Z" fill="#C6D6FF"/>
<path d="M358.782 379.163C385.669 370.67 398.299 334.753 386.992 298.938C375.685 263.124 344.723 240.976 317.836 249.468C290.95 257.961 278.32 293.879 289.627 329.693C300.934 365.507 331.896 387.656 358.782 379.163Z" fill="#C6D7FF"/>
<path d="M360.618 377.95C386.888 369.62 399.194 334.502 388.104 299.511C377.013 264.521 346.727 242.909 320.457 251.24C294.188 259.57 281.882 294.688 292.972 329.678C304.062 364.668 334.348 386.28 360.618 377.95Z" fill="#C7D8FF"/>
<path d="M360.077 376.245C385.639 368.139 397.612 333.967 386.821 299.918C376.029 265.87 346.559 244.839 320.998 252.945C295.436 261.05 283.463 295.223 294.255 329.271C305.046 363.32 334.516 384.351 360.077 376.245Z" fill="#C7D9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M385.532 300.323C396.023 333.426 384.381 366.645 359.538 374.526C334.694 382.408 306.035 361.96 295.544 328.857C285.052 295.754 296.695 262.536 321.538 254.654C346.381 246.772 375.04 267.22 385.532 300.323Z" fill="#C8DBFF"/>
<path d="M358.991 372.818C383.131 365.163 394.438 332.89 384.247 300.735C374.055 268.579 346.224 248.717 322.084 256.372C297.945 264.027 286.637 296.3 296.829 328.455C307.021 360.611 334.852 380.472 358.991 372.818Z" fill="#C9DCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M382.964 301.138C392.854 332.346 381.875 363.669 358.448 371.099C335.022 378.529 307.983 359.25 298.111 328.042C288.239 296.834 299.191 265.511 322.627 258.081C346.062 250.651 373.066 269.93 382.964 301.138Z" fill="#C9DDFF"/>
<path d="M357.905 369.39C380.623 362.186 391.264 331.813 381.673 301.551C372.081 271.288 345.889 252.596 323.17 259.8C300.452 267.004 289.811 297.376 299.403 327.639C308.995 357.902 335.187 376.594 357.905 369.39Z" fill="#CADEFF"/>
<path d="M357.362 367.677C379.372 360.697 389.682 331.274 380.39 301.958C371.098 272.641 345.723 254.534 323.714 261.513C301.704 268.493 291.394 297.916 300.686 327.232C309.978 356.548 335.352 374.656 357.362 367.677Z" fill="#CADFFF"/>
<path d="M356.821 365.971C378.123 359.217 388.1 330.739 379.107 302.364C370.114 273.99 345.555 256.464 324.254 263.218C302.953 269.973 292.975 298.451 301.969 326.825C310.962 355.2 335.52 372.726 356.821 365.971Z" fill="#CBE0FF"/>
<path d="M356.278 364.258C376.866 357.729 386.509 330.202 377.816 302.774C369.123 275.346 345.385 258.404 324.797 264.932C304.209 271.461 294.567 298.988 303.26 326.416C311.953 353.844 335.69 370.786 356.278 364.258Z" fill="#CCE1FF"/>
<path d="M355.735 362.544C375.614 356.24 384.926 329.662 376.533 303.181C368.139 276.699 345.22 260.342 325.34 266.646C305.461 272.95 296.149 299.528 304.543 326.009C312.936 352.491 335.855 368.848 355.735 362.544Z" fill="#CCE2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M375.226 303.582C383.318 329.123 374.341 354.751 355.173 360.826C336.004 366.901 313.906 351.129 305.805 325.598C297.704 300.067 306.69 274.438 325.867 268.354C345.044 262.27 367.151 278.033 375.226 303.582Z" fill="#CDE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M373.96 303.99C381.751 328.582 373.074 353.263 354.65 359.117C336.226 364.971 314.906 349.765 307.115 325.191C299.324 300.616 308 275.926 326.425 270.072C344.849 264.218 366.169 279.406 373.96 303.99Z" fill="#CDE4FF"/>
<path d="M354.106 357.403C371.855 351.775 380.169 328.046 372.676 304.404C365.182 280.761 344.719 266.158 326.97 271.787C309.22 277.415 300.906 301.144 308.399 324.786C315.893 348.428 336.356 363.031 354.106 357.403Z" fill="#CEE5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M371.392 304.805C378.581 327.502 370.604 350.288 353.561 355.69C336.518 361.092 316.881 347.109 309.709 324.376C302.538 301.643 310.497 278.901 327.54 273.49C344.584 268.079 364.194 282.107 371.392 304.805Z" fill="#CEE6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M370.108 305.212C376.997 326.971 369.347 348.8 353.021 353.981C336.695 359.161 317.863 345.718 310.967 323.968C304.07 302.219 311.719 280.38 328.054 275.208C344.389 270.037 363.212 283.462 370.108 305.212Z" fill="#CFE7FF"/>
<path d="M352.479 352.27C368.098 347.317 375.414 326.434 368.819 305.627C362.224 284.819 344.216 271.966 328.597 276.919C312.977 281.872 305.662 302.755 312.257 323.563C318.852 344.371 336.86 357.223 352.479 352.27Z" fill="#D0E8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M367.532 306.027C373.827 325.89 366.842 345.825 351.932 350.554C337.023 355.283 319.838 343.008 313.543 323.154C307.248 303.299 314.225 283.347 329.187 278.627C344.15 273.907 361.237 286.172 367.532 306.027Z" fill="#D0E9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M366.248 306.434C372.242 325.35 365.593 344.337 351.392 348.836C337.191 353.334 320.82 341.662 314.826 322.746C308.833 303.83 315.482 284.844 329.683 280.345C343.884 275.846 360.254 287.527 366.248 306.434Z" fill="#D1EAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M364.965 306.841C370.658 324.81 364.336 342.849 350.852 347.126C337.368 351.404 321.803 340.307 316.111 322.33C310.418 304.353 316.73 286.322 330.223 282.045C343.716 277.768 359.29 288.873 364.965 306.841Z" fill="#D1EBFF"/>
<path d="M347.998 345.032C360.735 341.008 366.719 323.995 361.363 307.031C356.007 290.067 341.34 279.576 328.603 283.6C315.866 287.623 309.882 304.636 315.237 321.6C320.593 338.564 335.26 349.055 347.998 345.032Z" fill="#D2ECFF"/>
<path d="M349.763 343.702C361.835 339.874 367.491 323.739 362.396 307.663C357.301 291.588 343.384 281.66 331.312 285.488C319.24 289.316 313.584 305.451 318.679 321.526C323.774 337.602 337.691 347.53 349.763 343.702Z" fill="#D3EDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M361.104 308.072C365.903 323.198 360.582 338.386 349.223 341.99C337.864 345.594 324.76 336.243 320.006 321.117C315.252 305.991 320.528 290.795 331.887 287.19C343.247 283.586 356.306 292.938 361.104 308.072Z" fill="#D3EEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M359.821 308.48C364.318 322.649 359.325 336.898 348.674 340.281C338.023 343.664 325.752 334.888 321.228 320.71C316.703 306.531 321.715 292.282 332.365 288.908C343.016 285.534 355.323 294.293 359.821 308.48Z" fill="#D4EFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M358.537 308.887C362.734 322.171 358.077 335.454 348.134 338.563C338.191 341.671 326.735 333.542 322.538 320.302C318.342 307.063 322.999 293.735 332.941 290.618C342.884 287.5 354.34 295.648 358.537 308.887Z" fill="#D4F0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M357.244 309.294C361.149 321.586 356.819 333.922 347.594 336.854C338.368 339.785 327.718 332.187 323.822 319.895C319.926 307.603 324.247 295.258 333.481 292.327C342.715 289.395 353.349 296.994 357.244 309.294Z" fill="#D5F1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M355.96 309.702C359.564 321.046 355.571 332.435 347.054 335.136C338.537 337.837 328.709 330.832 325.106 319.487C321.502 308.143 325.504 296.746 334.021 294.045C342.538 291.344 352.366 298.349 355.96 309.702Z" fill="#D6F2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M354.677 310.109C357.979 320.506 354.314 330.947 346.505 333.426C338.696 335.906 329.683 329.477 326.398 319.071C323.114 308.666 326.752 298.234 334.57 295.754C342.388 293.274 351.383 299.704 354.677 310.109Z" fill="#D6F3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M353.393 310.516C356.385 319.974 353.065 329.468 345.965 331.717C338.864 333.967 330.674 328.122 327.682 318.664C324.689 309.206 328.009 299.721 335.11 297.463C342.211 295.205 350.436 301.058 353.393 310.516Z" fill="#D7F4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M352.109 310.924C354.765 319.434 351.808 327.98 345.425 329.999C339.041 332.018 331.666 326.74 328.966 318.256C326.265 309.773 329.258 301.209 335.65 299.181C342.043 297.153 349.409 302.413 352.109 310.924Z" fill="#D7F5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M350.817 311.331C353.216 318.894 350.56 326.492 344.876 328.29C339.192 330.088 332.649 325.421 330.25 317.884C327.85 310.348 330.515 302.732 336.19 300.926C341.866 299.119 348.426 303.759 350.817 311.331Z" fill="#D8F6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M349.551 311.739C351.649 318.354 349.321 325.022 344.354 326.581C339.387 328.14 333.614 324.084 331.534 317.442C329.453 310.8 331.764 304.158 336.74 302.608C341.715 301.058 347.435 305.114 349.551 311.739Z" fill="#D8F7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M348.249 312.146C350.02 317.823 348.054 323.517 343.822 324.863C339.59 326.209 334.65 322.711 332.853 317.034C331.055 311.358 333.047 305.664 337.279 304.317C341.511 302.971 346.452 306.469 348.249 312.146Z" fill="#D9F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M346.965 312.571C348.462 317.3 346.797 322.047 343.247 323.198C339.697 324.349 335.606 321.427 334.11 316.671C332.614 311.916 334.269 307.196 337.82 306.044C341.37 304.893 345.469 307.824 346.965 312.571Z" fill="#DAF9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.682 312.961C346.877 316.742 345.549 320.541 342.707 321.445C339.865 322.348 336.589 320.001 335.394 316.22C334.198 312.438 335.526 308.639 338.368 307.745C341.21 306.85 344.477 309.179 345.682 312.961Z" fill="#DAFAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M344.389 313.368C345.274 316.202 344.291 319.053 342.166 319.726C340.042 320.4 337.58 318.655 336.677 315.812C335.774 312.97 336.775 310.127 338.908 309.454C341.042 308.781 343.494 310.534 344.389 313.368Z" fill="#DBFBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M343.105 313.775C343.707 315.671 343.043 317.566 341.618 318.017C340.192 318.469 338.563 317.3 337.961 315.405C337.359 313.51 338.032 311.615 339.448 311.163C340.865 310.711 342.512 311.88 343.105 313.775Z" fill="#DBFCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M341.821 314.183C341.997 314.563 342.017 314.997 341.878 315.392C341.739 315.787 341.452 316.112 341.078 316.299C340.369 316.53 339.555 315.945 339.254 314.998C339.079 314.617 339.058 314.184 339.197 313.789C339.336 313.394 339.623 313.068 339.998 312.881C340.697 312.651 341.52 313.235 341.821 314.183Z" fill="#DCFDFF"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M405.399 408.09C388.453 411.915 368.143 402.661 349.55 387.845C351.923 389.103 382.68 405.265 399.892 378.352C417.847 350.394 417.909 300.926 417.909 300.926L446.727 321.905C445.559 360.286 433.66 401.722 405.399 408.09Z" fill="url(#paint127_linear_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M413.88 404.902C413.88 404.902 408.825 393.956 412.358 376.74C418.431 347.162 444.231 338.855 443.345 352.768C442.079 372.791 417.944 403.051 413.88 404.902Z" fill="url(#paint128_linear_292_86830)"/>
<g style="mix-blend-mode:soft-light">
<path fill-rule="evenodd" clip-rule="evenodd" d="M320.936 357.399C320.936 357.399 337.554 378.317 356.607 373.738C375.66 369.16 374.066 323.951 377.51 307.753L320.936 357.399Z" fill="white"/>
</g>
<path d="M410.662 350.157L404.264 430.748L466.654 435.703L473.051 355.112L410.662 350.157Z" fill="#B4B8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M467.303 376.014C475.962 403.325 466.356 430.734 445.851 437.234C425.346 443.734 401.716 426.873 393.057 399.562C384.398 372.25 394.004 344.842 414.509 338.341C435.014 331.841 458.6 348.712 467.303 376.014Z" fill="#B4B8FF"/>
<path d="M445.581 436.533C465.787 430.126 475.251 403.112 466.72 376.197C458.19 349.282 434.894 332.657 414.688 339.065C394.483 345.472 385.018 372.485 393.549 399.4C402.079 426.316 425.375 442.94 445.581 436.533Z" fill="#B5B9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M466.188 376.333C474.59 402.847 465.302 429.468 445.355 435.773C425.408 442.078 402.495 425.713 394.093 399.19C385.691 372.667 394.978 346.055 414.916 339.75C434.855 333.444 457.777 349.845 466.188 376.333Z" fill="#B5BAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M465.648 376.528C473.926 402.652 464.762 428.865 445.125 435.091C425.488 441.317 402.911 425.173 394.66 399.048C386.408 372.923 395.545 346.71 415.173 340.493C434.802 334.277 457.37 350.412 465.648 376.528Z" fill="#B6BBFF"/>
<path d="M444.899 434.38C464.214 428.255 473.262 402.435 465.109 376.708C456.955 350.982 434.686 335.092 415.371 341.217C396.055 347.342 387.007 373.163 395.161 398.889C403.315 424.616 425.583 440.505 444.899 434.38Z" fill="#B6BCFF"/>
<path d="M444.671 433.663C463.689 427.632 472.597 402.21 464.569 376.88C456.54 351.55 434.616 335.904 415.598 341.935C396.581 347.965 387.673 373.388 395.701 398.718C403.729 424.048 425.654 439.693 444.671 433.663Z" fill="#B7BDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M464.036 377.041C471.934 401.979 463.151 426.997 444.452 432.939C425.753 438.881 404.151 423.472 396.244 398.534C388.338 373.596 397.13 348.579 415.828 342.645C434.527 336.712 456.13 352.103 464.036 377.041Z" fill="#B8BEFF"/>
<path d="M441.433 431.757C459.791 425.959 468.415 401.434 460.694 376.98C452.974 352.526 431.833 337.403 413.475 343.202C395.117 349.001 386.494 373.525 394.214 397.979C401.935 422.433 423.075 437.556 441.433 431.757Z" fill="#B8BFFF"/>
<path d="M443.989 431.51C462.116 425.762 470.608 401.532 462.956 377.391C455.305 353.25 434.407 338.339 416.28 344.087C398.153 349.836 389.661 374.066 397.313 398.207C404.964 422.348 425.862 437.258 443.989 431.51Z" fill="#B9C0FF"/>
<path d="M443.761 430.793C461.59 425.139 469.942 401.307 462.416 377.562C454.89 353.817 434.336 339.151 416.508 344.805C398.679 350.459 390.327 374.291 397.853 398.036C405.379 421.78 425.933 436.446 443.761 430.793Z" fill="#B9C1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M461.876 377.723C469.277 401.076 461.07 424.508 443.54 430.07C426.01 435.631 405.797 421.214 398.387 397.852C390.976 374.491 399.201 351.067 416.731 345.506C434.262 339.944 454.474 354.37 461.876 377.723Z" fill="#BAC2FF"/>
<path d="M440.533 428.905C457.706 423.481 465.772 400.536 458.549 377.658C451.326 354.779 431.549 340.629 414.375 346.054C397.202 351.478 389.136 374.423 396.359 397.302C403.582 420.18 423.359 434.33 440.533 428.905Z" fill="#BAC3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M460.805 378.104C467.959 400.66 460.026 423.269 443.098 428.671C426.17 434.073 406.63 420.107 399.485 397.551C392.34 374.996 400.264 352.387 417.192 346.976C434.12 341.565 453.651 355.504 460.805 378.104Z" fill="#BBC4FF"/>
<path d="M442.855 427.931C459.495 422.654 467.289 400.409 460.264 378.245C453.239 356.08 434.055 342.39 417.415 347.667C400.775 352.943 392.98 375.189 400.005 397.353C407.03 419.517 426.215 433.208 442.855 427.931Z" fill="#BCC5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M459.724 378.405C466.63 400.173 458.972 422.02 442.628 427.201C426.284 432.381 407.444 418.938 400.547 397.171C393.65 375.403 401.3 353.556 417.643 348.375C433.987 343.194 452.827 356.638 459.724 378.405Z" fill="#BCC6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M459.193 378.573C465.966 399.951 458.449 421.4 442.371 426.483C426.293 431.566 407.842 418.371 401.052 397.002C394.261 375.633 401.795 354.184 417.873 349.092C433.952 344 452.42 357.204 459.193 378.573Z" fill="#BDC7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M458.653 378.75C465.302 399.721 457.927 420.78 442.177 425.775C426.426 430.769 408.276 417.805 401.627 396.834C394.978 375.863 402.353 354.804 418.104 349.81C433.854 344.815 452.004 357.736 458.653 378.75Z" fill="#BDC8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M458.113 378.919C464.638 399.5 457.396 420.151 441.947 425.057C426.497 429.964 408.684 417.238 402.159 396.666C395.634 376.094 402.876 355.424 418.325 350.527C433.775 345.63 451.588 358.338 458.113 378.919Z" fill="#BEC9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M457.564 379.087C463.966 399.278 456.865 419.531 441.716 424.34C426.568 429.149 409.091 416.68 402.69 396.489C396.289 376.297 403.389 356.044 418.547 351.244C433.704 346.444 451.181 358.905 457.564 379.087Z" fill="#BFCAFF"/>
<path d="M441.493 423.634C456.348 418.923 463.305 399.061 457.032 379.27C450.759 359.478 433.632 347.253 418.777 351.964C403.922 356.674 396.965 376.537 403.238 396.328C409.511 416.119 426.638 428.345 441.493 423.634Z" fill="#BFCBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M456.502 379.432C462.646 398.827 455.829 418.292 441.265 422.906C426.701 427.519 409.923 415.546 403.779 396.152C397.634 376.758 404.452 357.293 419.007 352.679C433.562 348.065 450.357 360.038 456.502 379.432Z" fill="#C0CCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M455.962 379.601C461.982 398.596 455.307 417.681 441.043 422.188C426.78 426.696 410.339 414.98 404.319 395.975C398.298 376.97 404.974 357.895 419.237 353.396C433.5 348.898 449.941 360.605 455.962 379.601Z" fill="#C0CDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M455.421 379.769C461.318 398.366 454.775 417.043 440.813 421.471C426.851 425.899 410.711 414.386 404.85 395.789C398.989 377.192 405.496 358.515 419.467 354.087C433.438 349.659 449.525 361.172 455.421 379.769Z" fill="#C1CEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M454.908 379.946C460.672 398.145 454.271 416.423 440.601 420.754C426.931 425.084 411.18 413.846 405.408 395.639C399.635 377.431 406.036 359.161 419.706 354.822C433.376 350.483 449.118 361.738 454.908 379.946Z" fill="#C2CFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M454.35 380.114C459.99 397.923 453.731 415.794 440.362 420.036C426.993 424.278 411.57 413.279 405.93 395.47C400.29 377.661 406.55 359.781 419.919 355.539C433.288 351.297 448.711 362.305 454.35 380.114Z" fill="#C2D0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M453.81 380.283C459.326 397.702 453.208 415.174 440.132 419.319C427.055 423.463 411.986 412.712 406.488 395.293C400.99 377.874 407.09 360.401 420.158 356.257C433.226 352.112 448.295 362.872 453.81 380.283Z" fill="#C3D1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M453.27 380.451C458.662 397.472 452.686 414.554 439.901 418.602C427.117 422.649 412.402 412.146 407.001 395.125C401.601 378.104 407.594 361.03 420.37 356.974C433.146 352.918 447.878 363.439 453.27 380.451Z" fill="#C3D2FF"/>
<path d="M439.673 417.894C452.151 413.937 457.996 397.255 452.728 380.634C447.46 364.014 433.074 353.747 420.596 357.704C408.119 361.661 402.274 378.342 407.542 394.963C412.81 411.584 427.196 421.85 439.673 417.894Z" fill="#C4D3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M452.199 380.761C457.343 396.985 451.632 413.27 439.45 417.14C427.267 421.01 413.225 410.941 408.082 394.753C402.938 378.565 408.639 362.234 420.822 358.373C433.004 354.512 447.055 364.572 452.199 380.761Z" fill="#C4D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M451.659 380.964C456.679 396.799 451.11 412.686 439.22 416.459C427.329 420.231 413.633 410.445 408.622 394.611C403.61 378.777 409.17 362.89 421.017 359.126C432.863 355.362 446.639 365.13 451.659 380.964Z" fill="#C5D5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M451.119 381.133C456.015 396.568 450.588 412.066 438.998 415.741C427.409 419.416 414.049 409.879 409.153 394.487C404.257 379.096 409.693 363.563 421.282 359.888C432.872 356.212 446.232 365.706 451.119 381.133Z" fill="#C6D6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M450.579 381.31C455.351 396.365 450.056 411.42 438.768 415.024C427.48 418.628 414.465 409.312 409.693 394.275C404.921 379.238 410.215 364.165 421.503 360.561C432.792 356.956 445.815 366.264 450.579 381.31Z" fill="#C6D7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M450.048 381.478C454.687 396.126 449.534 410.817 438.538 414.307C427.542 417.796 414.864 408.745 410.207 394.106C405.55 379.468 410.711 364.758 421.716 361.278C432.721 357.798 445.4 366.831 450.048 381.478Z" fill="#C7D8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M449.508 381.646C454.023 395.895 449.012 410.197 438.317 413.589C427.621 416.981 415.297 408.178 410.773 393.929C406.249 379.68 411.269 365.387 421.964 361.995C432.659 358.603 444.992 367.397 449.508 381.646Z" fill="#C7D9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M448.967 381.815C453.394 395.674 448.489 409.578 438.086 412.872C427.683 416.166 415.695 407.612 411.304 393.761C406.913 379.911 411.791 366.007 422.185 362.704C432.579 359.401 444.576 367.964 448.967 381.815Z" fill="#C8DBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M448.427 381.992C452.695 395.444 447.967 408.949 437.865 412.155C427.763 415.36 416.111 407.045 411.844 393.557C407.577 380.07 412.313 366.591 422.415 363.386C432.517 360.18 444.169 368.531 448.427 381.992Z" fill="#C9DCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M447.896 382.16C452.031 395.222 447.436 408.329 437.635 411.437C427.834 414.546 416.528 406.443 412.411 393.416C408.294 380.389 412.863 367.247 422.672 364.138C432.482 361.03 443.753 369.098 447.896 382.16Z" fill="#C9DDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M447.356 382.328C451.367 394.992 446.913 407.709 437.405 410.72C427.896 413.731 416.935 405.911 412.924 393.247C408.914 380.584 413.358 367.876 422.867 364.856C432.376 361.836 443.336 369.664 447.356 382.328Z" fill="#CADEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M446.816 382.532C450.703 394.797 446.391 407.107 437.183 409.985C427.976 412.863 417.351 405.327 413.465 393.061C409.578 380.796 413.89 368.478 423.097 365.555C432.305 362.633 442.929 370.231 446.816 382.532Z" fill="#CADFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M446.276 382.674C450.039 394.549 445.869 406.46 436.953 409.285C428.038 412.11 417.759 404.778 413.996 392.911C410.233 381.044 414.412 369.115 423.328 366.29C432.243 363.465 442.513 370.798 446.276 382.674Z" fill="#CBE0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M445.745 382.842C449.375 394.354 445.346 405.867 436.723 408.524C428.1 411.18 418.13 404.167 414.536 392.689C410.941 381.212 414.934 369.664 423.549 367.008C432.163 364.351 442.106 371.365 445.745 382.842Z" fill="#CCE1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M445.17 383.01C448.711 394.098 444.78 405.22 436.466 407.859C428.153 410.499 418.609 403.653 415.067 392.566C411.526 381.478 415.448 370.364 423.77 367.725C432.093 365.086 441.69 371.905 445.17 383.01Z" fill="#CCE2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M444.665 383.187C448.056 393.867 444.293 404.592 436.271 407.098C428.25 409.604 418.998 403.042 415.616 392.353C412.234 381.664 415.952 371.019 424.009 368.442C432.066 365.865 441.274 372.498 444.665 383.187Z" fill="#CDE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M444.125 383.356C447.392 393.646 443.77 403.972 436.041 406.425C428.312 408.878 419.414 402.519 416.147 392.256C412.88 381.992 416.51 371.63 424.231 369.186C431.951 366.742 440.866 373.065 444.125 383.356Z" fill="#CDE4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M443.585 383.524C446.728 393.416 443.248 403.352 435.82 405.663C428.392 407.975 419.822 401.908 416.687 392.008C413.553 382.107 417.033 372.188 424.461 369.868C431.889 367.548 440.45 373.676 443.585 383.524Z" fill="#CEE5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M443.053 383.692C446.064 393.194 442.717 402.723 435.59 404.99C428.463 407.257 420.238 401.386 417.227 391.884C414.217 382.381 417.555 372.852 424.691 370.63C431.827 368.407 440.043 374.199 443.053 383.692Z" fill="#CEE6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M442.513 383.869C445.399 392.973 442.194 402.103 435.359 404.238C428.524 406.372 420.653 400.784 417.767 391.68C414.881 382.576 418.077 373.437 424.912 371.312C431.747 369.186 439.627 374.765 442.513 383.869Z" fill="#CFE7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M441.973 384.037C444.736 392.743 441.672 401.483 435.138 403.52C428.604 405.557 421.061 400.217 418.316 391.512C415.572 382.806 418.626 374.066 425.16 372.029C431.694 369.992 439.211 375.332 441.973 384.037Z" fill="#D0E8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M441.433 384.206C444.089 392.521 441.149 400.863 434.908 402.803C428.666 404.742 421.477 399.65 418.839 391.335C416.2 383.019 419.131 374.686 425.372 372.737C431.614 370.789 438.803 375.899 441.433 384.206Z" fill="#D0E9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M440.902 384.374C443.408 392.291 440.628 400.235 434.705 402.085C428.781 403.936 421.911 399.083 419.405 391.166C416.9 383.249 419.68 375.306 425.603 373.455C431.526 371.604 438.388 376.466 440.902 384.374Z" fill="#D1EAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M440.362 384.551C442.743 392.07 440.096 399.606 434.456 401.377C428.817 403.148 422.3 398.525 419.919 391.007C417.537 383.488 420.176 375.952 425.824 374.181C431.473 372.41 437.98 377.032 440.362 384.551Z" fill="#D1EBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M439.857 384.719C442.115 391.804 439.609 398.995 434.261 400.66C428.914 402.325 422.752 398.003 420.485 390.839C418.219 383.674 420.733 376.563 426.081 374.898C431.428 373.233 437.564 377.599 439.857 384.719Z" fill="#D2ECFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M439.281 384.888C441.415 391.618 439.051 398.375 433.969 399.942C428.887 401.51 423.097 397.392 420.963 390.661C418.83 383.931 421.202 377.183 426.275 375.607C431.349 374.03 437.148 378.157 439.281 384.888Z" fill="#D3EDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M438.75 385.056C440.751 391.397 438.529 397.746 433.775 399.225C429.02 400.704 423.54 396.825 421.53 390.493C419.52 384.161 421.751 377.803 426.506 376.324C431.26 374.845 436.741 378.724 438.75 385.056Z" fill="#D3EEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M438.211 385.233C440.088 391.166 438.007 397.126 433.545 398.517C429.082 399.907 423.947 396.258 422.035 390.325C420.123 384.392 422.239 378.432 426.692 377.041C431.145 375.651 436.316 379.291 438.211 385.233Z" fill="#D4EFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M437.67 385.401C439.441 390.945 437.475 396.506 433.314 397.799C429.153 399.092 424.363 395.692 422.61 390.148C420.857 384.604 422.796 379.052 426.957 377.75C431.119 376.448 435.917 379.875 437.67 385.401Z" fill="#D4F0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M437.13 385.569C438.759 390.715 436.953 395.878 433.093 397.082C429.233 398.286 424.779 395.125 423.141 389.997C421.504 384.87 423.319 379.689 427.188 378.485C431.057 377.28 435.501 380.424 437.13 385.569Z" fill="#D5F1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M436.59 385.747C438.095 390.493 436.431 395.258 432.863 396.374C429.295 397.489 425.187 394.602 423.681 389.82C422.176 385.038 423.85 380.3 427.409 379.193C430.968 378.086 435.085 380.991 436.59 385.747Z" fill="#D6F2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M436.059 385.915C437.44 390.263 435.908 394.638 432.641 395.656C429.374 396.675 425.558 394 424.221 389.652C422.885 385.304 424.372 380.929 427.639 379.911C430.906 378.892 434.677 381.558 436.059 385.915Z" fill="#D6F3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M435.519 386.074C436.776 390.033 435.377 394.044 432.411 394.93C429.445 395.816 426.01 393.425 424.762 389.466C423.513 385.507 424.894 381.54 427.869 380.61C430.844 379.68 434.262 382.125 435.519 386.074Z" fill="#D7F4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M434.979 386.251C436.112 389.794 434.855 393.389 432.181 394.222C429.507 395.054 426.426 392.867 425.293 389.307C424.16 385.747 425.417 382.169 428.091 381.336C430.764 380.504 433.854 382.691 434.979 386.251Z" fill="#D7F5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M434.438 386.428C435.448 389.59 434.332 392.769 431.959 393.513C429.587 394.257 426.842 392.309 425.833 389.147C424.823 385.986 425.948 382.798 428.321 382.063C430.693 381.328 433.438 383.258 434.438 386.428Z" fill="#D8F6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M433.907 386.597C434.793 389.369 433.81 392.149 431.729 392.796C429.649 393.442 427.249 391.742 426.373 388.979C425.496 386.216 426.47 383.426 428.551 382.78C430.631 382.133 433.022 383.825 433.907 386.597Z" fill="#D8F7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M433.367 386.765C434.12 389.138 433.288 391.521 431.499 392.078C429.711 392.636 427.666 391.193 426.913 388.802C426.16 386.411 426.993 384.046 428.772 383.488C430.552 382.93 432.615 384.392 433.367 386.765Z" fill="#D9F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M432.827 386.96C433.456 388.943 432.756 390.927 431.278 391.388C429.799 391.848 428.073 390.644 427.444 388.66C426.816 386.676 427.515 384.693 429.003 384.232C430.49 383.772 432.199 384.958 432.827 386.96Z" fill="#DAF9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M432.287 387.11C432.792 388.687 432.234 390.281 431.048 390.653C429.861 391.025 428.489 390.059 427.984 388.474C427.48 386.889 428.037 385.304 429.233 384.932C430.428 384.56 431.791 385.525 432.287 387.11Z" fill="#DAFAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M431.756 387.279C432.128 388.465 431.712 389.661 430.818 389.935C429.923 390.21 428.896 389.493 428.525 388.306C428.153 387.119 428.525 385.924 429.41 385.649C430.295 385.375 431.376 386.074 431.756 387.279Z" fill="#DBFBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M431.216 387.447C431.319 387.599 431.385 387.772 431.409 387.954C431.433 388.136 431.415 388.321 431.355 388.494C431.294 388.668 431.195 388.825 431.063 388.953C430.932 389.081 430.772 389.176 430.598 389.231C430.423 389.287 430.237 389.301 430.056 389.272C429.875 389.243 429.703 389.172 429.554 389.065C429.406 388.958 429.284 388.817 429.199 388.655C429.114 388.492 429.068 388.312 429.065 388.129C428.917 387.811 428.899 387.448 429.015 387.117C429.131 386.787 429.371 386.514 429.684 386.358C430.031 386.308 430.383 386.393 430.668 386.596C430.953 386.799 431.149 387.103 431.216 387.447Z" fill="#DBFCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M430.676 387.615C430.745 387.775 430.752 387.954 430.694 388.119C430.637 388.283 430.519 388.419 430.366 388.501C430.192 388.526 430.016 388.483 429.874 388.381C429.731 388.278 429.635 388.124 429.604 387.952C429.528 387.793 429.518 387.611 429.576 387.445C429.634 387.279 429.756 387.143 429.914 387.066C430.207 386.96 430.552 387.226 430.676 387.615Z" fill="#DCFDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M463.904 430.07C463.904 430.07 457.813 449.092 438.503 447.489C422.035 446.152 417.715 432.673 417.484 431.841C427.976 426.598 439.548 426.147 448.472 427.024C453.581 427.48 458.627 428.48 463.523 430.008C463.651 430.022 463.778 430.042 463.904 430.07Z" fill="url(#paint129_radial_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M411.189 264.511C400.122 277.396 423.035 316.999 436.705 316.007C446.86 315.272 446.843 283.577 438.804 272.215C430.765 260.853 417.227 257.47 411.189 264.511Z" fill="url(#paint130_linear_292_86830)"/>
<path d="M365 295C366.6 278.2 390.333 254 402 244C361 273 315 266 307 267C299 268 285 286 289 310C293 334 321 359 342 358C363 357 363 316 365 295Z" stroke="#FFD329"/>
<mask id="mask0_292_86830" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="286" y="246" width="135" height="128">
<path d="M382 301.5C383.829 282.347 400 262 421 246C372 267.5 318.656 266.435 309.5 267.5C288 270 283.427 286.639 288 314C292.573 341.361 333.5 378 354.5 373.5C378.003 368.464 379.713 325.441 382 301.5Z" fill="#C4C4C4"/>
</mask>
<g mask="url(#mask0_292_86830)">
<path d="M290.584 236.039L279.527 375.328L409.381 385.641L420.438 246.353L290.584 236.039Z" fill="#F2950D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M395.934 276.608C407.798 314.041 378.838 356.629 331.25 371.719C307.451 379.264 283.502 378.511 264.016 371.188L276.535 372.179L286.69 244.248L271.346 243.035C276.796 240.291 282.457 237.987 288.274 236.145C335.871 221.055 384.079 239.165 395.934 276.608Z" fill="#F2950D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M394.243 277.148C405.877 313.855 377.484 355.601 330.825 370.399C307.496 377.803 284.016 377.059 264.919 369.877L276.641 370.816L286.601 245.382L272.099 244.222C277.444 241.534 282.995 239.277 288.7 237.474C335.385 222.676 382.61 240.441 394.243 277.148Z" fill="#F2960D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M392.553 277.679C403.956 313.651 376.129 354.565 330.409 369.062C307.54 376.315 284.53 375.589 265.778 368.549L276.721 369.434L286.46 246.516L272.799 245.426C278.037 242.794 283.475 240.582 289.063 238.811C334.845 224.305 381.158 241.725 392.553 277.679Z" fill="#F2970D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M390.87 278.211C402.034 313.448 374.774 353.529 329.984 367.734C307.584 374.819 285.043 374.128 266.707 367.229L276.862 368.044L286.424 247.605L273.604 246.586C278.737 244.008 284.065 241.84 289.54 240.104C334.331 225.926 379.697 242.982 390.87 278.211Z" fill="#F2980D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M389.179 278.751C400.113 313.288 373.42 352.493 329.559 366.423C307.629 373.375 285.566 372.675 267.584 365.936L276.942 366.68L286.336 248.756L274.357 247.809C279.384 245.282 284.603 243.159 289.966 241.459C333.818 227.555 378.245 244.248 389.179 278.751Z" fill="#F2990D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M387.488 279.282C398.192 313.049 372.065 351.466 329.143 365.077C307.682 371.878 286.079 371.196 268.505 364.599L277.084 365.281L286.248 249.89L275.11 249.004C280.028 246.536 285.135 244.463 290.382 242.805C333.313 229.176 376.784 245.515 387.488 279.282Z" fill="#F29A0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M385.798 279.805C396.271 312.837 370.747 350.412 328.719 363.731C307.727 370.391 286.593 369.718 269.399 363.262L277.191 363.882L286.159 251.014L275.863 250.2C280.674 247.778 285.671 245.746 290.807 244.125C332.8 230.805 375.333 246.79 385.798 279.805Z" fill="#F29B0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M384.08 280.354C394.323 312.66 369.33 349.429 328.302 362.42C307.771 368.929 287.106 368.274 270.302 361.96L277.305 362.518L285.371 260.942L263.742 259.224C272.094 253.192 281.361 248.543 291.188 245.453C332.286 232.426 373.872 248.056 384.08 280.354Z" fill="#F29C0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M382.415 280.894C392.42 312.456 368.001 348.366 327.877 361.083C307.815 367.45 287.62 366.813 271.214 360.64L277.411 361.136L285.291 261.872L264.786 260.242C272.962 254.345 282.031 249.801 291.648 246.781C331.772 234.055 372.411 249.332 382.415 280.894Z" fill="#F29D0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M380.733 281.425C390.472 312.261 366.647 347.33 327.452 359.755C307.859 365.954 288.133 365.352 272.091 359.321L277.518 359.755L285.22 262.793L265.831 261.252C273.817 255.496 282.674 251.059 292.064 248.11C331.259 235.676 370.959 250.58 380.733 281.425Z" fill="#F29E0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M379.042 281.965C388.577 312.075 365.292 346.294 327.036 358.426C307.903 364.493 288.656 363.89 273.029 358.001L277.668 358.373L285.185 263.722L266.92 262.27C274.714 256.651 283.359 252.318 292.525 249.438C330.754 237.297 369.498 251.864 379.042 281.965Z" fill="#F29F0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M377.351 282.461C386.656 311.827 363.938 345.231 326.611 357.063C307.947 362.987 289.169 362.376 273.888 356.646L277.739 356.956L285.07 264.617L267.929 263.253C275.528 257.783 283.954 253.568 292.888 250.766C330.241 238.926 368.046 253.14 377.351 282.461Z" fill="#F2A00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M375.66 283.037C384.735 311.668 362.583 344.231 326.195 355.77C307.992 361.544 289.683 360.968 274.782 355.362L277.854 355.61L284.999 265.582L268.983 264.307C276.395 258.957 284.619 254.833 293.339 252.095C329.727 240.556 366.585 254.406 375.66 283.037Z" fill="#F2A10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M373.969 283.568C382.823 311.464 361.229 343.194 325.77 354.415C308.063 360.038 290.196 359.471 275.677 354.016L277.961 354.202L284.92 266.521L270.01 265.334C277.243 260.118 285.266 256.096 293.773 253.423C329.188 242.176 365.133 255.681 373.969 283.568Z" fill="#F2A20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.278 284.108C380.892 311.269 359.883 342.167 325.354 353.113C308.089 358.586 290.71 358.037 276.579 352.723L278.067 352.847L284.849 267.406L271.063 266.308C278.098 261.237 285.9 257.326 294.171 254.725C328.7 243.806 363.716 256.948 372.278 284.108Z" fill="#F2A30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M370.587 284.64C378.971 311.074 358.519 341.131 324.929 351.785C308.107 357.098 291.223 356.576 277.473 351.404L278.181 351.466L284.777 268.363L272.108 267.353C278.953 262.417 286.545 258.611 294.596 256.08C328.187 245.435 362.211 258.214 370.587 284.64Z" fill="#F2A40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M368.905 285.18C377.041 310.862 357.165 340.086 324.504 350.456C308.178 355.637 291.746 355.114 278.368 350.093C271.908 347.729 266.001 344.064 261.015 339.324L279.041 340.75L284.716 269.284L273.162 268.398C279.815 263.594 287.199 259.895 295.03 257.444C327.682 247.038 360.759 259.489 368.905 285.18Z" fill="#F2A50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M367.258 285.711C375.164 310.676 355.854 339.059 324.132 349.128C308.266 354.158 292.303 353.653 279.315 348.774C273.035 346.481 267.295 342.921 262.449 338.315L279.164 339.634L284.68 270.214L274.251 269.381C280.718 264.723 287.893 261.135 295.499 258.754C327.169 248.685 359.289 260.756 367.258 285.711Z" fill="#F2A60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M365.487 286.252C373.163 310.472 354.42 338.031 323.627 347.799C308.231 352.679 292.737 352.227 280.13 347.454C274.034 345.227 268.462 341.77 263.759 337.296L279.173 338.527L284.486 271.144L275.18 270.4C281.453 265.874 288.412 262.388 295.792 260.074C326.655 250.306 357.846 262.031 365.487 286.252Z" fill="#F2A70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M363.831 286.792C371.277 310.277 353.101 336.995 323.246 346.471C308.31 351.2 293.286 350.731 281.059 346.135C275.149 343.974 269.746 340.622 265.184 336.287L279.297 337.412L283.246 287.669L261.997 285.986C269.346 275.182 281.351 266.131 296.278 261.402C326.141 251.926 356.385 263.297 363.831 286.792Z" fill="#F2A80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M362.14 287.323C369.356 310.083 351.755 335.959 322.821 345.134C308.354 349.721 293.799 349.261 281.962 344.815C276.234 342.728 270.999 339.481 266.583 335.277L279.385 336.296L283.21 288.111L263.484 286.544C270.567 276.085 282.236 267.318 296.703 262.731C325.646 253.556 354.933 264.564 362.14 287.323Z" fill="#F2A90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M360.45 287.854C367.435 309.879 350.401 334.932 322.379 343.805C294.357 352.679 266.035 342.034 259.058 320.01L280.528 321.71L283.184 288.554L264.99 287.102C271.878 276.98 283.131 268.505 297.129 264.077C325.124 255.185 353.473 265.839 360.45 287.854Z" fill="#F2AA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M358.767 288.395C365.514 309.649 349.028 333.896 321.98 342.477C294.933 351.058 267.504 340.759 260.758 319.452L280.599 321.028L283.14 288.97L266.477 287.651C273.135 277.856 284.016 269.665 297.545 265.37C324.61 256.779 352.012 267.105 358.767 288.395Z" fill="#F2AB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M357.076 288.935C363.592 309.48 347.691 332.86 321.555 341.184C295.42 349.509 268.965 339.528 262.449 318.974L280.652 320.417L283.148 289.431L268.009 288.226C274.436 278.777 284.946 270.86 298.014 266.716C324.096 258.436 350.56 268.381 357.076 288.935Z" fill="#F2AC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M355.385 289.466C361.663 309.286 346.337 331.832 321.14 339.82C295.942 347.808 270.426 338.217 264.14 318.398L280.705 319.718L283.069 289.865L269.461 288.784C275.659 279.672 285.787 272.038 298.386 268.044C323.583 260.056 349.099 269.647 355.385 289.466Z" fill="#F2AD0D"/>
<path d="M320.716 338.499C344.977 330.806 359.741 309.096 353.691 290.009C347.641 270.922 323.07 261.685 298.809 269.378C274.548 277.072 259.785 298.781 265.834 317.869C271.884 336.956 296.455 346.193 320.716 338.499Z" fill="#F2AF0D"/>
<path d="M320.293 337.165C343.622 329.768 357.819 308.895 352.003 290.544C346.186 272.194 322.56 263.314 299.231 270.712C275.902 278.11 261.706 298.983 267.522 317.333C273.338 335.684 296.965 344.563 320.293 337.165Z" fill="#F2B00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M350.312 291.078C355.899 308.692 342.273 328.733 319.873 335.835C297.474 342.938 274.791 334.409 269.213 316.795C263.635 299.181 277.252 279.14 299.652 272.038C322.051 264.936 344.734 273.455 350.312 291.078Z" fill="#F2B10D"/>
<path d="M319.451 334.506C340.915 327.7 353.978 308.497 348.627 291.615C343.277 274.733 321.539 266.565 300.074 273.371C278.61 280.178 265.548 299.381 270.898 316.263C276.249 333.145 297.987 341.313 319.451 334.506Z" fill="#F2B20D"/>
<path d="M319.031 333.181C339.563 326.67 352.058 308.3 346.939 292.15C341.821 276 321.027 268.186 300.494 274.697C279.962 281.207 267.467 299.578 272.586 315.728C277.705 331.878 298.499 339.692 319.031 333.181Z" fill="#F2B30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.248 292.681C350.135 308.099 338.209 325.633 318.607 331.85C299.005 338.067 279.165 330.601 274.295 315.228C269.426 299.854 281.334 282.284 300.936 276.067C320.537 269.851 340.361 277.263 345.248 292.681Z" fill="#F2B40D"/>
<path d="M318.188 330.531C336.851 324.612 348.208 307.913 343.555 293.232C338.902 278.55 320 271.446 301.337 277.365C282.674 283.283 271.317 299.982 275.97 314.663C280.624 329.345 299.525 336.449 318.188 330.531Z" fill="#F2B50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M341.866 293.752C346.293 307.7 335.5 323.561 317.766 329.176C300.032 334.79 282.077 328.051 277.659 314.121C273.241 300.191 284.025 284.312 301.759 278.698C319.493 273.083 337.448 279.805 341.866 293.752Z" fill="#F2B60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M340.175 294.293C344.362 307.505 334.145 322.534 317.341 327.856C300.537 333.178 283.538 326.793 279.35 313.581C275.163 300.368 285.38 285.339 302.175 280.008C318.97 274.677 335.987 281.08 340.175 294.293Z" fill="#F2B70D"/>
<path d="M314.767 326.148C330.58 321.153 340.215 307.021 336.288 294.583C332.361 282.144 316.359 276.11 300.547 281.105C284.734 286.099 275.099 300.231 279.026 312.67C282.953 325.108 298.955 331.142 314.767 326.148Z" fill="#F2B80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M336.802 295.364C340.52 307.107 331.436 320.462 316.5 325.199C301.564 329.937 286.451 324.252 282.724 312.509C278.996 300.766 288.089 287.403 303.025 282.665C317.961 277.927 333.074 283.613 336.802 295.364Z" fill="#F2B90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M335.11 295.896C338.598 306.903 330.081 319.434 316.084 323.871C302.086 328.308 287.903 322.985 284.414 311.969C280.926 300.952 289.443 288.439 303.441 283.993C317.438 279.548 331.613 284.888 335.11 295.896Z" fill="#F2BA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M333.419 296.436C336.677 306.709 328.727 318.398 315.659 322.543C302.591 326.687 289.364 321.71 286.106 311.438C282.848 301.165 290.798 289.466 303.866 285.322C316.934 281.177 330.161 286.154 333.419 296.436Z" fill="#F2BB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M331.728 296.967C334.756 306.514 327.372 317.335 315.243 321.214C303.113 325.093 290.825 320.444 287.797 310.897C284.769 301.351 292.153 290.529 304.282 286.659C316.411 282.789 328.7 287.421 331.728 296.967Z" fill="#F2BC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M330.073 297.516C332.862 306.319 326.053 316.344 314.853 319.895C303.654 323.446 292.312 319.177 289.523 310.375C286.734 301.572 293.543 291.547 304.743 287.996C315.942 284.445 327.248 288.66 330.073 297.516Z" fill="#F2BD0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M328.346 298.039C330.905 306.115 324.663 315.299 314.393 318.558C304.123 321.817 293.773 317.885 291.179 309.826C288.585 301.767 294.862 292.566 305.132 289.316C315.402 286.066 325.788 289.962 328.346 298.039Z" fill="#F2BE0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M326.655 298.579C328.984 305.92 323.309 314.263 313.977 317.229C304.645 320.196 295.19 316.636 292.861 309.259C290.533 301.882 296.217 293.567 305.548 290.609C314.88 287.651 324.336 291.237 326.655 298.579Z" fill="#F2BF0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M324.973 299.11C327.062 305.717 321.954 313.235 313.552 315.892C305.15 318.549 296.65 315.361 294.552 308.754C292.454 302.148 297.571 294.638 305.973 291.972C314.375 289.307 322.875 292.504 324.973 299.11Z" fill="#F2C00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M323.282 299.65C325.141 305.522 320.626 312.199 313.136 314.564C305.646 316.928 298.085 314.094 296.243 308.223C294.402 302.351 298.899 295.665 306.389 293.301C313.88 290.936 321.423 293.77 323.282 299.65Z" fill="#F2C10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M321.591 300.173C323.22 305.309 319.244 311.154 312.737 313.226C306.23 315.299 299.589 312.81 297.96 307.674C296.331 302.537 300.307 296.693 306.814 294.62C313.321 292.548 319.962 295.045 321.591 300.173Z" fill="#F2C20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M319.9 300.722C321.299 305.15 317.89 310.136 312.286 311.907C306.682 313.678 301.024 311.553 299.625 307.151C298.227 302.75 301.635 297.738 307.231 295.958C312.826 294.177 318.501 296.312 319.9 300.722Z" fill="#F2C30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M318.209 301.253C319.378 304.928 316.536 309.1 311.87 310.578C307.204 312.057 302.476 310.286 301.316 306.611C300.157 302.936 302.99 298.765 307.656 297.286C312.321 295.807 317.049 297.587 318.209 301.253Z" fill="#F2C40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M316.518 301.793C317.447 304.725 315.181 308.063 311.445 309.25C307.709 310.437 303.937 309.011 303.007 306.08C302.078 303.148 304.344 299.801 308.08 298.614C311.817 297.428 315.588 298.853 316.518 301.793Z" fill="#F2C50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M314.827 302.325C315.526 304.53 313.826 307.036 311.029 307.922C308.231 308.807 305.389 307.745 304.689 305.54C303.99 303.334 305.699 300.837 308.496 299.943C311.294 299.048 314.136 300.12 314.827 302.325Z" fill="#F2C60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M313.145 302.83C313.605 304.291 312.472 305.965 310.604 306.558C308.735 307.151 306.85 306.434 306.38 304.973C305.911 303.512 307.053 301.829 308.921 301.236C310.789 300.642 312.675 301.395 313.145 302.83Z" fill="#F2C70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M311.48 303.396C311.677 303.72 311.769 304.098 311.742 304.476C311.714 304.854 311.57 305.214 311.329 305.507C311.088 305.799 310.761 306.009 310.395 306.107C310.029 306.205 309.641 306.187 309.286 306.055C308.931 305.922 308.626 305.683 308.413 305.369C308.2 305.055 308.09 304.683 308.099 304.304C308.108 303.924 308.234 303.558 308.461 303.254C308.688 302.95 309.003 302.724 309.364 302.608C309.751 302.441 310.187 302.429 310.581 302.576C310.975 302.723 311.298 303.017 311.48 303.396Z" fill="#F2C80D"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M248.425 237.97L248.637 238.182L248.425 237.97Z" fill="url(#paint131_radial_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M426.754 252.139C426.754 252.139 401.787 278.033 362.22 292.123C322.653 306.213 293.72 296.4 293.72 296.4C303.653 263.35 395.288 237.509 426.754 252.139Z" fill="url(#paint132_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M357.271 270.568C333.871 274.633 313.552 274.376 296.287 270.373H296.225C255.499 260.995 231.789 231.027 224.883 188.572C217.8 144.745 231.434 84.0735 283.228 62.4034C339.006 39.0418 430.331 53.2288 452.872 112.288C457.176 123.6 459.587 135.544 460.008 147.641C462.204 202.679 424.585 258.923 357.271 270.568Z" fill="url(#paint133_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M330.197 258.737C293.295 266.521 266.23 256.372 248.62 238.182L248.407 237.97C235.534 224.562 227.743 206.806 224.901 188.572C222.183 171.33 216.844 128.06 248.168 94.9573C274.667 66.9287 345.708 54.628 385.169 75.0229C396.749 81.0183 405.603 89.8121 409.817 101.847C428.348 154.876 397.555 244.55 330.197 258.737Z" fill="url(#paint134_radial_292_86830)"/>
<path style="mix-blend-mode:screen" d="M433.496 178.353C438.022 121.342 402.544 72.0174 354.253 68.182C305.963 64.3466 263.147 107.453 258.621 164.463C254.096 221.474 289.574 270.799 337.864 274.634C386.154 278.469 428.97 235.363 433.496 178.353Z" fill="url(#paint135_radial_292_86830)"/>
<path style="mix-blend-mode:screen" d="M413.806 176.789C417.312 132.616 389.824 94.3978 352.408 91.4261C314.992 88.4544 281.818 121.854 278.312 166.027C274.805 210.2 302.294 248.418 339.709 251.39C377.125 254.362 410.299 220.961 413.806 176.789Z" fill="url(#paint136_radial_292_86830)"/>
<path d="M385.82 171.731C388.266 140.912 372.547 114.523 350.71 112.788C328.873 111.054 309.187 134.631 306.74 165.45C304.294 196.269 320.013 222.658 341.85 224.392C363.687 226.127 383.373 202.549 385.82 171.731Z" fill="white"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M267.017 167.522C268.434 214.812 257.039 254.672 241.581 256.558C226.122 258.444 212.426 221.631 211.018 174.341C209.61 127.051 220.996 87.1819 236.454 85.3045C251.913 83.4271 265.609 120.232 267.017 167.522Z" fill="url(#paint137_radial_292_86830)"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M260.713 168.292C261.811 204.929 252.984 235.818 241.005 237.279C229.026 238.74 218.411 210.216 217.322 173.535C216.233 136.854 225.051 106.009 237.03 104.548C249.009 103.087 259.616 131.647 260.713 168.292Z" fill="url(#paint138_radial_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M251.833 169.399C252.586 194.966 247.451 216.379 240.368 217.221C233.285 218.062 226.946 198.03 226.202 172.463C225.458 146.897 230.585 125.483 237.667 124.642C244.75 123.801 251.09 143.833 251.833 169.399Z" fill="white"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M330.196 258.737C293.294 266.521 266.229 256.372 248.619 238.182C251.488 240.945 274.339 267.902 329.284 252.051C391.463 234.117 437.201 118.523 385.186 74.9875C396.766 80.9829 405.62 89.7767 409.834 101.812C428.347 154.876 397.554 244.55 330.196 258.737Z" fill="url(#paint139_linear_292_86830)"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M237.269 111.589C237.269 111.589 231.868 125.944 252.692 129.805C273.516 133.666 338.43 112.811 363.761 109.136C389.091 105.46 397.962 94.8068 394.474 88.165C390.985 81.5231 357.395 59.8706 302.573 71.4275C247.752 82.9843 237.269 111.589 237.269 111.589Z" fill="url(#paint140_linear_292_86830)"/>
<path style="mix-blend-mode:screen" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M256.756 238.829C256.756 238.829 272.356 261.553 315.871 253.795C348.107 248.056 360.75 222.074 360.75 222.074C360.75 222.074 342.22 242.743 304.503 247.038C279.341 249.89 256.756 238.829 256.756 238.829Z" fill="url(#paint141_linear_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M266.832 208.073C266.832 208.073 269.744 214.272 280.307 214.272C290.869 214.272 297.89 202.671 297.89 202.671C297.89 202.671 294.729 221.356 283.724 222.472C270.612 223.809 266.832 208.073 266.832 208.073Z" fill="url(#paint142_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M334.278 68.1508C334.278 68.1508 335.933 81.6204 354.535 86.3583C377.395 92.1854 384.637 80.1592 384.053 79.8227C383.468 79.4862 364.46 68.0356 334.278 68.1508Z" fill="white"/>
<g style="mix-blend-mode:soft-light">
<path fill-rule="evenodd" clip-rule="evenodd" d="M252.798 242.15C252.798 242.15 277.296 274.659 336.403 262.429C395.51 250.2 414.005 186.27 414.722 146.206C414.722 146.206 408.214 236.376 338.174 255.478C280.413 271.232 252.798 242.15 252.798 242.15Z" fill="white"/>
</g>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M425.116 109.773C417.272 117.478 408.754 164.679 436.006 184.693C436.006 184.693 447.763 187.554 453.598 164.05C459.937 138.554 438.972 113.067 425.116 109.773Z" fill="url(#paint143_linear_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M419.618 249.881C416.076 250.572 396.501 255.292 385.23 279.362C372.755 305.929 376.819 359.507 355.907 369.611C334.995 379.716 308.452 347.179 299.474 326.377C299.12 325.571 298.792 324.765 298.465 323.986C301.882 331.31 314.481 355.522 335.083 365.741C359.537 377.829 366.691 352.174 369.763 336.862C374.34 313.864 377.51 252.493 419.618 249.881Z" fill="url(#paint144_linear_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M357.271 270.568C333.871 274.633 313.552 274.376 296.287 270.373H296.225C296.225 270.373 363.194 279.884 393.464 247.835C423.735 215.786 418.954 156.408 418.954 156.408L460.008 147.641C462.204 202.679 424.585 258.923 357.271 270.568Z" fill="url(#paint145_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M437.609 178.042C416.528 167.23 417.723 123.278 425.125 109.773C426.128 108.064 427.401 106.53 428.897 105.23C428.897 105.23 417.511 122.986 429.694 129.646C429.694 129.646 483.426 83.6573 516.034 87.1908C518.557 103.663 458.689 188.847 437.609 178.042Z" fill="#F2A30D" style="mix-blend-mode:multiply"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M460.061 89.3339C460.061 89.3339 475.431 92.6814 479.937 96.2768C484.444 99.8723 488.552 139.174 488.552 139.174C488.552 139.174 523.205 94.6739 516.529 84.2684C509.854 73.8628 460.061 89.3339 460.061 89.3339Z" fill="url(#paint146_linear_292_86830)"/>
<path style="mix-blend-mode:multiply" opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M463.904 430.07C463.904 430.07 457.813 449.092 438.503 447.489C422.035 446.152 417.715 432.673 417.484 431.841C418.113 432.116 437.343 441.255 447.941 427.697C448.118 427.449 448.304 427.236 448.481 426.988C450.783 423.791 452.66 419.425 453.74 413.563C459.734 381.469 444.284 354.362 443.399 352.83C444.178 353.476 471.314 377.316 463.904 430.07Z" fill="url(#paint147_linear_292_86830)"/>
<path d="M462.205 88.9835L428.078 106.299C419.521 114.855 424.513 126.161 428.078 130.745L480.032 95.6042L462.205 88.9835Z" fill="#FFC65A"/>
<path style="mix-blend-mode:screen" fill-rule="evenodd" clip-rule="evenodd" d="M294.154 296.436C294.154 296.436 289.798 307.063 297.243 315.591C304.689 324.119 311.94 309.613 320.688 306.877C329.435 304.14 333.366 308.303 347.514 304.149C361.662 299.996 358.944 293.248 358.944 293.248C358.944 293.248 337.501 299.562 322.335 300.049C312.813 300.267 303.312 299.049 294.154 296.436Z" fill="url(#paint148_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M294.153 296.436C294.153 296.436 293.702 307.479 300.138 309.409C306.575 311.34 307.717 298.738 307.717 298.738C307.717 298.738 297.872 298.039 294.153 296.436Z" fill="white"/>
<g style="mix-blend-mode:soft-light">
<path fill-rule="evenodd" clip-rule="evenodd" d="M478.361 84.1975C478.361 84.1975 477.025 87.7398 489.871 88.9708C502.718 90.2017 507.578 81.6205 507.578 81.6205C507.578 81.6205 495.555 79.4242 478.361 84.1975Z" fill="white"/>
</g>
<path d="M460.913 363.614C485.221 355.906 496.606 323.409 486.344 291.029C476.081 258.65 448.057 238.65 423.749 246.358C399.442 254.066 388.056 286.563 398.318 318.943C408.581 351.322 436.605 371.322 460.913 363.614Z" fill="#BDC7FF"/>
<path d="M460.571 362.533C484.431 354.967 495.607 323.069 485.534 291.286C475.46 259.504 447.952 239.872 424.092 247.439C400.232 255.005 389.055 286.903 399.129 318.686C409.202 350.468 436.711 370.099 460.571 362.533Z" fill="#BDC8FF"/>
<path d="M460.225 361.435C483.633 354.013 494.597 322.718 484.715 291.537C474.832 260.356 447.844 241.096 424.437 248.519C401.029 255.942 390.064 287.236 399.947 318.417C409.83 349.598 436.817 368.858 460.225 361.435Z" fill="#BEC9FF"/>
<path d="M459.88 360.355C482.841 353.074 493.597 322.382 483.905 291.803C474.212 261.223 447.742 242.336 424.782 249.617C401.821 256.897 391.065 287.59 400.757 318.169C410.449 348.749 436.92 367.636 459.88 360.355Z" fill="#BFCAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M483.088 292.057C492.597 322.034 482.053 352.126 459.547 359.264C437.041 366.401 411.082 347.884 401.556 317.898C392.029 287.912 402.591 257.829 425.106 250.691C447.621 243.554 473.589 262.071 483.088 292.057Z" fill="#BFCBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M482.274 292.314C491.588 321.697 481.256 351.187 459.201 358.183C437.147 365.179 411.711 347.025 402.397 317.641C393.083 288.258 403.415 258.768 425.478 251.781C447.541 244.793 472.96 262.93 482.274 292.314Z" fill="#C0CCFF"/>
<path d="M458.85 357.105C480.459 350.253 490.58 321.364 481.457 292.579C472.334 263.794 447.42 246.014 425.812 252.867C404.203 259.719 394.081 288.608 403.205 317.393C412.328 346.178 437.241 363.958 458.85 357.105Z" fill="#C0CDFF"/>
<path d="M458.505 356.007C479.662 349.299 489.571 321.013 480.639 292.83C471.706 264.646 447.314 247.238 426.157 253.947C405 260.656 395.091 288.941 404.024 317.125C412.956 345.308 437.348 362.716 458.505 356.007Z" fill="#C1CEFF"/>
<path d="M458.16 354.927C478.869 348.36 488.57 320.677 479.828 293.095C471.086 265.514 447.211 248.478 426.502 255.045C405.793 261.612 396.092 289.295 404.834 316.877C413.576 344.458 437.451 361.494 458.16 354.927Z" fill="#C2CFFF"/>
<path d="M457.817 353.838C478.074 347.414 487.563 320.331 479.01 293.346C470.457 266.361 447.102 249.693 426.845 256.116C406.588 262.54 397.099 289.623 405.652 316.608C414.205 343.593 437.56 360.262 457.817 353.838Z" fill="#C2D0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M478.201 293.607C486.559 319.988 477.316 346.467 457.475 352.755C437.634 359.042 414.836 342.739 406.47 316.348C398.103 289.958 407.355 263.488 427.196 257.209C447.037 250.931 469.835 267.225 478.201 293.607Z" fill="#C3D1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M477.387 293.863C485.559 319.652 476.501 345.528 457.13 351.665C437.758 357.802 415.456 341.88 407.284 316.091C399.112 290.303 408.169 264.427 427.541 258.29C446.913 252.153 469.215 268.075 477.387 293.863Z" fill="#C3D2FF"/>
<path d="M456.785 350.588C475.69 344.593 484.545 319.317 476.562 294.131C468.58 268.946 446.783 253.389 427.878 259.384C408.972 265.379 400.118 290.655 408.1 315.841C416.082 341.026 437.879 356.583 456.785 350.588Z" fill="#C4D3FF"/>
<path d="M456.44 349.499C474.897 343.646 483.544 318.972 475.752 294.388C467.96 269.804 446.681 254.62 428.223 260.473C409.765 266.326 401.118 291 408.91 315.584C416.702 340.168 437.982 355.352 456.44 349.499Z" fill="#C4D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M474.943 294.643C482.54 318.624 474.111 342.694 456.103 348.406C438.095 354.118 417.333 339.303 409.737 315.321C402.14 291.339 410.56 267.261 428.568 261.549C446.576 255.837 467.338 270.652 474.943 294.643Z" fill="#C5D5FF"/>
<path d="M455.752 347.33C473.31 341.762 481.535 318.29 474.123 294.905C466.711 271.519 446.469 257.074 428.91 262.642C411.352 268.21 403.127 291.682 410.539 315.067C417.951 338.453 438.193 352.898 455.752 347.33Z" fill="#C6D6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M473.314 295.156C480.53 317.942 472.517 340.808 455.412 346.237C438.307 351.665 418.581 337.585 411.365 314.799C404.149 292.013 412.153 269.147 429.258 263.718C446.363 258.29 466.089 272.37 473.314 295.156Z" fill="#C6D7FF"/>
<path d="M455.064 345.151C471.718 339.87 479.518 317.602 472.486 295.415C465.453 273.227 446.252 259.522 429.598 264.803C412.944 270.084 405.144 292.352 412.176 314.539C419.208 336.727 438.41 350.432 455.064 345.151Z" fill="#C7D8FF"/>
<path d="M454.719 344.071C470.925 338.932 478.517 317.267 471.675 295.681C464.834 274.094 446.149 260.762 429.943 265.901C413.736 271.04 406.144 292.705 412.986 314.291C419.828 335.877 438.512 349.21 454.719 344.071Z" fill="#C7D9FF"/>
<path d="M451.751 342.84C467.452 337.88 474.826 316.902 468.222 295.984C461.618 275.066 443.537 262.129 427.836 267.089C412.135 272.048 404.76 293.026 411.364 313.944C417.968 334.862 436.05 347.799 451.751 342.84Z" fill="#C8DBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M470.047 296.175C476.51 316.543 469.339 337.018 454.04 341.88C438.741 346.741 421.087 334.14 414.623 313.745C408.16 293.35 415.332 272.902 430.64 268.049C445.947 263.196 463.584 275.806 470.047 296.175Z" fill="#C9DCFF"/>
<path d="M453.686 340.813C468.541 336.102 475.499 316.243 469.228 296.457C462.957 276.67 445.831 264.449 430.976 269.159C416.121 273.87 409.163 293.729 415.434 313.515C421.705 333.302 438.831 345.523 453.686 340.813Z" fill="#C9DDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M468.418 296.706C474.5 315.897 467.754 335.158 453.367 339.719C438.98 344.28 422.379 332.439 416.27 313.249C410.161 294.058 416.934 274.806 431.321 270.236C445.708 265.666 462.335 277.516 468.418 296.706Z" fill="#CADEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M467.603 296.963C473.5 315.56 466.957 334.21 453.004 338.638C439.05 343.066 422.964 331.554 417.067 312.957C411.171 294.359 417.713 275.709 431.667 271.281C445.62 266.853 461.76 278.375 467.603 296.963Z" fill="#CADFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M466.789 297.22C472.491 315.215 466.169 333.272 452.623 337.549C439.077 341.826 423.548 330.721 417.846 312.753C412.144 294.784 418.475 276.71 432.012 272.423C445.549 268.137 461.087 279.234 466.789 297.22Z" fill="#CBE0FF"/>
<path d="M452.311 336.465C465.366 332.325 471.482 314.871 465.97 297.481C460.458 280.091 445.407 269.349 432.351 273.489C419.295 277.629 413.18 295.083 418.691 312.473C424.203 329.863 439.255 340.605 452.311 336.465Z" fill="#CCE1FF"/>
<path d="M451.966 335.385C464.569 331.388 470.473 314.538 465.152 297.749C459.831 280.961 445.3 270.591 432.696 274.587C420.093 278.584 414.189 295.434 419.51 312.223C424.831 329.011 439.362 339.381 451.966 335.385Z" fill="#CCE2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M464.346 297.999C469.472 314.187 463.779 330.438 451.632 334.308C439.485 338.178 425.46 328.144 420.325 311.956C415.19 295.767 420.892 279.517 433.048 275.647C445.204 271.777 459.21 281.811 464.346 297.999Z" fill="#CDE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M463.531 298.256C468.471 313.851 462.991 329.499 451.286 333.21C439.582 336.92 426.089 327.285 421.184 311.699C416.279 296.113 421.733 280.465 433.437 276.745C445.142 273.026 458.59 282.67 463.531 298.256Z" fill="#CDE4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M462.716 298.513C467.462 313.506 462.194 328.552 450.941 332.12C439.688 335.689 426.709 326.435 421.963 311.442C417.218 296.449 422.477 281.403 433.73 277.834C444.983 274.265 457.962 283.529 462.716 298.513Z" fill="#CEE5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M461.902 298.778C466.461 313.169 461.397 327.613 450.596 331.04C439.794 334.467 427.337 325.576 422.804 311.23C418.271 296.883 423.3 282.395 434.102 278.968C444.903 275.541 457.333 284.388 461.902 298.778Z" fill="#CEE6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M461.087 299.035C465.452 312.824 460.609 326.665 450.25 329.951C439.892 333.236 427.957 324.717 423.592 310.92C419.227 297.122 424.07 283.29 434.42 280.004C444.77 276.719 456.714 285.238 461.087 299.035Z" fill="#CFE7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M460.264 299.292C464.452 312.487 459.812 325.727 449.905 328.862C439.998 331.996 428.586 323.858 424.407 310.663C420.228 297.468 424.858 284.237 434.765 281.093C444.673 277.95 456.085 286.097 460.264 299.292Z" fill="#D0E8FF"/>
<path d="M449.558 327.787C459.01 324.79 463.438 312.151 459.447 299.559C455.455 286.966 444.557 279.187 435.104 282.185C425.652 285.182 421.224 297.821 425.216 310.413C429.207 323.006 440.105 330.785 449.558 327.787Z" fill="#D0E9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M458.635 299.806C462.442 311.805 458.219 323.84 449.215 326.692C440.21 329.543 429.834 322.14 426.036 310.149C422.238 298.158 426.452 286.115 435.456 283.263C444.46 280.411 454.837 287.815 458.635 299.806Z" fill="#D1EAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M457.82 300.071C461.433 311.46 457.431 322.893 448.878 325.603C440.326 328.312 430.463 321.281 426.851 309.892C423.238 298.504 427.249 287.062 435.802 284.352C444.354 281.642 454.208 288.674 457.82 300.071Z" fill="#D1EBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M457.006 300.328C460.432 311.123 456.634 321.954 448.533 324.522C440.432 327.09 431.082 320.422 427.665 309.627C424.247 298.832 428.037 288.001 436.138 285.433C444.239 282.865 453.588 289.533 457.006 300.328Z" fill="#D2ECFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M456.191 300.603C459.423 310.796 455.837 321.024 448.223 323.451C440.609 325.877 431.746 319.581 428.515 309.388C425.283 299.195 428.869 288.966 436.483 286.54C444.097 284.113 452.96 290.392 456.191 300.603Z" fill="#D3EDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M455.377 300.842C458.422 310.433 455.049 320.068 447.842 322.353C440.636 324.637 432.34 318.704 429.294 309.113C426.249 299.522 429.631 289.887 436.829 287.602C444.027 285.318 452.34 291.251 455.377 300.842Z" fill="#D3EEFF"/>
<path d="M447.492 321.279C454.246 319.137 457.411 310.11 454.56 301.117C451.71 292.123 443.924 286.569 437.17 288.711C430.416 290.852 427.252 299.879 430.102 308.873C432.953 317.866 440.738 323.421 447.492 321.279Z" fill="#D4EFFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M453.792 301.355C456.448 309.751 453.5 318.181 447.196 320.174C440.892 322.167 433.632 316.995 430.967 308.599C428.302 300.204 431.26 291.773 437.563 289.781C443.867 287.788 451.083 292.969 453.792 301.355Z" fill="#D4F0FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M452.906 301.621C455.377 309.414 452.632 317.234 446.78 319.094C440.928 320.953 434.181 316.136 431.729 308.343C429.276 300.55 432.012 292.721 437.855 290.861C443.699 289.002 450.463 293.819 452.906 301.621Z" fill="#D5F1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M452.118 301.878C454.394 309.069 451.871 316.295 446.47 318.004C441.069 319.713 434.836 315.277 432.552 308.077C430.268 300.877 432.809 293.66 438.21 291.951C443.61 290.241 449.834 294.678 452.118 301.878Z" fill="#D6F2FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M451.304 302.135C453.394 308.732 451.074 315.348 446.125 316.924C441.176 318.5 435.501 314.418 433.367 307.82C431.233 301.223 433.597 294.607 438.555 293.031C443.513 291.455 449.215 295.537 451.304 302.135Z" fill="#D6F3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M450.489 302.374C452.384 308.369 450.277 314.391 445.779 315.817C441.282 317.243 436.04 313.541 434.181 307.546C432.322 301.55 434.393 295.528 438.891 294.103C443.389 292.677 448.586 296.396 450.489 302.374Z" fill="#D7F4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M449.675 302.648C451.384 308.05 449.489 313.461 445.434 314.754C441.379 316.047 436.713 312.7 435.005 307.307C433.296 301.913 435.19 296.494 439.237 295.209C443.283 293.925 447.957 297.255 449.675 302.648Z" fill="#D7F5FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M448.861 302.914C450.374 307.705 448.692 312.523 445.089 313.665C441.485 314.807 437.333 311.841 435.819 307.05C434.305 302.259 435.979 297.432 439.582 296.29C443.185 295.148 447.338 298.114 448.861 302.914Z" fill="#D8F6FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M448.046 303.171C449.374 307.369 447.896 311.575 444.744 312.576C441.592 313.576 437.962 310.982 436.634 306.784C435.306 302.586 436.776 298.38 439.927 297.379C443.079 296.378 446.709 298.973 448.046 303.171Z" fill="#D8F7FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M447.223 303.428C448.365 307.023 447.099 310.636 444.398 311.495C441.698 312.354 438.582 310.123 437.448 306.527C436.315 302.932 437.572 299.319 440.273 298.46C442.973 297.601 446.09 299.832 447.223 303.428Z" fill="#D9F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M446.408 303.685C447.364 306.687 446.31 309.698 444.053 310.406C441.795 311.115 439.21 309.273 438.262 306.27C437.315 303.268 438.369 300.266 440.618 299.549C442.866 298.832 445.46 300.691 446.408 303.685Z" fill="#DAF9FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M445.594 303.941C446.355 306.341 445.514 308.75 443.717 309.326C441.919 309.901 439.839 308.44 439.077 306.014C438.316 303.587 439.157 301.205 440.963 300.638C442.769 300.071 444.832 301.55 445.594 303.941Z" fill="#DAFAFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M444.779 304.207C445.355 305.978 444.717 307.811 443.371 308.236C442.026 308.661 440.459 307.554 439.892 305.757C439.325 303.959 439.954 302.152 441.3 301.719C442.645 301.285 444.212 302.374 444.779 304.207Z" fill="#DBFBFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M443.964 304.464C444.345 305.659 443.964 306.864 443.026 307.121C442.087 307.377 441.087 306.669 440.706 305.465C440.325 304.26 440.706 303.065 441.645 302.808C442.583 302.551 443.584 303.259 443.964 304.464Z" fill="#DBFCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M443.168 304.721C443.241 304.835 443.288 304.965 443.303 305.1C443.318 305.236 443.302 305.373 443.256 305.501C443.21 305.629 443.134 305.745 443.036 305.839C442.938 305.933 442.819 306.004 442.689 306.045C442.559 306.086 442.422 306.096 442.287 306.075C442.153 306.054 442.025 306.003 441.913 305.924C441.802 305.846 441.71 305.743 441.645 305.623C441.58 305.503 441.544 305.37 441.538 305.234C441.465 305.12 441.418 304.99 441.403 304.854C441.388 304.719 441.404 304.582 441.45 304.454C441.496 304.326 441.572 304.21 441.67 304.116C441.768 304.021 441.887 303.951 442.017 303.91C442.147 303.869 442.284 303.859 442.419 303.88C442.553 303.901 442.681 303.952 442.793 304.031C442.904 304.109 442.996 304.212 443.061 304.332C443.126 304.451 443.162 304.585 443.168 304.721Z" fill="#DCFDFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M511.65 355.178C505.54 365.902 488.338 365.162 473.237 353.531C458.136 341.899 450.873 323.812 456.96 313.06L466.725 327.1L481.338 317.784L472.839 305.565C479.794 305.861 487.864 308.955 495.428 314.777C510.491 326.314 517.755 344.458 511.65 355.178Z" fill="#F2960D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M511.101 354.754C505.117 365.266 488.265 364.542 473.46 353.139C458.656 341.736 451.519 323.988 457.504 313.477C463.488 302.965 480.345 303.696 495.145 315.092C509.944 326.488 517.091 344.25 511.101 354.754Z" fill="#F2970D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M510.552 354.33C504.687 364.623 488.181 363.92 473.684 352.759C459.186 341.598 452.182 324.197 458.053 313.9C463.923 303.603 480.424 304.311 494.927 315.479C509.429 326.646 516.417 344.038 510.552 354.33Z" fill="#F2980D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M510.008 353.914C504.263 363.988 488.102 363.293 473.907 352.367C459.711 341.442 452.851 324.401 458.601 314.323C464.352 304.246 480.475 304.903 494.703 315.87C508.931 326.837 515.748 343.834 510.008 353.914Z" fill="#F2990D"/>
<path d="M474.12 351.976C488.016 362.677 503.838 363.355 509.46 353.491C515.081 343.626 508.373 326.955 494.477 316.254C480.581 305.553 464.759 304.875 459.138 314.739C453.517 324.604 460.224 341.276 474.12 351.976Z" fill="#F29A0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M508.911 353.067C503.416 362.716 487.939 362.055 474.348 351.589C460.756 341.122 454.194 324.817 459.705 315.155C465.216 305.493 480.676 306.167 494.267 316.633C507.859 327.1 514.416 343.42 508.911 353.067Z" fill="#F29B0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M508.367 352.65C502.987 362.085 487.855 361.433 474.56 351.195C461.265 340.957 454.857 325.014 460.237 315.58C465.617 306.146 480.744 306.79 494.044 317.036C507.345 327.281 513.742 343.209 508.367 352.65Z" fill="#F29C0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M507.818 352.227C502.563 361.449 487.776 360.818 474.789 350.81C461.801 340.803 455.531 325.226 460.786 316.004C466.041 306.781 480.833 307.42 493.815 317.42C506.798 327.421 513.057 343.018 507.818 352.227Z" fill="#F29D0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M507.275 351.811C502.14 360.814 487.693 360.196 475.007 350.423C462.321 340.651 456.2 325.43 461.335 316.427C466.47 307.424 480.907 308.04 493.598 317.808C506.289 327.575 512.405 342.801 507.275 351.811Z" fill="#F29E0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M506.726 351.387C501.716 360.178 487.619 359.576 475.236 350.039C462.852 340.502 456.869 325.646 461.884 316.851C466.899 308.056 480.991 308.662 493.375 318.199C505.758 327.736 511.736 342.596 506.726 351.387Z" fill="#F29F0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M506.177 350.963C501.292 359.542 487.535 358.954 475.453 349.652C463.371 340.35 457.537 325.851 462.422 317.272C467.306 308.693 481.069 309.277 493.156 318.586C505.244 327.895 511.072 342.387 506.177 350.963Z" fill="#F2A00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M505.634 350.547C500.869 358.907 487.451 358.331 475.677 349.26C463.902 340.189 458.19 326.068 462.977 317.691C467.763 309.314 481.154 309.9 492.934 318.978C504.714 328.056 510.41 342.19 505.634 350.547Z" fill="#F2A10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M505.085 350.123C500.467 358.299 487.373 357.716 475.895 348.873C464.417 340.03 458.848 326.236 463.52 318.108C468.193 309.979 481.232 310.526 492.71 319.358C504.189 328.189 509.73 341.972 505.085 350.123Z" fill="#F2A20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M504.536 349.7C500.022 357.635 487.283 357.098 476.123 348.489C464.963 339.879 459.565 326.469 464.052 318.521C468.539 310.574 481.305 311.123 492.47 319.728C503.636 328.333 509.061 341.767 504.536 349.7Z" fill="#F2A30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M503.992 349.283C499.592 357.004 487.21 356.467 476.336 348.094C465.461 339.722 460.24 326.658 464.623 318.973C469.007 311.288 481.406 311.778 492.28 320.15C503.154 328.522 508.349 341.563 503.992 349.283Z" fill="#F2A40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M503.444 348.837C499.169 356.346 487.131 355.829 476.559 347.681C465.986 339.532 460.881 326.861 465.156 319.353C469.43 311.845 481.479 312.364 492.046 320.505C502.613 328.647 507.718 341.351 503.444 348.837Z" fill="#F2A50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M502.895 348.436C498.745 355.732 487.047 355.229 476.782 347.323C466.517 339.417 461.55 327.088 465.705 319.799C469.86 312.51 481.552 313.006 491.828 320.915C502.104 328.824 507.05 341.147 502.895 348.436Z" fill="#F2A60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M502.351 348.02C498.316 355.09 486.947 354.575 477.006 346.932C467.065 339.288 462.219 327.293 466.254 320.223C470.289 313.152 481.637 313.628 491.605 321.306C501.574 328.984 506.387 340.938 502.351 348.02Z" fill="#F2A70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M501.802 347.596C497.892 354.454 486.89 353.987 477.223 346.544C467.556 339.102 462.892 327.504 466.797 320.639C470.701 313.774 481.72 314.25 491.387 321.693C501.053 329.136 505.717 340.734 501.802 347.596Z" fill="#F2A80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M501.248 347.188C497.463 353.834 486.8 353.38 477.441 346.168C468.082 338.956 463.556 327.724 467.34 321.078C471.125 314.432 481.793 314.881 491.153 322.093C500.512 329.305 505.043 340.522 501.248 347.188Z" fill="#F2A90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M500.71 346.756C497.045 353.182 486.721 352.743 477.708 345.776C468.694 338.81 464.274 327.923 467.939 321.496C471.603 315.07 481.921 315.503 490.968 322.454C500.014 329.406 504.374 340.318 500.71 346.756Z" fill="#F2AA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M500.161 346.332C496.61 352.533 486.648 352.123 477.887 345.374C469.126 338.626 464.893 328.121 468.438 321.902C471.983 315.684 481.984 316.142 490.717 322.856C499.45 329.57 503.706 340.113 500.161 346.332Z" fill="#F2AB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M499.612 345.909C496.192 351.915 486.564 351.501 478.105 344.987C469.646 338.474 465.567 328.333 468.987 322.326C472.407 316.319 482.04 316.73 490.499 323.243C498.958 329.757 503.037 339.909 499.612 345.909Z" fill="#F2AC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M499.085 345.513C495.785 351.301 486.48 350.923 478.345 344.617C470.21 338.31 466.253 328.558 469.553 322.77C472.853 316.983 482.13 317.325 490.292 323.656C498.455 329.986 502.374 339.7 499.085 345.513Z" fill="#F2AD0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M498.52 345.069C495.345 350.644 486.402 350.263 478.552 344.216C470.702 338.168 466.905 328.742 470.08 323.166C473.254 317.591 482.203 317.979 490.058 324.022C497.913 330.065 501.7 339.489 498.52 345.069Z" fill="#F2AF0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M497.971 344.645C494.921 350.008 486.323 349.636 478.775 343.824C471.227 338.012 467.59 328.967 470.628 323.59C473.667 318.213 482.282 318.594 489.835 324.413C497.388 330.232 501.031 339.284 497.971 344.645Z" fill="#F2B00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M497.427 344.229C494.498 349.373 486.245 349.021 479.01 343.424C471.775 337.827 468.264 329.145 471.194 324C474.124 318.856 482.382 319.204 489.612 324.805C496.841 330.406 500.363 339.08 497.427 344.229Z" fill="#F2B10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M496.883 343.801C494.074 348.737 486.16 348.399 479.216 343.046C472.272 337.693 468.916 329.362 471.721 324.43C474.525 319.498 482.444 319.832 489.388 325.185C496.332 330.538 499.693 338.876 496.883 343.801Z" fill="#F2B20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M496.341 343.384C493.651 348.102 486.076 347.777 479.434 342.659C472.792 337.541 469.58 329.571 472.27 324.854C474.96 320.136 482.523 320.459 489.171 325.572C495.818 330.686 499.02 338.665 496.341 343.384Z" fill="#F2B30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M495.792 342.961C493.227 347.466 486.003 347.157 479.657 342.267C473.312 337.377 470.249 329.775 472.819 325.277C475.389 320.779 482.607 321.081 488.948 325.964C495.288 330.846 498.356 338.456 495.792 342.961Z" fill="#F2B40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M495.242 342.537C492.797 346.834 485.919 346.534 479.875 341.88C473.831 337.226 470.922 329.986 473.362 325.693C475.802 321.4 482.658 321.718 488.729 326.351C494.8 330.983 497.709 338.234 495.242 342.537Z" fill="#F2B50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M494.699 342.121C492.385 346.213 485.835 345.912 480.099 341.5C474.363 337.088 471.581 330.177 473.89 326.089C476.198 322.002 482.759 322.305 488.529 326.77C494.298 331.235 497.019 338.047 494.699 342.121Z" fill="#F2B60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M494.15 341.697C491.945 345.556 485.756 345.297 480.338 341.096C474.92 336.894 472.277 330.382 474.482 326.535C476.688 322.687 482.871 322.939 488.294 327.136C493.718 331.333 496.345 337.836 494.15 341.697Z" fill="#F2B70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M493.601 341.274C491.521 344.92 485.677 344.67 480.539 340.721C475.401 336.772 472.929 330.6 474.981 326.974C477.034 323.349 482.905 323.578 488.043 327.538C493.181 331.498 495.676 337.631 493.601 341.274Z" fill="#F2B80D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M493.057 340.857C491.097 344.285 485.593 344.048 480.763 340.33C475.932 336.612 473.592 330.808 475.552 327.381C477.513 323.953 483.011 324.183 487.842 327.901C492.672 331.619 495.018 337.441 493.057 340.857Z" fill="#F2B90D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M492.509 340.433C490.674 343.649 485.515 343.432 480.981 339.943C476.447 336.453 474.266 331.02 476.096 327.797C477.926 324.575 483.096 324.805 487.619 328.292C492.142 331.779 494.339 337.222 492.509 340.433Z" fill="#F2BA0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M491.96 340.01C490.25 343.013 485.431 342.81 481.177 339.517C476.922 336.223 474.908 331.19 476.634 328.173C478.36 325.157 483.158 325.377 487.39 328.632C491.622 331.886 493.67 337.007 491.96 340.01Z" fill="#F2BB0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M491.416 339.593C489.826 342.377 485.33 342.156 481.421 339.164C477.513 336.172 475.603 331.428 477.193 328.644C478.784 325.86 483.252 326.047 487.177 329.071C491.102 332.094 493.001 336.78 491.416 339.593Z" fill="#F2BC0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M490.868 339.17C489.37 341.768 485.274 341.568 481.662 338.771C478.05 335.974 476.284 331.624 477.748 329.052C479.213 326.48 483.348 326.661 486.96 329.458C490.572 332.255 492.333 336.598 490.868 339.17Z" fill="#F2BD0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M490.319 338.746C488.974 341.11 485.189 340.946 481.868 338.392C478.547 335.839 476.947 331.844 478.286 329.484C479.626 327.124 483.415 327.285 486.736 329.849C490.058 332.414 491.664 336.393 490.319 338.746Z" fill="#F2BE0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M489.781 338.325C488.534 340.443 485.133 340.313 482.097 337.997C479.062 335.68 477.615 332.049 478.841 329.904C480.066 327.758 483.489 327.916 486.524 330.232C489.56 332.549 490.995 336.189 489.781 338.325Z" fill="#F2BF0D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M489.226 337.906C488.105 339.856 485.032 339.704 482.309 337.614C479.587 335.524 478.279 332.257 479.379 330.324C480.479 328.391 483.573 328.527 486.29 330.621C489.007 332.715 490.321 335.978 489.226 337.906Z" fill="#F2C00D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M488.677 337.483C487.703 339.204 484.948 339.082 482.532 337.223C480.117 335.364 478.947 332.462 479.927 330.748C480.908 329.034 483.657 329.149 486.072 331.008C488.487 332.867 489.657 335.769 488.677 337.483Z" fill="#F2C10D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M488.134 337.066C487.279 338.568 484.869 338.466 482.739 336.822C480.609 335.177 479.61 332.659 480.465 331.158C481.32 329.656 483.702 329.723 485.854 331.406C488.006 333.09 488.989 335.564 488.134 337.066Z" fill="#F2C20D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M487.568 336.656C486.822 337.958 484.769 337.857 482.963 336.453C481.157 335.048 480.301 332.914 481.009 331.597C481.716 330.279 483.803 330.4 485.615 331.8C487.426 333.199 488.32 335.36 487.568 336.656Z" fill="#F2C30D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M487.036 336.219C486.432 337.297 484.706 337.217 483.197 336.064C481.687 334.911 480.959 333.082 481.569 332.012C482.179 330.941 483.898 331.014 485.408 332.178C486.917 333.343 487.646 335.149 487.036 336.219Z" fill="#F2C40D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M486.493 335.802C485.997 336.658 484.628 336.602 483.42 335.672C482.213 334.743 481.622 333.291 482.118 332.435C482.613 331.579 483.982 331.636 485.19 332.565C486.398 333.495 486.977 334.944 486.493 335.802Z" fill="#F2C50D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M485.943 335.379C485.852 335.752 485.642 336.056 485.345 336.251C485.047 336.446 484.676 336.521 484.282 336.466C483.889 336.41 483.494 336.227 483.151 335.943C482.809 335.658 482.536 335.285 482.371 334.876C482.206 334.468 482.157 334.043 482.232 333.663C482.306 333.282 482.499 332.964 482.785 332.753C483.071 332.541 483.434 332.448 483.825 332.484C484.216 332.521 484.615 332.686 484.967 332.957C485.373 333.219 485.702 333.618 485.884 334.069C486.066 334.521 486.087 334.99 485.943 335.379Z" fill="#F2C60D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M485.4 334.963C485.339 335.212 485.198 335.415 484.999 335.545C484.8 335.675 484.551 335.725 484.289 335.688C484.026 335.651 483.761 335.529 483.532 335.338C483.303 335.148 483.12 334.899 483.01 334.625C482.899 334.352 482.867 334.068 482.916 333.814C482.965 333.559 483.094 333.346 483.285 333.204C483.476 333.062 483.719 332.999 483.98 333.023C484.242 333.047 484.508 333.156 484.744 333.337C485.018 333.511 485.241 333.779 485.363 334.083C485.486 334.386 485.499 334.702 485.4 334.963Z" fill="#F2C70D"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M484.851 334.539C484.766 334.64 484.638 334.693 484.495 334.687C484.351 334.682 484.202 334.617 484.079 334.507C484.009 334.427 483.959 334.333 483.932 334.236C483.905 334.138 483.904 334.041 483.928 333.955C483.953 333.869 484.002 333.798 484.069 333.749C484.137 333.701 484.221 333.677 484.312 333.681C484.403 333.685 484.497 333.716 484.584 333.771C484.67 333.826 484.746 333.902 484.802 333.991C484.858 334.08 484.892 334.179 484.901 334.276C484.91 334.373 484.892 334.464 484.851 334.539Z" fill="#F2C80D"/>
<path style="mix-blend-mode:multiply" opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M510.934 355.469C510.678 356.494 510.046 357.385 509.163 357.966C503.992 362.819 492.766 359.817 484.32 356.478C482.832 355.902 481.451 355.309 480.229 354.787C479.788 354.623 479.359 354.428 478.945 354.202L478.193 353.848C477.37 353.485 476.528 353.069 475.687 352.644C475.672 352.636 475.655 352.633 475.639 352.633C475.622 352.633 475.605 352.636 475.59 352.644C475.342 352.493 475.059 352.378 474.811 352.227C474.156 351.882 473.819 351.687 473.819 351.687C468.428 348.743 463.251 345.422 458.325 341.751C455.191 339.422 451.96 336.818 448.702 334.029C450.844 335.72 463.885 345.019 476.006 350.731C488.782 356.744 500.584 358.754 497.556 343.646C495.7 334.439 492.436 325.574 487.879 317.362C468.905 282.532 432.526 265.113 430.835 263.908C442.398 267.681 458.281 277.75 475.041 292.902C478.157 295.718 481.115 298.65 483.895 301.758C483.895 301.758 483.957 301.758 483.948 301.829C484.435 302.325 484.913 302.812 485.355 303.334C486.02 304.043 486.639 304.769 487.232 305.504C493.747 313.116 499.39 321.433 504.054 330.3C509.641 339.891 513.448 349.615 510.934 355.469Z" fill="url(#paint149_linear_292_86830)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M428.498 293.593C428.498 293.593 430.889 309.604 446.825 315.733C446.825 315.759 441.84 299.571 428.498 293.593Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M421.566 380.07C421.566 380.07 415.714 391.113 422.566 394.833C422.566 394.833 426.267 381.806 421.566 380.07Z" fill="white"/>
<path d="M385.479 303.715C385.479 303.715 379.379 315.449 383.867 323.942C383.867 323.924 391.428 316.193 385.479 303.715Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M235.711 470.275L239.076 370.709L151.814 369.576L241.183 369.106L235.711 470.275Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M231.116 415.015L231.567 374.181L149.512 372.321L229.735 375.066L231.116 415.015Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_292_86830" x1="525.241" y1="545.12" x2="107.829" y2="545.12" gradientUnits="userSpaceOnUse">
<stop stop-color="#603082"/>
<stop offset="0.99" stop-color="#2A0E51"/>
</linearGradient>
<linearGradient id="paint1_linear_292_86830" x1="-594838" y1="21194.4" x2="-586908" y2="-27859.9" gradientUnits="userSpaceOnUse">
<stop stop-color="#603082"/>
<stop offset="0.99" stop-color="#2A0E51"/>
</linearGradient>
<linearGradient id="paint2_linear_292_86830" x1="378.077" y1="435.105" x2="378.077" y2="214.684" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#2A0E45"/>
<stop offset="1" stop-color="#561F7D"/>
</linearGradient>
<radialGradient id="paint3_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(376.855 317.987) rotate(180) scale(146.058 146.094)">
<stop stop-color="#04FFFF"/>
<stop offset="0.99" stop-color="#26BBFF"/>
</radialGradient>
<radialGradient id="paint4_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(321.874 423.563) rotate(180) scale(107.987 143.659)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<linearGradient id="paint5_linear_292_86830" x1="184819" y1="23476.6" x2="184819" y2="21845.3" gradientUnits="userSpaceOnUse">
<stop stop-color="#603082"/>
<stop offset="0.99" stop-color="#2A0E51"/>
</linearGradient>
<radialGradient id="paint6_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(382.291 476.736) rotate(180) scale(41.1869 41.197)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<radialGradient id="paint7_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-335017 2806.79) rotate(180) scale(5402.52 179.8)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<linearGradient id="paint8_linear_292_86830" x1="202.837" y1="244.439" x2="154.337" y2="244.439" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint9_linear_292_86830" x1="157.693" y1="278.18" x2="202.165" y2="283.977" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6E27"/>
<stop offset="1" stop-color="#E03347"/>
</linearGradient>
<linearGradient id="paint10_linear_292_86830" x1="-98968.8" y1="18323.2" x2="-96123" y2="18323.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint11_linear_292_86830" x1="-130696" y1="9584.28" x2="-131777" y2="7241.95" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint12_linear_292_86830" x1="-137970" y1="-17066.9" x2="-136076" y2="-14946.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint13_linear_292_86830" x1="644.163" y1="501.417" x2="113.947" y2="501.417" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF8647"/>
<stop offset="1" stop-color="#FFB934"/>
</linearGradient>
<radialGradient id="paint14_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(211.552 490.019) rotate(180) scale(6.26106 16.4807)">
<stop stop-color="#FFEF00"/>
<stop offset="0.01" stop-color="#FCEC00"/>
<stop offset="0.23" stop-color="#B1A500"/>
<stop offset="0.43" stop-color="#726B00"/>
<stop offset="0.62" stop-color="#413D00"/>
<stop offset="0.78" stop-color="#1D1C00"/>
<stop offset="0.91" stop-color="#080700"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint15_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1.57352e+06 -52631.9) scale(88325.6 6443.52)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<linearGradient id="paint16_linear_292_86830" x1="-1.12115e+06" y1="21255.6" x2="-1.31073e+06" y2="21255.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint17_linear_292_86830" x1="390.499" y1="512.451" x2="391.925" y2="485.237" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#BD9F65"/>
</linearGradient>
<linearGradient id="paint18_linear_292_86830" x1="644.163" y1="515.692" x2="113.947" y2="515.692" gradientUnits="userSpaceOnUse">
<stop stop-color="#C93347"/>
<stop offset="1" stop-color="#FF7527"/>
</linearGradient>
<linearGradient id="paint19_linear_292_86830" x1="-1.15982e+06" y1="105170" x2="-1.37328e+06" y2="120109" gradientUnits="userSpaceOnUse">
<stop stop-color="#603082"/>
<stop offset="0.99" stop-color="#2A0E51"/>
</linearGradient>
<radialGradient id="paint20_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(463.72 528.739) rotate(175.995) scale(198.46 198.509)">
<stop stop-color="#6D47D3"/>
<stop offset="0.99" stop-color="#713592"/>
</radialGradient>
<linearGradient id="paint21_linear_292_86830" x1="-1.08842e+06" y1="110490" x2="-1.27362e+06" y2="123451" gradientUnits="userSpaceOnUse">
<stop stop-color="#603082"/>
<stop offset="0.99" stop-color="#2A0E51"/>
</linearGradient>
<radialGradient id="paint22_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1.18396e+06 28896.6) scale(65560.4 9287.8)">
<stop stop-color="#6D47D3"/>
<stop offset="0.99" stop-color="#713592"/>
</radialGradient>
<linearGradient id="paint23_linear_292_86830" x1="504.2" y1="538.898" x2="163.534" y2="563.174" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint24_linear_292_86830" x1="960596" y1="14706.9" x2="1.09486e+06" y2="16790.7" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint25_linear_292_86830" x1="908388" y1="13643.4" x2="1.03535e+06" y2="15635.6" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint26_linear_292_86830" x1="854160" y1="12002.1" x2="973538" y2="13988.7" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint27_linear_292_86830" x1="813794" y1="11624.7" x2="927528" y2="13472.7" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint28_linear_292_86830" x1="27146.9" y1="2856.17" x2="30874.2" y2="2865.71" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint29_linear_292_86830" x1="314.754" y1="4803.2" x2="-17.3271" y2="4827.7" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint30_linear_292_86830" x1="41472" y1="3641.37" x2="47199.4" y2="3659.08" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint31_linear_292_86830" x1="104.844" y1="1935.53" x2="-229.77" y2="1960.42" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint32_linear_292_86830" x1="32810.7" y1="3876.06" x2="37331.8" y2="3886.16" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint33_linear_292_86830" x1="25338.7" y1="3073.91" x2="28817.2" y2="3081.42" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint34_linear_292_86830" x1="-19877.7" y1="2685.54" x2="-23032.8" y2="2960.61" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint35_linear_292_86830" x1="-10621.8" y1="1962.79" x2="-12482.6" y2="2114.44" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint36_linear_292_86830" x1="29678.7" y1="5345.66" x2="33804.5" y2="5351.54" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint37_linear_292_86830" x1="-22084.3" y1="2776.37" x2="-25543.7" y2="3073.53" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint38_linear_292_86830" x1="21935.2" y1="4928.85" x2="24974.3" y2="4932.35" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint39_linear_292_86830" x1="-5303.54" y1="1527.16" x2="-6418.29" y2="1610.47" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint40_linear_292_86830" x1="11019.1" y1="3499.32" x2="12528.3" y2="3500.59" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint41_linear_292_86830" x1="7687.9" y1="2337.28" x2="8730.41" y2="2338.25" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint42_linear_292_86830" x1="16369.9" y1="4896.31" x2="18628.7" y2="4898.3" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint43_linear_292_86830" x1="4705.41" y1="1662.86" x2="5328.83" y2="1663.41" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint44_linear_292_86830" x1="-19972.5" y1="2741.9" x2="-23134.3" y2="3021.45" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint45_linear_292_86830" x1="9525.99" y1="3915.28" x2="10824" y2="3916.09" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint46_linear_292_86830" x1="-12326" y1="2085.8" x2="-14417.5" y2="2254.48" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint47_linear_292_86830" x1="5425.75" y1="2250.11" x2="6147.98" y2="2250.6" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint48_linear_292_86830" x1="10728" y1="4702.05" x2="12193" y2="4702.92" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint49_linear_292_86830" x1="5999.72" y1="3580.24" x2="6800.36" y2="3580.58" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint50_linear_292_86830" x1="727.562" y1="1114.58" x2="462.609" y2="1134.95" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint51_linear_292_86830" x1="-17991.3" y1="2527.85" x2="-20872.2" y2="2772.89" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint52_linear_292_86830" x1="-6464.28" y1="1623.44" x2="-7732.74" y2="1719.4" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint53_linear_292_86830" x1="-18227.7" y1="2502.09" x2="-21139.8" y2="2740.85" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint54_linear_292_86830" x1="-11058.2" y1="1990.14" x2="-12968.3" y2="2143.84" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint55_linear_292_86830" x1="343.188" y1="4879.08" x2="9.50853" y2="4903.62" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint56_linear_292_86830" x1="449.265" y1="6754.86" x2="115.572" y2="6779.44" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint57_linear_292_86830" x1="310.009" y1="4626.01" x2="-23.6853" y2="4650.55" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint58_linear_292_86830" x1="363.828" y1="4588.3" x2="31.2748" y2="4614.33" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint59_linear_292_86830" x1="2538.53" y1="1003.67" x2="2834.95" y2="1003.96" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint60_linear_292_86830" x1="24557.9" y1="3965.4" x2="27931" y2="3970.88" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint61_linear_292_86830" x1="242.569" y1="3150.64" x2="-91.9181" y2="3175.3" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint62_linear_292_86830" x1="379.834" y1="5481.71" x2="47.0521" y2="5504.62" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint63_linear_292_86830" x1="305.302" y1="4129.71" x2="-29.175" y2="4154.47" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint64_linear_292_86830" x1="344.566" y1="4138.5" x2="11.2083" y2="4163.94" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint65_linear_292_86830" x1="20480.7" y1="4280.85" x2="23288.2" y2="4284.33" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint66_linear_292_86830" x1="326.92" y1="3550.42" x2="-6.44543" y2="3577.44" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint67_linear_292_86830" x1="2778.42" y1="587.427" x2="3112.16" y2="593.373" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint68_linear_292_86830" x1="9269.97" y1="2411.28" x2="10513.6" y2="2412.6" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint69_linear_292_86830" x1="139.531" y1="795.65" x2="-193.904" y2="824.156" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint70_linear_292_86830" x1="409.07" y1="4425.33" x2="76.0026" y2="4452.25" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint71_linear_292_86830" x1="10644.6" y1="3331.65" x2="12082.5" y2="3332.84" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint72_linear_292_86830" x1="7561.38" y1="3036.7" x2="8569.93" y2="3037.35" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint73_linear_292_86830" x1="6218.11" y1="2391.95" x2="7039.29" y2="2392.53" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint74_linear_292_86830" x1="363.599" y1="3520.84" x2="31.7978" y2="3545.1" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint75_linear_292_86830" x1="168.146" y1="819.403" x2="-163.503" y2="845.442" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint76_linear_292_86830" x1="7645.15" y1="4133.4" x2="8667.35" y2="4133.88" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint77_linear_292_86830" x1="294.488" y1="2461.62" x2="-38.4512" y2="2486.34" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint78_linear_292_86830" x1="344.731" y1="3150.8" x2="11.7465" y2="3175.5" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint79_linear_292_86830" x1="184.924" y1="860.468" x2="-148.577" y2="885.904" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint80_linear_292_86830" x1="442.318" y1="4189.66" x2="108.835" y2="4215.21" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint81_linear_292_86830" x1="389.739" y1="3571.27" x2="56.2146" y2="3596.31" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<linearGradient id="paint82_linear_292_86830" x1="1.06429e+06" y1="19247.2" x2="1.23931e+06" y2="19247.2" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#FF4C06"/>
<stop offset="0.17" stop-color="#FF0E59"/>
<stop offset="0.54" stop-color="#FF12C3"/>
<stop offset="1" stop-color="#00B3FF"/>
</linearGradient>
<radialGradient id="paint83_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-248839 -5421.43) rotate(-112.22) scale(4218.2 3654.67)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<radialGradient id="paint84_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(701.654 447.9) rotate(-112.22) scale(25.6488 25.6443)">
<stop stop-color="#00FFFF"/>
<stop offset="0.99" stop-color="#26A9FF"/>
</radialGradient>
<radialGradient id="paint85_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(67147.4 93868.3) scale(1838.82 943.127)">
<stop stop-color="#00FFFF"/>
<stop offset="0.99" stop-color="#26A9FF"/>
</radialGradient>
<radialGradient id="paint86_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-46298.9 -8166.82) rotate(-112.22) scale(232.777 143.65)">
<stop stop-color="#6D47D3"/>
<stop offset="0.99" stop-color="#713592"/>
</radialGradient>
<radialGradient id="paint87_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(204.146 590.785) rotate(176.119) scale(46.6287 16.4806)">
<stop stop-color="#0097FF"/>
<stop offset="0.01" stop-color="#0095FC"/>
<stop offset="0.23" stop-color="#0069B1"/>
<stop offset="0.43" stop-color="#004372"/>
<stop offset="0.62" stop-color="#002641"/>
<stop offset="0.78" stop-color="#00111D"/>
<stop offset="0.91" stop-color="#000508"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint88_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(515.569 565.71) rotate(176.119) scale(39.1254 10.3259)">
<stop stop-color="#FF2A13"/>
<stop offset="0.01" stop-color="#FC2913"/>
<stop offset="0.23" stop-color="#B11D0D"/>
<stop offset="0.43" stop-color="#721308"/>
<stop offset="0.62" stop-color="#410B05"/>
<stop offset="0.78" stop-color="#1D0502"/>
<stop offset="0.91" stop-color="#080101"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint89_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(598137 -199883) scale(14600.8 10456.7)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<radialGradient id="paint90_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-236122 -141291) rotate(180) scale(2795.72 3708.47)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<linearGradient id="paint91_linear_292_86830" x1="86118.5" y1="43534" x2="86118.5" y2="37486" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#2A0E45"/>
<stop offset="1" stop-color="#561F7D"/>
</linearGradient>
<linearGradient id="paint92_linear_292_86830" x1="109.636" y1="398.256" x2="44.9512" y2="398.256" gradientUnits="userSpaceOnUse">
<stop stop-color="#8184B1"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint93_linear_292_86830" x1="446721" y1="21502.1" x2="469747" y2="21502.1" gradientUnits="userSpaceOnUse">
<stop stop-color="#8184B1"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint94_linear_292_86830" x1="-281133" y1="60667.2" x2="-281133" y2="49435.1" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6E27"/>
<stop offset="1" stop-color="#E03347"/>
</linearGradient>
<radialGradient id="paint95_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(230549 -46560.5) scale(1908.2 845.88)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<radialGradient id="paint96_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(229039 -10911) scale(1656.29 180.482)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<radialGradient id="paint97_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(398900 -300549) scale(13623.1 16572.5)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<linearGradient id="paint98_linear_292_86830" x1="125297" y1="8250.62" x2="127917" y2="8250.62" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#2A0E45"/>
<stop offset="1" stop-color="#561F7D"/>
</linearGradient>
<radialGradient id="paint99_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160512 -43342.2) scale(1344.95 541.816)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<linearGradient id="paint100_linear_292_86830" x1="310467" y1="43831.4" x2="327384" y2="43831.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#8184B1"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint101_linear_292_86830" x1="-338549" y1="34248.5" x2="-338746" y2="14420.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA700"/>
<stop offset="1" stop-color="#FFD328"/>
</linearGradient>
<linearGradient id="paint102_linear_292_86830" x1="681.631" y1="45.7502" x2="635.398" y2="45.7502" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED3D47"/>
<stop offset="1" stop-color="#FF8427"/>
</linearGradient>
<radialGradient id="paint103_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(87556.9 -108317) scale(909.357 1721.25)">
<stop stop-color="#EBD7E3"/>
<stop offset="0.98" stop-color="#BDAFD2"/>
</radialGradient>
<radialGradient id="paint104_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-177090 -157069) rotate(180) scale(2488.1 3339.32)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<linearGradient id="paint105_linear_292_86830" x1="137194" y1="9707.99" x2="140391" y2="9707.99" gradientUnits="userSpaceOnUse">
<stop stop-color="#8184B1"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint106_linear_292_86830" x1="52216.3" y1="4408.71" x2="52216.3" y2="4118.91" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#2A0E45"/>
<stop offset="1" stop-color="#561F7D"/>
</linearGradient>
<linearGradient id="paint107_linear_292_86830" x1="559.645" y1="243.934" x2="556.771" y2="148.167" gradientUnits="userSpaceOnUse">
<stop stop-color="#B6ACE0"/>
<stop offset="0.98" stop-color="#E2DCEB"/>
</linearGradient>
<linearGradient id="paint108_linear_292_86830" x1="560.097" y1="270.228" x2="558.013" y2="200.781" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D6FF"/>
<stop offset="0.98" stop-color="#E2DCEB"/>
</linearGradient>
<linearGradient id="paint109_linear_292_86830" x1="557.423" y1="270.308" x2="556.319" y2="233.504" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#E2DCEB"/>
<stop offset="1" stop-color="#E5F6FF"/>
</linearGradient>
<linearGradient id="paint110_linear_292_86830" x1="315.411" y1="152.356" x2="217.844" y2="152.356" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB152C"/>
<stop offset="1" stop-color="#C73E27"/>
</linearGradient>
<linearGradient id="paint111_linear_292_86830" x1="276.057" y1="177.471" x2="276.057" y2="124.044" gradientUnits="userSpaceOnUse">
<stop stop-color="#E03347"/>
<stop offset="1" stop-color="#FF6E27"/>
</linearGradient>
<radialGradient id="paint112_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-118590 -142798) rotate(-169.025) scale(1737.7 3035.97)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<linearGradient id="paint113_linear_292_86830" x1="34898.8" y1="7148.18" x2="35059.9" y2="7148.18" gradientUnits="userSpaceOnUse">
<stop stop-color="#8184B1"/>
<stop offset="1"/>
</linearGradient>
<radialGradient id="paint114_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(37435.6 -46162.3) rotate(-29.2661) scale(1898.07 984.313)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<linearGradient id="paint115_linear_292_86830" x1="15524.6" y1="-34766" x2="14932.9" y2="-35821.7" gradientUnits="userSpaceOnUse">
<stop stop-color="#E03347"/>
<stop offset="1" stop-color="#FF6E27"/>
</linearGradient>
<linearGradient id="paint116_linear_292_86830" x1="1502.26" y1="-27712.3" x2="2072.25" y2="-28031.6" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB152C"/>
<stop offset="1" stop-color="#C73E27"/>
</linearGradient>
<linearGradient id="paint117_linear_292_86830" x1="-249344" y1="42058.9" x2="-248315" y2="37902.8" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#D4CBE2"/>
<stop offset="1" stop-color="#F3E6EE"/>
</linearGradient>
<radialGradient id="paint118_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-185862 -23378.4) rotate(-166.1) scale(1488.47 366.902)">
<stop stop-color="#B3819D"/>
<stop offset="1" stop-color="#785CA6"/>
</radialGradient>
<linearGradient id="paint119_linear_292_86830" x1="-53061" y1="34412.4" x2="-52799.7" y2="33356.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#E03347"/>
<stop offset="1" stop-color="#FF6E27"/>
</linearGradient>
<linearGradient id="paint120_linear_292_86830" x1="-38011.2" y1="37957.8" x2="-37750.8" y2="36905.7" gradientUnits="userSpaceOnUse">
<stop stop-color="#E03347"/>
<stop offset="1" stop-color="#FF6E27"/>
</linearGradient>
<linearGradient id="paint121_linear_292_86830" x1="-14352.1" y1="912.788" x2="-14383.6" y2="904.989" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB152C"/>
<stop offset="1" stop-color="#C73E27"/>
</linearGradient>
<linearGradient id="paint122_linear_292_86830" x1="-14897.2" y1="270.88" x2="-14930.9" y2="262.542" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB152C"/>
<stop offset="1" stop-color="#C73E27"/>
</linearGradient>
<linearGradient id="paint123_linear_292_86830" x1="349.795" y1="74.1979" x2="348.555" y2="34.4339" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#B4B8FF"/>
<stop offset="1" stop-color="#DCFDFF"/>
</linearGradient>
<linearGradient id="paint124_linear_292_86830" x1="250.198" y1="267.423" x2="324.759" y2="273.342" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#92A2CE"/>
</linearGradient>
<radialGradient id="paint125_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(333.548 430.68) rotate(4.54112) scale(18.2916 18.296)">
<stop stop-color="#F2C80D"/>
<stop offset="0.98" stop-color="#F2950D"/>
</radialGradient>
<linearGradient id="paint126_linear_292_86830" x1="-75070.6" y1="34518.7" x2="-72476" y2="34518.7" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#92A2CE"/>
</linearGradient>
<linearGradient id="paint127_linear_292_86830" x1="-149632" y1="69380.9" x2="-139546" y2="69380.9" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#92A2CE"/>
</linearGradient>
<linearGradient id="paint128_linear_292_86830" x1="412.143" y1="374.488" x2="441.533" y2="376.821" gradientUnits="userSpaceOnUse">
<stop stop-color="#81899A"/>
<stop offset="1" stop-color="#540068"/>
</linearGradient>
<radialGradient id="paint129_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-66283.5 15670.6) scale(941.85 424.384)">
<stop stop-color="#F2C80D"/>
<stop offset="0.98" stop-color="#F2950D"/>
</radialGradient>
<linearGradient id="paint130_linear_292_86830" x1="-53645.1" y1="31311.5" x2="-52075" y2="31311.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#81899A"/>
<stop offset="1" stop-color="#540068"/>
</linearGradient>
<radialGradient id="paint131_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(248.607 238.123) rotate(4.54) scale(0.106247)">
<stop offset="0.02" stop-color="#F28600"/>
<stop offset="0.17" stop-color="#C06A00"/>
<stop offset="0.4" stop-color="#7C4500"/>
<stop offset="0.6" stop-color="#472700"/>
<stop offset="0.77" stop-color="#201200"/>
<stop offset="0.91" stop-color="#090500"/>
<stop offset="1"/>
</radialGradient>
<linearGradient id="paint132_linear_292_86830" x1="295.993" y1="268.741" x2="424.7" y2="278.959" gradientUnits="userSpaceOnUse">
<stop stop-color="#890068"/>
<stop offset="1" stop-color="#81899A"/>
</linearGradient>
<linearGradient id="paint133_linear_292_86830" x1="-369088" y1="122632" x2="-374824" y2="67444.3" gradientUnits="userSpaceOnUse">
<stop offset="0.02" stop-color="#B4B8FF"/>
<stop offset="1" stop-color="#DCFDFF"/>
</linearGradient>
<radialGradient id="paint134_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(294.5 110.564) rotate(4.54113) scale(126.757 126.788)">
<stop stop-color="#2553CD"/>
<stop offset="0.98" stop-color="#0030C3"/>
</radialGradient>
<radialGradient id="paint135_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(341.528 171.048) rotate(4.54113) scale(87.9965 103.551)">
<stop offset="0.02" stop-color="#F23D00"/>
<stop offset="0.17" stop-color="#C03000"/>
<stop offset="0.4" stop-color="#7C1F00"/>
<stop offset="0.6" stop-color="#471200"/>
<stop offset="0.77" stop-color="#200800"/>
<stop offset="0.91" stop-color="#090200"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint136_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(341.528 171.048) rotate(4.54113) scale(68.1816 80.2335)">
<stop offset="0.02" stop-color="#F20000"/>
<stop offset="0.17" stop-color="#C00000"/>
<stop offset="0.4" stop-color="#7C0000"/>
<stop offset="0.6" stop-color="#470000"/>
<stop offset="0.77" stop-color="#200000"/>
<stop offset="0.91" stop-color="#090000"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint137_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-93120 69323) rotate(-6.46041) scale(1780.23 16532.4)">
<stop offset="0.02" stop-color="#F23D00"/>
<stop offset="0.17" stop-color="#C03000"/>
<stop offset="0.4" stop-color="#7C1F00"/>
<stop offset="0.6" stop-color="#471200"/>
<stop offset="0.77" stop-color="#200800"/>
<stop offset="0.91" stop-color="#090200"/>
<stop offset="1"/>
</radialGradient>
<radialGradient id="paint138_radial_292_86830" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-71715.7 53737.3) rotate(-6.46041) scale(1068.91 9929.17)">
<stop offset="0.02" stop-color="#F20000"/>
<stop offset="0.17" stop-color="#C00000"/>
<stop offset="0.4" stop-color="#7C0000"/>
<stop offset="0.6" stop-color="#470000"/>
<stop offset="0.77" stop-color="#200000"/>
<stop offset="0.91" stop-color="#090000"/>
<stop offset="1"/>
</radialGradient>
<linearGradient id="paint139_linear_292_86830" x1="326.115" y1="263.307" x2="341.344" y2="71.5546" gradientUnits="userSpaceOnUse">
<stop stop-color="#540068"/>
<stop offset="1" stop-color="#81899A"/>
</linearGradient>
<linearGradient id="paint140_linear_292_86830" x1="313.49" y1="134.815" x2="318.91" y2="66.5746" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#615BA2"/>
</linearGradient>
<linearGradient id="paint141_linear_292_86830" x1="306.781" y1="256.353" x2="309.822" y2="218.075" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF931F"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint142_linear_292_86830" x1="-51060" y1="9939.96" x2="-49988.8" y2="9939.96" gradientUnits="userSpaceOnUse">
<stop stop-color="#540068"/>
<stop offset="1" stop-color="#81899A"/>
</linearGradient>
<linearGradient id="paint143_linear_292_86830" x1="-54434.5" y1="33519.8" x2="-54434.5" y2="27262.3" gradientUnits="userSpaceOnUse">
<stop stop-color="#81899A"/>
<stop offset="1" stop-color="#540068"/>
</linearGradient>
<linearGradient id="paint144_linear_292_86830" x1="352.364" y1="371.996" x2="362.42" y2="245.376" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9898E"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint145_linear_292_86830" x1="-264185" y1="60366.8" x2="-235612" y2="60366.8" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#92A2CE"/>
</linearGradient>
<linearGradient id="paint146_linear_292_86830" x1="490.348" y1="139.161" x2="488.535" y2="81.2107" gradientUnits="userSpaceOnUse">
<stop stop-color="#DCFDFF"/>
<stop offset="0.98" stop-color="#DEF4FF"/>
</linearGradient>
<linearGradient id="paint147_linear_292_86830" x1="-69365.8" y1="65479.3" x2="-66866.8" y2="65479.3" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#92A2CE"/>
</linearGradient>
<linearGradient id="paint148_linear_292_86830" x1="-106712" y1="15065.2" x2="-101816" y2="15065.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF931F"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="paint149_linear_292_86830" x1="427.333" y1="309.218" x2="514.63" y2="316.148" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#890068"/>
</linearGradient>
</defs>
</svg>
