<?php

namespace app\common\util\dwz;

use app\common\util\DWZ;

class UomgVip extends DWZ {

    protected $domain;
    protected $key;

    public function __construct() {
        $this->domain = sysconf('uomgvip_domain');
        $this->key = sysconf('short_uomgvip_token');
    }

    public function create($url) {


        $result = file_get_contents("http://check.uomg.com/api/dwz/{$this->domain}?token={$this->key}&format=json&sina=0&longurl={$url}");

        $arr = json_decode($result, true);
        if ($arr['code'] == 200) {
            return $arr['ae_url'];
        } else {
            record_system_log("Uomg短链接生成失败：" . $arr['msg']);
            return false;
        }
    }

}
