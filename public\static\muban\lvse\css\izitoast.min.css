﻿/*
* iziToast | v1.1.5
* http://izitoast.marcelodolce.com
* by <PERSON><PERSON>.
*/
.iziToast-capsule {
    font-size: 0;
    height: 0;
    max-height: 1000px;
    width: 100%;
    -webkit-transform: translateZ(0);
            transform: translateZ(0);
    -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
    -webkit-transition: height .5s cubic-bezier(.25, .8, .25, 1), -webkit-transform .5s cubic-bezier(.25, .8, .25, 1);
    transition: height .5s cubic-bezier(.25, .8, .25, 1), -webkit-transform .5s cubic-bezier(.25, .8, .25, 1);
    transition: transform .5s cubic-bezier(.25, .8, .25, 1), height .5s cubic-bezier(.25, .8, .25, 1);
    transition: transform .5s cubic-bezier(.25, .8, .25, 1), height .5s cubic-bezier(.25, .8, .25, 1), -webkit-transform .5s cubic-bezier(.25, .8, .25, 1)
}

.iziToast-capsule,
.iziToast-capsule * {
    -webkit-box-sizing: border-box;
            box-sizing: border-box
}

.iziToast {
    display: inline-block;
    clear: both;
    position: relative;
    font-family: Lato, arial;
    font-size: 14px;
    padding: 8px 45px 9px 0;
    background: hsla(0, 0%, 93%, .9);
    border-color: hsla(0, 0%, 93%, .9);
    width: 100%;
    pointer-events: all;
    cursor: default;
    -webkit-transform: translateX(0);
            transform: translateX(0);
    -webkit-touch-callout: none
        /* iOS Safari */
    ;
    -webkit-user-select: none
        /* Chrome/Safari/Opera */
    ;
    -moz-user-select: none
        /* Firefox */
    ;
    -ms-user-select: none
        /* Internet Explorer/Edge */
    ;
    user-select: none;
    min-height: 54px
}

.iziToast>.iziToast-progressbar {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    background: hsla(0, 0%, 100%, .2)
}

.iziToast>.iziToast-progressbar>div {
    height: 2px;
    width: 100%;
    background: rgba(0, 0, 0, .3);
    border-radius: 0 0 3px 3px
}

.iziToast.iziToast-balloon:before {
    content: '';
    position: absolute;
    right: 8px;
    left: auto;
    width: 0;
    height: 0;
    top: 100%;
    border-right: 0 solid transparent;
    border-left: 15px solid transparent;
    border-top: 10px solid #000;
    border-top-color: inherit;
    border-radius: 0
}

.iziToast.iziToast-balloon .iziToast-progressbar {
    top: 0;
    bottom: auto
}

.iziToast.iziToast-balloon>div {
    border-radius: 0 0 0 3px
}

.iziToast>.iziToast-cover {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    margin: 0;
    background-size: 100%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-color: rgba(0, 0, 0, .1)
}

.iziToast>.iziToast-close {
    position: absolute;
    right: 0;
    top: 0;
    border: 0;
    padding: 0;
    opacity: .6;
    width: 42px;
    height: 100%;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAJPAAACTwBcGfW0QAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAD3SURBVFiF1ZdtDoMgDEBfdi4PwAX8vLFn0qT7wxantojKupmQmCi8R4tSACpgjC2ICCUbEBa8ingjsU1AXRBeR8aLN64FiknswN8CYefBBDQ3whuFESy7WyQMeC0ipEI0A+0FeBvHUFN8xPaUhAH/iKoWsnXHGegy4J0yxialOfaHJAz4bhRzQzgDvdGnz4GbAonZbCQMuBm1K/kcFu8Mp1N2cFFpsxsMuJqqbIGExGl4loARajU1twskJLLhIsID7+tvUoDnIjTg5T9DPH9EBrz8rxjPzciAl9+O8SxI8CzJ8CxKFfh3ynK8Dyb8wNHM/XDqejx/AtNyPO87tNybAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 8px;
    cursor: pointer;
    outline: none
}

.iziToast>.iziToast-close:hover {
    opacity: 1
}

.iziToast>.iziToast-body {
    position: relative;
    padding: 0 0 0 10px;
    height: 100%;
    min-height: 36px;
    margin: 0 0 0 15px
}

.iziToast>.iziToast-body:after {
    content: "";
    display: table;
    clear: both
}

.iziToast>.iziToast-body>.iziToast-buttons {
    min-height: 17px;
    display: inline-block;
    margin: 0 -2px
}

.iziToast>.iziToast-body>.iziToast-buttons>a,
.iziToast>.iziToast-body>.iziToast-buttons>button {
    display: inline-block;
    margin: 6px 2px;
    border-radius: 2px;
    border: 0;
    padding: 5px 10px;
    font-size: 12px;
    letter-spacing: .02em;
    cursor: pointer;
    background: rgba(0, 0, 0, .1);
    color: #000
}

.iziToast>.iziToast-body>.iziToast-buttons>a:hover,
.iziToast>.iziToast-body>.iziToast-buttons>button:hover {
    background: rgba(0, 0, 0, .2)
}

.iziToast>.iziToast-body>.iziToast-icon {
    height: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    display: table;
    font-size: 23px;
    line-height: 24px;
    margin-top: -12px;
    color: #000
}

.iziToast>.iziToast-body>.iziToast-icon.ico-info {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAflBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACCtoPsAAAAKXRSTlMA6PsIvDob+OapavVhWRYPrIry2MxGQ97czsOzpJaMcE0qJQOwVtKjfxCVFeIAAAI3SURBVFjDlJPZsoIwEETnCiGyb8q+qmjl/3/wFmGKwjBROS9QWbtnOqDDGPq4MdMkSc0m7gcDDhF4NRdv8NoL4EcMpzoJglPl/KTDz4WW3IdvXEvxkfIKn7BMZb1bFK4yZFqghZ03jk0nG8N5NBwzx9xU5cxAg8fXi20/hDdC316lcA8o7t16eRuQvW1XGd2d2P8QSHQDDbdIII/9CR3lUF+lbucfJy4WfMS64EJPORnrZxtfc2pjJdnbuags3l04TTtJMXrdTph4Pyg4XAjugAJqMDf5Rf+oXx2/qi4u6nipakIi7CsgiuMSEF9IGKg8heQJKkxIfFSUU/egWSwNrS1fPDtLfon8sZOcYUQml1Qv9a3kfwsEUyJEMgFBKzdV8o3Iw9yAjg1jdLQCV4qbd3no8yD2GugaC3oMbF0NYHCpJYSDhNI5N2DAWB4F4z9Aj/04Cna/x7eVAQ17vRjQZPh+G/kddYv0h49yY4NWNDWMMOMUIRYvlTECmrN8pUAjo5RCMn8KoPmbJ/+Appgnk//Sy90GYBCGgm7IAskQ7D9hFKW4ApB1ei3FSYD9PjGAKygAV+ARFYBH5BsVgG9kkBSAQWKUFYBRZpkUgGVinRWAdUZQDABBQdIcAElDVBUAUUXWHQBZx1gMAGMprM0AsLbVXHsA5trZe93/wp3svQ0YNb/jWV3AIOLsMtlznSNOH7JqjOpDVh7z8qCZR10ftvO4nxeOvPLkpSuvfXnxzKtvXr7j+v8C5ii0e71At7cAAAAASUVORK5CYII=") no-repeat 50% 50%;
    background-size: 85%;
    width: 24px;
    height: 24px
}

.iziToast>.iziToast-body>.iziToast-icon.ico-warning {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAMAAAAPzWOAAAAAkFBMVEUAAAAAAAABAAIAAAABAAIAAAMAAAABAAIBAAIBAAIAAAIAAAABAAIAAAABAAICAAICAAIAAAIAAAAAAAAAAAABAAIBAAIAAAMAAAABAAIBAAMBAAECAAIAAAIAAAIAAAABAAIBAAIBAAMBAAIBAAEAAAIAAAMAAAAAAAABAAECAAICAAIAAAIAAAMAAAQAAAE05yNAAAAAL3RSTlMAB+kD7V8Q+PXicwv7I9iYhkAzJxnx01IV5cmnk2xmHfzexsK4eEw5L7Gei39aRw640awAAAHQSURBVFjD7ZfJdoJAEEWJgCiI4oDiPM8m7///LidErRO7sHrY5u7YXLr7vKqu9kTC0HPmo9n8cJbEQOzqqAdAUHeUZACQuTkGDQBoDJwkHZR0XBz9FkpafXuHP0SJ09mGeJLZ5wwlTmcbA0THPmdEK7XPGTG1zxmInn3OiJ19zkB0jSVTKExMHT0wjAwlWzC0fSPHF1gWRpIhWMYm7fYTFcQGlbemf4dFfdTGg0B/KXM8qBU/3wntbq7rSGqvJ9kla6IpueFJet8fxfem5yhykjyOgNaWF1qSGd5JMNNxpNF7SZQaVh5JzLrTCZIEJ1GyEyVyd+pClMjdaSJK5O40giSRu5PfFiVyd1pAksjdKRnrSsbVdbiHrgT7yss315fkVQPLFQrL+4FHeOXKO5YRFEKv5AiFaMlKLlBpJuVCJlC5sJfvCgztru/3NmBYccPgGTxRAzxn1XGEMUf58pXZvjoOsOCgjL08+b53mtfAM/SVsZcjKLtysQZPqIy9HPP3m/3zKItRwT0LyQo8sTr26tcO83DIUMWIJjierHLsJda/tbNBFY0BP/bKtcM8HNIWCK3aYR4OMzgxo5w5EFLOLKDExXAm9gI4E3iAO94/Ct/lKWuM2LMGbgAAAABJRU5ErkJggg==") no-repeat 50% 50%;
    background-size: 85%;
    width: 24px;
    height: 24px
}

.iziToast>.iziToast-body>.iziToast-icon.ico-error {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAeFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVyEiIAAAAJ3RSTlMA3BsB98QV8uSyWVUFz7+kcWMM2LuZioBpTUVBNcq2qaibj4d1azLZZYABAAACZElEQVRYw7WX25KCMAyGAxUoFDkpiohnV97/DXeGBtoOUprZ2dyo1K82fxKbwJJVp+KQZ7so2mX5oThVQLKwjDe9YZu4DF3ptAn6rxY0qQPOEq9fNC9ha3y77a22ba24v+9Xbe8v8x03dPOC2/NdvB6xeSreLfGJpnx0TyotKqLm2s7Jd/WO6ivXNp0tCy02R/aFz5VQ5wUPlUL5fIfj5KIlVGU0nWHm/5QtoTVMWY8mzIVu1K9O7XH2JiU/xnOOT39gnUfj+lFHddx4tFjL3/H8jjzaFCy2Rf0c/fdQyQszI8BDR973IyMSKa4krjxAiW/lkRvMP+bKK9WbYS1ASQg8dKjaUGlYPwRe/WoIkz8tiQchH5QAEMv6T0k8MD4mUyWr4E7jAWqZ+xWcMIYkXvlwggJ3IvFK+wIOcpXAo8n8P0COAaXyKH4OsjBuZB4ew0IGu+H1SebhNazsQBbWm8yj+hFuUJB5eMsN0IUXmYendAFFfJB5uEkRMYwxmcd6zDGRtmQePEykAgubymMRFmMxCSIPCRbTuFNN5OGORTjmNGc0Po0m8Uv0gcCry6xUhR2QeLii9tofbEfhz/qvNti+OfPqNm2Mq6105FUMvdT4GPmufMiV8PqBMkc+DdT1bjYYbjzU/ew23VP4n3mLAz4n8Jtv/Ui3ceTT2mzz5o1mZt0gnBpmsdjqRqVlmplcPdqa7X23kL9brdm2t/uBYDPn2+tyu48mtIGD10JTuUrukVrbCFiwDzcHrPjxKt7PW+AZQyT/WESO+1WL7f3o+WLHL2dYMSZsg6dg/z360ofvP4//v1NPzgs28WlWAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 80%;
    width: 24px;
    height: 24px
}

.iziToast>.iziToast-body>.iziToast-icon.ico-check {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABABAMAAABYR2ztAAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAACnRSTlMApAPhIFn82wgGv8mVtwAAAKVJREFUSMft0LEJAkEARNFFFEw1NFJb8CKjAy1AEOzAxNw+bEEEg6nyFjbY4LOzcBwX7S/gwUxoTdIn+Jbv4Lv8bx446+kB6VsBtK0B+wbMCKxrwL33wOrVeeChX28n7KTOTjgoEu6DRSYAgAAAAkAmAIAAAAIACQIkMkACAAgAIACAyECBKAOJuCagTJwSUCaUAEMAABEBRwAAEQFLbCJgO4bW+AZKGnktR+jAFAAAAABJRU5ErkJggg==") no-repeat 50% 50%;
    background-size: 85%;
    width: 24px;
    height: 24px
}

.iziToast>.iziToast-body>strong {
    margin: 10px 0 -10px;
    color: #000
}

.iziToast>.iziToast-body>p,
.iziToast>.iziToast-body>strong {
    padding: 0;
    line-height: 16px;
    font-size: 14px;
    text-align: left;
    float: left
}

.iziToast>.iziToast-body>p {
    margin: 10px 0;
    color: rgba(0, 0, 0, .6)
}

.iziToast.iziToast-animateInside .iziToast-buttons *,
.iziToast.iziToast-animateInside .iziToast-icon,
.iziToast.iziToast-animateInside p,
.iziToast.iziToast-animateInside strong {
    opacity: 0
}

.iziToast-target {
    position: relative;
    width: 100%;
    margin: 0 auto
}

.iziToast-target .iziToast-capsule {
    overflow: hidden
}

.iziToast-target .iziToast-capsule:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0
}

.iziToast-target .iziToast-capsule .iziToast {
    width: 100%;
    float: left
}

.iziToast-wrapper {
    z-index: 99999;
    position: fixed;
    width: 100%;
    pointer-events: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column
}

.iziToast-wrapper .iziToast.iziToast-balloon:before {
    border-right: 0 solid transparent;
    border-left: 15px solid transparent;
    border-top: 10px solid #000;
    border-top-color: inherit;
    right: 8px;
    left: auto
}

.iziToast-wrapper-bottomLeft {
    left: 0;
    bottom: 0
}

.iziToast-wrapper-bottomLeft .iziToast.iziToast-balloon:before {
    border-right: 15px solid transparent;
    border-left: 0 solid transparent;
    right: auto;
    left: 8px
}

.iziToast-wrapper-bottomRight {
    right: 0;
    bottom: 0;
    text-align: right
}

.iziToast-wrapper-topLeft {
    left: 0;
    top: 0
}

.iziToast-wrapper-topLeft .iziToast.iziToast-balloon:before {
    border-right: 15px solid transparent;
    border-left: 0 solid transparent;
    right: auto;
    left: 8px
}

.iziToast-wrapper-topRight {
    top: 0;
    right: 0;
    text-align: right
}

.iziToast-wrapper-topCenter {
    top: 0;
    left: 0;
    right: 0;
    text-align: center
}

.iziToast-wrapper-bottomCenter,
.iziToast-wrapper-center {
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center
}

.iziToast-wrapper-center {
    top: 0;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-flow: column;
            flex-flow: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center
}

.iziToast-rtl {
    direction: rtl;
    padding: 8px 0 9px 50px
}

.iziToast-rtl .iziToast-cover {
    left: auto;
    right: 0
}

.iziToast-rtl .iziToast-close {
    right: auto;
    left: 0
}

.iziToast-rtl .iziToast-body {
    padding: 0 10px 0 0;
    margin: 0 16px 0 0
}

.iziToast-rtl .iziToast-body strong {
    padding: 0 0 0 10px
}

.iziToast-rtl .iziToast-body p,
.iziToast-rtl .iziToast-body strong {
    float: right;
    text-align: right
}

.iziToast-rtl .iziToast-body .iziToast-icon {
    left: auto;
    right: 0
}

@media only screen and (min-width:568px) {
    .iziToast-wrapper {
        padding: 10px 15px
    }

    .iziToast-cover {
        border-radius: 3px 0 0 3px
    }

    .iziToast {
        margin: 5px 0;
        border-radius: 3px;
        width: auto
    }

    .iziToast:after {
        content: '';
        z-index: -1;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 3px;
        -webkit-box-shadow: inset 0 -10px 20px -10px rgba(0, 0, 0, .2), inset 0 0 5px rgba(0, 0, 0, .1), 0 8px 8px -5px rgba(0, 0, 0, .25);
                box-shadow: inset 0 -10px 20px -10px rgba(0, 0, 0, .2), inset 0 0 5px rgba(0, 0, 0, .1), 0 8px 8px -5px rgba(0, 0, 0, .25)
    }

    .iziToast.iziToast-color-dark:after {
        -webkit-box-shadow: inset 0 -10px 20px -10px hsla(0, 0%, 100%, .3), 0 10px 10px -5px rgba(0, 0, 0, .25);
                box-shadow: inset 0 -10px 20px -10px hsla(0, 0%, 100%, .3), 0 10px 10px -5px rgba(0, 0, 0, .25)
    }

    .iziToast.iziToast-balloon .iziToast-progressbar {
        background: transparent
    }

    .iziToast.iziToast-balloon:after {
        -webkit-box-shadow: 0 10px 10px -5px rgba(0, 0, 0, .25), inset 0 10px 20px -5px rgba(0, 0, 0, .25);
                box-shadow: 0 10px 10px -5px rgba(0, 0, 0, .25), inset 0 10px 20px -5px rgba(0, 0, 0, .25)
    }

    .iziToast-target .iziToast:after {
        -webkit-box-shadow: inset 0 -10px 20px -10px rgba(0, 0, 0, .2), inset 0 0 5px rgba(0, 0, 0, .1);
                box-shadow: inset 0 -10px 20px -10px rgba(0, 0, 0, .2), inset 0 0 5px rgba(0, 0, 0, .1)
    }
}

.iziToast.iziToast-theme-dark {
    background: #565c70;
    border-color: #565c70
}

.iziToast.iziToast-theme-dark strong {
    color: #fff
}

.iziToast.iziToast-theme-dark p {
    color: hsla(0, 0%, 100%, .7);
    font-weight: 300
}

.iziToast.iziToast-theme-dark .iziToast-close {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfgCR4OIQIPSao6AAAAwElEQVRIx72VUQ6EIAwFmz2XB+AConhjzqTJ7JeGKhLYlyx/BGdoBVpjIpMJNjgIZDKTkQHYmYfwmR2AfAqGFBcO2QjXZCd24bEggvd1KBx+xlwoDpYmvnBUUy68DYXD77ESr8WDtYqvxRex7a8oHP4Wo1Mkt5I68Mc+qYqv1h5OsZmZsQ3gj/02h6cO/KEYx29hu3R+VTTwz6D3TymIP1E8RvEiiVdZfEzicxYLiljSxKIqlnW5seitTW6uYnv/Aqh4whX3mEUrAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE2LTA5LTMwVDE0OjMzOjAyKzAyOjAwl6RMVgAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNi0wOS0zMFQxNDozMzowMiswMjowMOb59OoAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 8px
}

.iziToast.iziToast-theme-dark .iziToast-icon {
    color: #fff
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-info {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAflBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////vroaSAAAAKXRSTlMA6PsIvDob+OapavVhWRYPrIry2MxGQ97czsOzpJaMcE0qJQOwVtKjfxCVFeIAAAI3SURBVFjDlJPZsoIwEETnCiGyb8q+qmjl/3/wFmGKwjBROS9QWbtnOqDDGPq4MdMkSc0m7gcDDhF4NRdv8NoL4EcMpzoJglPl/KTDz4WW3IdvXEvxkfIKn7BMZb1bFK4yZFqghZ03jk0nG8N5NBwzx9xU5cxAg8fXi20/hDdC316lcA8o7t16eRuQvW1XGd2d2P8QSHQDDbdIII/9CR3lUF+lbucfJy4WfMS64EJPORnrZxtfc2pjJdnbuags3l04TTtJMXrdTph4Pyg4XAjugAJqMDf5Rf+oXx2/qi4u6nipakIi7CsgiuMSEF9IGKg8heQJKkxIfFSUU/egWSwNrS1fPDtLfon8sZOcYUQml1Qv9a3kfwsEUyJEMgFBKzdV8o3Iw9yAjg1jdLQCV4qbd3no8yD2GugaC3oMbF0NYHCpJYSDhNI5N2DAWB4F4z9Aj/04Cna/x7eVAQ17vRjQZPh+G/kddYv0h49yY4NWNDWMMOMUIRYvlTECmrN8pUAjo5RCMn8KoPmbJ/+Appgnk//Sy90GYBCGgm7IAskQ7D9hFKW4ApB1ei3FSYD9PjGAKygAV+ARFYBH5BsVgG9kkBSAQWKUFYBRZpkUgGVinRWAdUZQDABBQdIcAElDVBUAUUXWHQBZx1gMAGMprM0AsLbVXHsA5trZe93/wp3svQ0YNb/jWV3AIOLsMtlznSNOH7JqjOpDVh7z8qCZR10ftvO4nxeOvPLkpSuvfXnxzKtvXr7j+v8C5ii0e71At7cAAAAASUVORK5CYII=") no-repeat 50% 50%;
    background-size: 85%
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-warning {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAMAAAAPzWOAAAAAllBMVEUAAAD////+//3+//3+//3///////z+//3+//3+//3////////////9//3////+//39//3///3////////////+//3+//39//3///z+//z+//7///3///3///3///3////////+//3+//3+//3+//z+//3+//7///3///z////////+//79//3///3///z///v+//3///+trXouAAAAMHRSTlMAB+j87RBf+PXiCwQClSPYhkAzJxnx05tSyadzcmxmHRbp5d7Gwrh4TDkvsYt/WkdQzCITAAAB1UlEQVRYw+3XaXKCQBCGYSIIighoxCVqNJrEPfly/8vFImKXduNsf/Mc4K1y7FnwlMLQc/bUbj85R6bA1LXRDICg6RjJcZa7NQYtnLUGTpERSiOXxrOPkv9s30iGKDmtbYir3H7OUHJa2ylAuvZzRvzUfs7Ii/2cgfTt54x82s8ZSM848gJmYtroQzA2jHwA+LkBIEuMGt+QIng1igzlyMrkuP2CyOi47axRaYTL5jhDJehoR+aovC29s3iIyly3Eb+hRCvZo2qsGTnhKr2cLDS+J73GsqBI9W80UCmWWpEuhIjh6ZRGjyNRarjzKGJ2Ou2himCvjHwqI+rTqQdlRH06TZQR9ek0hiqiPp06mV4ke7QPX6ERUZxO8Uo3sqrfhxvoRrCpvXwL/UjR9GRHMIvLgke4d5QbiwhM6JV2YKKF4vIl7XIBkwm4keryJVmvk/TfwcmPwQNkUQuyA2/sYGwnXL7GPu4bW1jYsmevrNj09/MGZMOEPXslQVqO8hqykD17JfPHP/bmo2yGGpdZiH3IZvzZa7B3+IdDjjpjesHJcvbs5dZ/e+cddVoDdvlq7x12Nac+iN7e4R8OXTjp0pw5CGnOLNDEzeBs5gVwFniAO+8f8wvfeXP2hyqnmwAAAABJRU5ErkJggg==") no-repeat 50% 50%;
    background-size: 85%
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-error {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAeFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GqOSsAAAAJ3RSTlMA3BsB98QV8uSyWVUFz6RxYwzYvbupmYqAaU1FQTXKv7abj4d1azKNUit3AAACZElEQVRYw7WXaZOCMAyGw30UORRQBLxX/v8/3BkaWjrY2szO5otKfGrzJrEp6Kw6F8f8sI+i/SE/FucKSBaWiT8p5idlaEtnXTB9tKDLLHAvdSatOan3je93k9F2vRF36+mr1a6eH2NFNydoHq/ieU/UXcWjjk9XykdNWq2ywtp4tXL6Wb2T/MqtzzZutsrNyfvA51KoQROhVCjfrnASIRpSVUZiD5v4RbWExjRdJzSmOsZFvzYz59kRSr6V5zE+/QELHkNdb3VRx45HS1b1u+zfkkcbRAZ3qJ9l/A4qefHUDMShJe+6kZKJDD2pLQ9Q4lu+5Q7rz7Plperd7AtQEgIPI6o2dxr2D4GXvxqCiKcn8cD4gxIAEt7/GYkHL16KqeJd0NB4gJbXfgVnzCGJlzGcocCVSLzUvoAj9xJ4NF7/R8gxoVQexc/hgBpSebjPjgPs59cHmYfn7NkDb6wXmUf1I1ygIPPw4gtgCE8yDw8eAop4J/PQcBExjQmZx37MsZB2ZB4cLKQCG5vKYxMWSzMxIg8pNtOyUkvkocEmXGo69mh8FgnxS4yBwMvDrJSNHZB4uC3ayz/YkcIP4lflwVIT+OU07ZSjrbTkZQ6dTPkYubZ8GC/Cqxu6WvJZII93dcCw46GdNqdpTeF/tiMOuDGB9z/NI6NvyWetGPM0g+bVNeovBmamHXWj0nCbEaGeTMN2PWrqd6cM26ZxP2DeJvj+ph/30Zi/GmRbtlK5SptI+nwGGnvH6gUruT+L16MJHF+58rwNIifTV0vM8+hwMeOXAb6Yx0wXT+b999WXfvn+8/X/F7fWzjdTord5AAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 80%
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-check {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABABAMAAABYR2ztAAAAIVBMVEUAAAD////////////////////////////////////////PIev5AAAACnRSTlMApAPhIFn82wgGv8mVtwAAAKVJREFUSMft0LEJAkEARNFFFEw1NFJb8CKjAy1AEOzAxNw+bEEEg6nyFjbY4LOzcBwX7S/gwUxoTdIn+Jbv4Lv8bx446+kB6VsBtK0B+wbMCKxrwL33wOrVeeChX28n7KTOTjgoEu6DRSYAgAAAAkAmAIAAAAIACQIkMkACAAgAIACAyECBKAOJuCagTJwSUCaUAEMAABEBRwAAEQFLbCJgO4bW+AZKGnktR+jAFAAAAABJRU5ErkJggg==") no-repeat 50% 50%;
    background-size: 85%
}

.iziToast.iziToast-theme-dark strong {
    font-weight: 500
}

.iziToast.iziToast-theme-dark .iziToast-buttons a,
.iziToast.iziToast-theme-dark .iziToast-buttons button {
    color: #fff;
    background: hsla(0, 0%, 100%, .1)
}

.iziToast.iziToast-theme-dark .iziToast-buttons a:hover,
.iziToast.iziToast-theme-dark .iziToast-buttons button:hover {
    background: hsla(0, 0%, 100%, .2)
}

.iziToast.iziToast-color-red {
    background: rgba(243, 186, 189, .9);
    border-color: rgba(243, 186, 189, .9)
}

.iziToast.iziToast-color-yellow {
    background: hsla(55, 75%, 81%, .9);
    border-color: hsla(55, 75%, 81%, .9)
}

.iziToast.iziToast-color-blue {
    background: rgba(181, 225, 249, .9);
    border-color: rgba(181, 225, 249, .9)
}

.iziToast.iziToast-color-green {
    background: rgba(180, 241, 196, .9);
    border-color: rgba(180, 241, 196, .9)
}

.iziToast.iziToast-layout2 .iziToast-body>p {
    width: 100%
}

.iziToast.revealIn,
.iziToast .revealIn {
    -webkit-animation: a 1s cubic-bezier(.25, 1.6, .25, 1) both;
    animation: a 1s cubic-bezier(.25, 1.6, .25, 1) both
}

.iziToast.slideIn,
.iziToast .slideIn {
    -webkit-animation: b 1s cubic-bezier(.16, .81, .32, 1) both;
    animation: b 1s cubic-bezier(.16, .81, .32, 1) both
}

.iziToast.bounceInLeft {
    -webkit-animation: c .7s ease-in-out both;
    animation: c .7s ease-in-out both
}

.iziToast.bounceInRight {
    -webkit-animation: d .85s ease-in-out both;
    animation: d .85s ease-in-out both
}

.iziToast.bounceInDown {
    -webkit-animation: e .7s ease-in-out both;
    animation: e .7s ease-in-out both
}

.iziToast.bounceInUp {
    -webkit-animation: f .7s ease-in-out both;
    animation: f .7s ease-in-out both
}

.iziToast.fadeIn {
    -webkit-animation: g .5s ease both;
    animation: g .5s ease both
}

.iziToast.fadeInUp {
    -webkit-animation: h .7s ease both;
    animation: h .7s ease both
}

.iziToast.fadeInDown {
    -webkit-animation: i .7s ease both;
    animation: i .7s ease both
}

.iziToast.fadeInLeft {
    -webkit-animation: j .85s cubic-bezier(.25, .8, .25, 1) both;
    animation: j .85s cubic-bezier(.25, .8, .25, 1) both
}

.iziToast.fadeInRight {
    -webkit-animation: k .85s cubic-bezier(.25, .8, .25, 1) both;
    animation: k .85s cubic-bezier(.25, .8, .25, 1) both
}

.iziToast.flipInX {
    -webkit-animation: l .85s cubic-bezier(.35, 0, .25, 1) both;
    animation: l .85s cubic-bezier(.35, 0, .25, 1) both
}

.iziToast.fadeOut {
    -webkit-animation: m .7s ease both;
    animation: m .7s ease both
}

.iziToast.fadeOutDown {
    -webkit-animation: n .7s cubic-bezier(.4, .45, .15, .91) both;
    animation: n .7s cubic-bezier(.4, .45, .15, .91) both
}

.iziToast.fadeOutUp {
    -webkit-animation: o .7s cubic-bezier(.4, .45, .15, .91) both;
    animation: o .7s cubic-bezier(.4, .45, .15, .91) both
}

.iziToast.fadeOutLeft {
    -webkit-animation: p .5s ease both;
    animation: p .5s ease both
}

.iziToast.fadeOutRight {
    -webkit-animation: q .5s ease both;
    animation: q .5s ease both
}

.iziToast.flipOutX {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation: r .7s cubic-bezier(.4, .45, .15, .91) both;
    animation: r .7s cubic-bezier(.4, .45, .15, .91) both
}

@-webkit-keyframes a {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, 1)
    }

    to {
        opacity: 1
    }
}

@-webkit-keyframes b {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateX(0)
    }
}

@-webkit-keyframes c {
    0% {
        opacity: 0;
        -webkit-transform: translateX(280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(-20px)
    }

    70% {
        -webkit-transform: translateX(10px)
    }

    to {
        -webkit-transform: translateX(0)
    }
}

@-webkit-keyframes d {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(20px)
    }

    70% {
        -webkit-transform: translateX(-10px)
    }

    to {
        -webkit-transform: translateX(0)
    }
}

@-webkit-keyframes e {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(10px)
    }

    70% {
        -webkit-transform: translateY(-5px)
    }

    to {
        -webkit-transform: translateY(0)
    }
}

@-webkit-keyframes f {
    0% {
        opacity: 0;
        -webkit-transform: translateY(200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(-10px)
    }

    70% {
        -webkit-transform: translateY(5px)
    }

    to {
        -webkit-transform: translateY(0)
    }
}

@-webkit-keyframes a {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, 1);
                transform: scale3d(.3, .3, 1)
    }

    to {
        opacity: 1
    }
}

@keyframes a {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, 1);
                transform: scale3d(.3, .3, 1)
    }

    to {
        opacity: 1
    }
}

@-webkit-keyframes b {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px);
                transform: translateX(50px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@keyframes b {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px);
                transform: translateX(50px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@-webkit-keyframes c {
    0% {
        opacity: 0;
        -webkit-transform: translateX(280px);
                transform: translateX(280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(-20px);
                transform: translateX(-20px)
    }

    70% {
        -webkit-transform: translateX(10px);
                transform: translateX(10px)
    }

    to {
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@keyframes c {
    0% {
        opacity: 0;
        -webkit-transform: translateX(280px);
                transform: translateX(280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(-20px);
                transform: translateX(-20px)
    }

    70% {
        -webkit-transform: translateX(10px);
                transform: translateX(10px)
    }

    to {
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@-webkit-keyframes d {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-280px);
                transform: translateX(-280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(20px);
                transform: translateX(20px)
    }

    70% {
        -webkit-transform: translateX(-10px);
                transform: translateX(-10px)
    }

    to {
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@keyframes d {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-280px);
                transform: translateX(-280px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateX(20px);
                transform: translateX(20px)
    }

    70% {
        -webkit-transform: translateX(-10px);
                transform: translateX(-10px)
    }

    to {
        -webkit-transform: translateX(0);
                transform: translateX(0)
    }
}

@-webkit-keyframes e {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-200px);
                transform: translateY(-200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(10px);
                transform: translateY(10px)
    }

    70% {
        -webkit-transform: translateY(-5px);
                transform: translateY(-5px)
    }

    to {
        -webkit-transform: translateY(0);
                transform: translateY(0)
    }
}

@keyframes e {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-200px);
                transform: translateY(-200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(10px);
                transform: translateY(10px)
    }

    70% {
        -webkit-transform: translateY(-5px);
                transform: translateY(-5px)
    }

    to {
        -webkit-transform: translateY(0);
                transform: translateY(0)
    }
}

@-webkit-keyframes f {
    0% {
        opacity: 0;
        -webkit-transform: translateY(200px);
                transform: translateY(200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(-10px);
                transform: translateY(-10px)
    }

    70% {
        -webkit-transform: translateY(5px);
                transform: translateY(5px)
    }

    to {
        -webkit-transform: translateY(0);
                transform: translateY(0)
    }
}

@keyframes f {
    0% {
        opacity: 0;
        -webkit-transform: translateY(200px);
                transform: translateY(200px)
    }

    50% {
        opacity: 1;
        -webkit-transform: translateY(-10px);
                transform: translateY(-10px)
    }

    70% {
        -webkit-transform: translateY(5px);
                transform: translateY(5px)
    }

    to {
        -webkit-transform: translateY(0);
                transform: translateY(0)
    }
}

@-webkit-keyframes g {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes g {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@-webkit-keyframes h {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes h {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes i {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes i {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes j {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes j {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes k {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes k {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes l {
    0% {
        -webkit-transform: perspective(400px) rotateX(90deg);
        transform: perspective(400px) rotateX(90deg);
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotateX(-20deg);
        transform: perspective(400px) rotateX(-20deg)
    }

    60% {
        -webkit-transform: perspective(400px) rotateX(10deg);
        transform: perspective(400px) rotateX(10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotateX(-5deg);
        transform: perspective(400px) rotateX(-5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

@keyframes l {
    0% {
        -webkit-transform: perspective(400px) rotateX(90deg);
        transform: perspective(400px) rotateX(90deg);
        opacity: 0
    }

    40% {
        -webkit-transform: perspective(400px) rotateX(-20deg);
        transform: perspective(400px) rotateX(-20deg)
    }

    60% {
        -webkit-transform: perspective(400px) rotateX(10deg);
        transform: perspective(400px) rotateX(10deg);
        opacity: 1
    }

    80% {
        -webkit-transform: perspective(400px) rotateX(-5deg);
        transform: perspective(400px) rotateX(-5deg)
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }
}

@-webkit-keyframes m {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes m {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@-webkit-keyframes n {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

@keyframes n {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0)
    }
}

@-webkit-keyframes o {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

@keyframes o {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
}

@-webkit-keyframes p {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0)
    }
}

@keyframes p {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0)
    }
}

@-webkit-keyframes q {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0)
    }
}

@keyframes q {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0)
    }
}

@-webkit-keyframes r {
    0% {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotateX(-20deg);
        transform: perspective(400px) rotateX(-20deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotateX(90deg);
        transform: perspective(400px) rotateX(90deg);
        opacity: 0
    }
}

@keyframes r {
    0% {
        -webkit-transform: perspective(400px);
        transform: perspective(400px)
    }

    30% {
        -webkit-transform: perspective(400px) rotateX(-20deg);
        transform: perspective(400px) rotateX(-20deg);
        opacity: 1
    }

    to {
        -webkit-transform: perspective(400px) rotateX(90deg);
        transform: perspective(400px) rotateX(90deg);
        opacity: 0
    }
}