<?php

namespace app\pay\controller;

use service\PayService;

class JingPay extends PayService {

    /**
     * 参考接口
     * @param string $trade_no 外部单号
     * @param string $subject 标题
     * @param float $totalAmount 支付金额
     */
    public function pay($trade_no, $subject, $totalAmount) {

        $order = $this->loadOrder($trade_no); //内置函数loadOrder，获取订单对象

        $appid = $order->channelAccount->params->app_id; //获取支付通道账号参数，示例获取账号app_id参数
        $channel_key = $order->channelAccount->params->key; //获取支付通道账号参数，示例获取账号key参数
        $channel_type = $order->channelAccount->params->type;

        $callbackurl = url("pay/JingPay/callback"); //同步回调地址
        $notifyurl = url("pay/JingPay/notify"); //异步回调地址

        $native = array(
            "appid" => $appid,
            "type" => $channel_type,
            "price" => $totalAmount,
            "name" => $subject,
            "body" => $subject,
            "notify_url" => $notifyurl,
            "out_trade_no" => $trade_no,
            "ip" => $this->request->ip(),
            "server" => url("index/index/index"),
        );
        ksort($native);
        $arg = "";
        foreach ($native as $key => $val) {
            $arg .= $key . "=" . ($val) . "&";
        }
        $sign = md5(substr($arg, 0, -1) . $this->authcode($channel_key, "DECODE", "jingfaka"));
        $native["sign"] = $sign;

        $res = $this->request_post("https://pay.itcn.net/api/gateway/request.html", $native);

        $res = json_decode($res, true);
        if ($res['state'] == 'succeeded') {
            $payUrl = $res['return_data']['url'];
            return $this->qrcode($payUrl);
        } else {
            print_r($res);
        }
    }

    private function request_post($url = '', $post_data = array()) {
        if (empty($url) || empty($post_data)) {
            return false;
        }

        $o = "";
        foreach ($post_data as $k => $v) {
            $o .= "$k=" . urlencode($v) . "&";
        }
        $post_data = substr($o, 0, -1);

        $postUrl = $url;
        $curlPost = $post_data;
        $ch = curl_init(); //初始化curl
        curl_setopt($ch, CURLOPT_URL, $postUrl); //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0); //设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_POST, 1); //post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  // 跳过检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 跳过检查
        $data = curl_exec($ch); //运行curl
        curl_close($ch);

        return $data;
    }

    /**
     * 页面回调
     */
    public function callback() {
        $out_trade_no = input("out_trade_no"); //获取同步回调订单号
        header("Location:" . url('index/Pay/pay_result', ['orderid' => $out_trade_no]));
        die;
    }

    /**
     * 异步回调
     */
    public function notify() {
        $data = input('');

        $sign = $data['sign'];
        unset($data['sign']);

        $order = $this->loadOrder($data['out_trade_no']);
        $channel_key = $order->channelAccount->params->key;

        ksort($data);
        $arg = "";
        foreach ($data as $key => $val) {
            $arg .= $key . "=" . ($val) . "&";
        }
        $sign_new = md5(substr($arg, 0, -1) . $this->authcode($channel_key, "DECODE", "jingfaka"));

        if ($sign == $sign_new) {
            $order->transaction_id = $data['trade_no'];
            $this->completeOrder($order);
            echo "SUCCESS";
        } else {
            echo "FAIL";
        }
    }

    /**
     * 退款接口
     */
    public function refund($order) {
        $appid = $order->channelAccount->params->app_id;
        $channel_key = $order->channelAccount->params->key;

        $native = array(
            "system_order" => $order->transaction_id,
            "appid" => $appid,
        );
        ksort($native);
        $arg = "";
        foreach ($native as $key => $val) {
            $arg .= $key . "=" . ($val) . "&";
        }
        $sign = md5(substr($arg, 0, -1) . $this->authcode($channel_key, "DECODE", "jingfaka"));
        $native["sign"] = $sign;

        $res = $this->request_post("https://pay.itcn.net/api/gateway/refund.html", $native);

        $res = json_decode($res, true);
        if ($res['state'] == "succeeded") {
            return ["code" => 1, "msg" => "退款成功"];
        } else {
            return ["code" => 0, "msg" => "退款失败，原因" . $res['message']];
        }
    }

    private function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0) {
        $ckey_length = 4;
        $key = md5($key ? $key : $GLOBALS['discuz_auth_key']);
        $keya = md5(substr($key, 0, 16));
        $keyb = md5(substr($key, 16, 16));
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';
        $cryptkey = $keya . md5($keya . $keyc);
        $key_length = strlen($cryptkey);
        $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
        $string_length = strlen($string);
        $result = '';
        $box = range(0, 255);
        $rndkey = array();
        for ($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }
        for ($j = $i = 0; $i < 256; $i++) {
            $j = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }
        for ($a = $j = $i = 0; $i < $string_length; $i++) {
            $a = ($a + 1) % 256;
            $j = ($j + $box[$a]) % 256;
            $tmp = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }
        if ($operation == 'DECODE') {
            if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            return $keyc . str_replace('=', '', base64_encode($result));
        }
    }

}
