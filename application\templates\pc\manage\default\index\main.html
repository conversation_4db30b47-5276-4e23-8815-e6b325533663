{extend name='./content'}

{block name="style"}
<style>
    .ibox{
        margin-bottom: 0px;
    }
    .ibox-content{
        padding:0px
    } 
    .tipbox .alert{
        margin-bottom: 0px;
        margin:15px;
    }
</style>
{/block}

{block name="content"}
<div class="tipbox">

</div>
<div>
    <iframe src="{:url('manage/index/bgdata')}" style='width: 100%;'  frameborder="no"  />
</div>
{/block}

{block name="script"}
<script>
    function ignore(v)
    {
        layer.confirm('确定要忽略此版本？忽略后可从版本列表查看更新！', {
            btn: ['确定', '关闭'] //按钮
        }, function () {
            $.ajax({type: "POST",
                url: "{:url('admin/cloud/ignoreVersion')}",
                data: {version: v},
                dataType: 'json',
                success: function (result) {
                    location.reload();
                },
            });
        }, function () {

        });
    }
    $(function () {





        $.ajax({type: "POST",
            url: "{:url('admin/cloud/checkVersion')}",
            dataType: 'json',
            success: function (result) {
                if (result.code == 1)
                {
                    if (result.data != null)
                    {
                        $(".tipbox").html('<div style="position: relative;"><span onclick="ignore(' + result.data.newversion + ')" style="position: absolute;right: 40px;top: 10px;cursor: pointer;"><i class="fa fa-close"></i> 忽略</span><p class="alert alert-danger">发下新版本：V' + result.data.newversion + '<button data-open="{:url(\'admin/cloud/upgrade\')}" style="margin-left:15px" class="layui-btn layui-btn-normal layui-btn-mini">去更新</button><br>' + result.data.content + '</p></div>');
                        if (result.data.enforce == 1)
                        {
                            layer.alert("发现一个强制更新补丁，为了您的系统安全与稳定，推荐您立即更新！");
                        }
                    }
                }
            },
        });



    })
</script>
{/block}
