# Modern Theme - 现代化主题

这是一个为发卡平台设计的现代化主题，采用最新的设计理念和前端技术，提供优秀的用户体验。

## 🎨 设计特色

### 现代化设计系统
- **一致的设计语言**: 统一的颜色、字体、间距和组件规范
- **响应式设计**: 完美适配桌面端、平板和移动设备
- **深色模式支持**: 自动检测用户偏好，支持主题切换
- **无障碍设计**: 遵循WCAG 2.1标准，支持键盘导航和屏幕阅读器

### 视觉效果
- **渐变背景**: 现代化的渐变色彩搭配
- **毛玻璃效果**: 半透明背景和模糊效果
- **微交互动画**: 流畅的过渡动画和悬停效果
- **卡片式布局**: 清晰的信息层次和视觉分组

## 🚀 技术特性

### CSS架构
- **CSS变量系统**: 便于主题定制和维护
- **模块化设计**: 组件化的CSS结构
- **现代CSS特性**: 使用Grid、Flexbox、CSS变量等
- **优化性能**: 最小化重绘和回流

### JavaScript功能
- **模块化架构**: ES6+语法，面向对象设计
- **交互增强**: 导航、模态框、表单验证等
- **性能优化**: 防抖、节流、懒加载等技术
- **渐进增强**: 基础功能不依赖JavaScript

### 移动端优化
- **移动优先**: Mobile-first响应式设计
- **触摸友好**: 44px最小触摸目标
- **手势支持**: 滑动导航和交互
- **PWA就绪**: 支持离线访问和安装

## 📁 文件结构

```
modern/
├── css/
│   └── modern.css          # 主样式文件
├── js/
│   └── modern.js           # 主脚本文件
├── images/                 # 主题图片资源
├── fonts/                  # 字体文件
└── README.md              # 说明文档
```

## 🛠️ 安装使用

### 1. 配置主题
在ThinkPHP配置文件中设置主题：

```php
// application/config.php
'template' => [
    'view_base' => __DIR__ . DS . 'templates' . DS,
    'view_theme' => 'modern',  // 设置为modern主题
    'view_platform' => 'pc',   // 或 'mobile'
],
```

### 2. 引入资源
在模板中引入CSS和JS文件：

```html
<!-- CSS -->
<link rel="stylesheet" href="__RES__/theme/modern/css/modern.css">

<!-- JavaScript -->
<script src="__RES__/theme/modern/js/modern.js"></script>
```

### 3. 使用组件
主题提供了丰富的组件类：

```html
<!-- 按钮 -->
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-secondary">次要按钮</button>

<!-- 卡片 -->
<div class="card">
    <div class="card-header">
        <h3>卡片标题</h3>
    </div>
    <div class="card-body">
        <p>卡片内容</p>
    </div>
</div>

<!-- 表单 -->
<div class="form-group">
    <label class="form-label">标签</label>
    <input type="text" class="form-input" placeholder="请输入...">
</div>
```

## 🎯 组件列表

### 基础组件
- **按钮**: `btn`, `btn-primary`, `btn-secondary`, `btn-outline`
- **卡片**: `card`, `card-header`, `card-body`, `card-footer`
- **表单**: `form-group`, `form-label`, `form-input`
- **导航**: `navbar`, `nav-link`, `navbar-nav`

### 布局组件
- **容器**: `container`, `container-fluid`
- **网格**: `grid`, `grid-cols-1/2/3/4`
- **弹性布局**: `flex`, `flex-col`, `items-center`, `justify-between`

### 工具类
- **间距**: `m-*`, `p-*`, `mt-*`, `mb-*`, `px-*`, `py-*`
- **文本**: `text-center`, `text-lg`, `font-bold`, `text-gray-600`
- **背景**: `bg-white`, `bg-primary-500`, `bg-gray-100`
- **边框**: `border`, `rounded`, `shadow-md`

### 交互组件
- **模态框**: `modal`, `modal-content`, `modal-backdrop`
- **下拉菜单**: `dropdown`, `dropdown-menu`, `dropdown-item`
- **选项卡**: `tabs`, `tab-list`, `tab-button`, `tab-panel`
- **手风琴**: `accordion-item`, `accordion-header`, `accordion-content`

## 🎨 自定义主题

### 修改颜色
通过CSS变量自定义主题颜色：

```css
:root {
    --primary-500: #your-color;
    --primary-600: #your-darker-color;
    /* 其他颜色变量 */
}
```

### 修改字体
```css
:root {
    --font-sans: 'Your Font', sans-serif;
}
```

### 修改间距
```css
:root {
    --space-4: 1.5rem;  /* 修改基础间距 */
}
```

## 📱 响应式断点

- **手机**: < 768px
- **平板**: 768px - 1024px  
- **桌面**: > 1024px

## 🔧 浏览器支持

- **现代浏览器**: Chrome 60+, Firefox 60+, Safari 12+, Edge 79+
- **移动浏览器**: iOS Safari 12+, Chrome Mobile 60+
- **不支持**: IE 11及以下版本

## 📈 性能优化

### CSS优化
- 使用CSS变量减少重复代码
- 合理使用CSS Grid和Flexbox
- 避免深层嵌套选择器

### JavaScript优化
- 事件委托减少内存占用
- 防抖和节流优化性能
- 懒加载图片和组件

### 加载优化
- 关键CSS内联
- 非关键资源延迟加载
- 图片压缩和WebP格式

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

## 🆘 技术支持

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- QQ群: 123456789
- 微信: example_wechat

---

**享受现代化的用户体验！** 🎉
