{extend name="base"}


{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->
            <div class="search-form-wrapper">

                <div class="row">
                    <div class="col-5">
                        <a href="{:url('goods_category/index')}" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                            <i class="bx bx-grid-alt me-1"></i>分类列表
                        </a>
                    </div>
                    <div class="col-5">
                        <a href="{:url('goods/add')}" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                            <i class="bx bx-plus me-1"></i>添加商品
                        </a>
                    </div>
                    <div class="col-2">
                        <a href="{:url('goods/trash')}" class="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center">
                            <i class="bx bx-trash-alt fs-5"></i>
                        </a>
                    </div>
                </div>

                <form class="mb-3 pb-4 mt-2" action="" method="get">

                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm">商品分类</span>
                        <select name="cate_id" class="form-select  form-select-sm">
                            <option value="" {if $Think.get.cate_id==''}selected{/if}>全部分类</option>
                            {foreach $categorys as $v}
                            <option value="{$v.id|htmlentities}" {if $Think.get.cate_id==$v.id}selected{/if}>{$v.name}</option>
                            {/foreach}
                        </select>
                    </div>

                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm">商品类型</span>
                        <select name="type" class="form-select  form-select-sm">
                            <option value="" {if $Think.get.type=='' }selected{/if}>全部类型 </option>
                            <option value="1" {if $Think.get.type==1}selected{/if}>普通商品 </option>
                            <option value="2" {if $Think.get.type==1}selected{/if}>代理商品 </option>
                        </select>
                    </div>
                    <div class="input-group mb-2  input-group-sm">
                        <span class="input-group-text input-group-sm" id="basic-addon1">商品名称</span>
                        <input type="text" class="form-control form-control-sm" name="name" value="" maxlength="30" placeholder="商品名称">
                    </div>
                    <button class="btn btn-primary w-100 d-flex align-items-center justify-content-center" type="submit">
                        <i class="bx bx-search me-2"></i>查询
                    </button>
                </form>
            </div>

            {foreach $goodsList as $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">商品名称:&nbsp;&nbsp;{$v.name}</h6>
                    {if $v.status==1}
                    <span onclick="change_status('{$v.id}', 0)" class="badge rounded-pill bg-success float-end">上架中</span>
                    {else/}
                    <span onclick="change_status('{$v.id}', 1)" class="badge rounded-pill bg-danger float-end">已下架</span>
                    {/if}
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>商品价格：</span>
                        <span>{$v.price}元</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>库存卡：</span>
                        <span>
                            <span class="fw-bold">{$v.cards_stock_count}</span>张 
                            {if $v->cards_stock_count==0}
                            <span class="text-danger ms-1">缺货</span>
                            {/if}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>已卖出：</span>
                        <span><span class="fw-bold">{$v.cards_sold_count}</span>张</span>
                    </div>
                    {if $v.can_proxy == 1}
                    <div class="order-wrap-text" style='display: flex;align-items: center;'>
                        <span>开启代理：</span>
                        <span>
                            <span class="badge rounded-pill bg-success">是</span>
                            <a class="ml-2" href="javascript:void(0);" onclick="forceasync(this, '{$v.id}')" ><span class="goods-warp-btn">同步</span></a>
                        </span>
                    </div>
                    {/if}

                </div>

                <div class="goods-warp d-flex align-items-center mt-2 justify-content-end">
                    <a class="me-1" href="javascript:void(0);" onclick="$.x_show('商品链接', '{:url(\'goods/link\',[\'id\'=>$v.id])}', '90%');"><span class="goods-warp-btn">链接</span></a>
                    <a class="me-1 dumpCard"  href="javascript:;" data-id="{$v.id}" data-goodsname="{$v.name}"><span class="goods-warp-btn">导出库存</span></a>
                    <a class="me-1" href="{:url('goods/dumpCards',['goods_id'=>$v.id,'status'=>2])}" target="_blank"><span class="goods-warp-btn">导出已卖出</span></a>

                    <a class="me-1" href="{:url('goods_card/add',['goods_id'=>$v.id])}"><span class="goods-warp-btn">加卡</span></a>
                    <a class="me-1" href="{:url('goods/edit',['id'=>$v.id])}"><span class="goods-warp-btn">编辑</span></a>
                    <a href="javascript:void(0);" onclick="del(this, '{$v.id}')"><span class="goods-warp-btn goods-warp-btn-delete" >删除</span></a>
                </div>
            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>
<!-- End Page-content -->
<div class="modal fade" id="exportCard" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title mt-0" >导出卡密</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <form id="export_form" class="form-horizontal" method="POST" action="{:url('goods/dumpCards')}" target="_blank">
                    <input type="hidden" id="goods_id" name="goods_id" value="">
                    <div class="form-group">
                        <div class="col-sm-2">
                            <label class="control-label">商品名称</label>
                        </div>
                        <div class="col-sm-6">
                            <p id="goodsname" style="padding-top: 5px;"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-2">
                            <label class="control-label">导出范围</label>
                        </div>
                        <div class="col-sm-6">
                            <label class="radio-inline">
                                <input type="radio" name="range" value="0" checked> 全部库存的卡密
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="range" value="1"> 导出指定的数量
                            </label>
                        </div>
                    </div>
                    <div class="form-group" id="exportNUm" style="display:none">
                        <div class="col-sm-2">
                            <label class="control-label">导出数量</label>
                        </div>
                        <div class="col-sm-6">
                            <input name="number" type="number" class="form-control" placeholder="请输入正整数">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-2">
                            <label class="control-label">是否删除</label>
                        </div>
                        <div class="col-sm-6">
                            <label class="radio-inline">
                                <input type="radio" name="del" value="0" checked> 仅导出不做删除
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="del" value="1"> 导出并删除卡密
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-2">
                            <label class="control-label">商品名称</label>
                        </div>
                        <div class="col-sm-6">
                            <label class="radio-inline">
                                <input type="radio" name="need_goods_name" value="1" checked> 导出商品名
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="need_goods_name" value="0"> 不需要商品名
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-2">
                            <label class="control-label">导出格式</label>
                        </div>
                        <div class="col-sm-6">
                            <label class="radio-inline">
                                <input type="radio" name="file_type" value="0" checked> EXCEL
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="file_type" value="1"> TXT
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" id="export">确定</button>
                <button type="button" class="btn btn-light closebtn" data-dismiss="modal">关闭</button>

            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
{/block}
{block name="js"}

<script>


    function del(obj, id)
    {
        $.confirm({
            title: '确定删除吗？',
            content: '删除的商品将进入回收站！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('goods/del')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("删除成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }

    function forceasync(obj, id)
    {
        $.confirm({
            title: '同步提示',
            content: '确定要下级强制同步该商品标题、商品说明、使用说明吗？',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('goods/forceasync')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("操作成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }

    function change_status(id, status)
    {
        $.post("{:url('goods/changeStatus')}", {
            id: id,
            status: status
        }, function (res) {
            location.reload();
        });
    }


    $(document).ready(function () {
        $("input[name='range']").change(function () {
            var selectedvalue = $("input[name='range']:checked").val();
            if (selectedvalue == 1) {
                $('#exportNUm').show();
            } else {
                $('#exportNUm').hide();
            }
        });
        $('.dumpCard').click(function () {
            var id = $(this).data('id');
            $('#goods_id').val(id);
            var goodsname = $(this).data('goodsname');
            $('#goodsname').html(goodsname);
            $('#exportCard').modal('show')
        });
        $('#export').click(function () {
            var range = $("input[name='range']");
            var number = $("input[name='number']");
            if (range == 1 && !number) {
                swal('请输入导出数量', '', "error");
                return false;
            }
            $('#export_form').submit();
        });

        $(".modal-dialog .close").click(function () {
            $(this).parents(".modal").click();
        });
        $(".modal-dialog .closebtn").click(function () {
            $(this).parents(".modal").click();
        });

    });

</script>
{/block}


