URI.Munge
TYPE: string/null
VERSION: 1.3.0
DEFAULT: NULL
--DESCRIPTION--

<p>
    Munges all browsable (usually http, https and ftp)
    absolute URIs into another URI, usually a URI redirection service.
    This directive accepts a URI, formatted with a <code>%s</code> where
    the url-encoded original URI should be inserted (sample:
    <code>http://www.google.com/url?q=%s</code>).
</p>
<p>
    Uses for this directive:
</p>
<ul>
    <li>
        Prevent PageRank leaks, while being fairly transparent
        to users (you may also want to add some client side JavaScript to
        override the text in the statusbar). <strong>Notice</strong>:
        Many security experts believe that this form of protection does not deter spam-bots.
    </li>
    <li>
        Redirect users to a splash page telling them they are leaving your
        website. While this is poor usability practice, it is often mandated
        in corporate environments.
    </li>
</ul>
<p>
    Prior to HTML Purifier 3.1.1, this directive also enabled the munging
    of browsable external resources, which could break things if your redirection
    script was a splash page or used <code>meta</code> tags. To revert to
    previous behavior, please use %URI.MungeResources.
</p>
<p>
    You may want to also use %URI.MungeSecretKey along with this directive
    in order to enforce what URIs your redirector script allows. Open
    redirector scripts can be a security risk and negatively affect the
    reputation of your domain name.
</p>
<p>
    Starting with HTML Purifier 3.1.1, there is also these substitutions:
</p>
<table>
    <thead>
        <tr>
            <th>Key</th>
            <th>Description</th>
            <th>Example <code>&lt;a href=""&gt;</code></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>%r</td>
            <td>1 - The URI embeds a resource<br />(blank) - The URI is merely a link</td>
            <td></td>
        </tr>
        <tr>
            <td>%n</td>
            <td>The name of the tag this URI came from</td>
            <td>a</td>
        </tr>
        <tr>
            <td>%m</td>
            <td>The name of the attribute this URI came from</td>
            <td>href</td>
        </tr>
        <tr>
            <td>%p</td>
            <td>The name of the CSS property this URI came from, or blank if irrelevant</td>
            <td></td>
        </tr>
    </tbody>
</table>
<p>
    Admittedly, these letters are somewhat arbitrary; the only stipulation
    was that they couldn't be a through f. r is for resource (I would have preferred
    e, but you take what you can get), n is for name, m
    was picked because it came after n (and I couldn't use a), p is for
    property.
</p>
--# vim: et sw=4 sts=4
