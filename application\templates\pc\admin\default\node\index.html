{extend name='./content'}

{block name="content"}
<table class="table table-hover">
    <thead>
    <tr>
        <th style="width:20px"></th>
        <th class='text-left'>系统节点结构</th>
        <th class='text-left'></th>
        <th>&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    {foreach $nodes as $key=>$vo}
    <tr>
        <td style="width:20px"></td>
        <td class='text-left nowrap'>
            {$vo.spl}{$vo.node}
            {if auth("$classuri/save")}
            &nbsp;<input class='layui-input layui-input-inline title-input' name='title' data-node="{$vo.node}"
                         value="{$vo.title}" style='height:28px;line-height:28px;width:auto'/>
            {/if}
        </td>
        <td class='text-left nowrap'>
            {if auth("$classuri/save")}
            <label data-tips-text="勾选后需要登录后才能访问">
                {if substr_count($vo['node'],'/')==2}
                {notempty name='vo.is_login'}
                <input checked='checked' class="check-box login_{$key}"
                       type='checkbox' value='1' name='is_login' data-node="{$vo.node}"
                       onclick="!this.checked&&($('.auth_{$key}')[0].checked=!!this.checked)"/>
                {else}
                <input class="check-box login_{$key}" type='checkbox' value='1' name='is_login' data-node="{$vo.node}"
                       onclick="!this.checked&&($('.auth_{$key}')[0].checked=!!this.checked)"/>
                {/notempty}
                加入登录控制
                {/if}
            </label>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <label data-tips-text="勾选后需配置用户权限后才能访问">
                {if substr_count($vo['node'],'/')==2}
                {notempty name='vo.is_auth'}
                <input name='is_auth' data-node="{$vo.node}" checked='checked' class="check-box auth_{$key}"
                       type='checkbox' onclick="this.checked&&($('.login_{$key}')[0].checked=!!this.checked)"
                       value='1'/>
                {else}
                <input name='is_auth' data-node="{$vo.node}" class="check-box auth_{$key}" type='checkbox' value='1'
                       onclick="this.checked&&($('.login_{$key}')[0].checked=!!this.checked)"
                />
                {/notempty}
                加入权限控制
                {/if}
            </label>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <label data-tips-text="勾选后配置菜单时节点可自动选择">
                {if substr_count($vo['node'],'/')==2}
                {notempty name='vo.is_menu'}
                <input name='is_menu' data-node="{$vo.node}" checked='checked' class='check-box menu_{$key}'
                       type='checkbox' value='1'/>
                {else}
                <input name='is_menu' data-node="{$vo.node}" class='check-box menu_{$key}' type='checkbox' value='1'/>
                {/notempty}
                加入菜单节点选择器
                {/if}
            </label>
            {/if}
        </td>
        <td style="width:100%"></td>
    </tr>
    {/foreach}
    </tbody>
</table>
{if auth("$classuri/save")}
<script>
    $(function () {
        $('input.title-input').on('blur', function () {
            var data = {list: [{name: this.name, value: this.value, node: this.getAttribute('data-node')}]};
            $.form.load('{:url("save")}', data, 'POST', function (ret) {
                if (ret.code === 0) {
                    setTimeout(function () {
                        $.form.reload();
                    }, 3000);
                    $.msg.auto(ret);
                }
                return false;
            });
        });
        $('input.check-box').on('click', function () {
            var data = {list: []};
            $(this).parent().parent().find('input').map(function () {
                data.list.push({name: this.name, value: this.checked ? 1 : 0, node: this.getAttribute('data-node')});
            });
            $.form.load('{:url("save")}', data, 'POST', function (ret) {
                if (ret.code === 0) {
                    setTimeout(function () {
                        $.form.reload();
                    }, 3000);
                    $.msg.auto(ret);
                }
                return false;
            });
        });
    });
</script>
{/if}
{/block}