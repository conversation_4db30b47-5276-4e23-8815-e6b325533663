#clicaptcha-container{
    display: none;
    width: 350px;
    height: 290px;
    padding: 15px;
    border: 1px solid #b1b3b8;
    background-color: #f5f6f7;
    position: fixed;
    z-index: 10000;
    left: 50%;
    top: 50%;
    margin-left: -191px;
    margin-top: -161px;
    border-radius: 10px;
    box-shadow: 0 0 0 1px hsla(0,0%,100%,.3) inset, 0 .5em 1em rgba(0, 0, 0, 0.6);
    box-sizing:content-box;
}
#clicaptcha-container .clicaptcha-imgbox{
    position: relative;
}
#clicaptcha-container .clicaptcha-imgbox .step{
    position: absolute;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    color: #f04848;
    border: 3px solid #f04848;
    background-color: #fff;
    border-radius: 30px;
    box-shadow: 0 0 10px #fff;
    -webkit-user-select: none;
    user-select: none;
}
#clicaptcha-container .clicaptcha-img{
    width: 350px;
    height: 200px;
    border: none;
}
#clicaptcha-container .clicaptcha-title{
    font-family: 'Microsoft YaHei';
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    color: #333;
}
#clicaptcha-container .clicaptcha-title span{
    font-size: 16px;
    font-weight: bold;
    color: #c00;
}
#clicaptcha-container .clicaptcha-title span.clicaptcha-clicked{
    color: #069;
}
#clicaptcha-container .clicaptcha-refresh-box{
    position: relative;
    margin-top: 10px;
}
#clicaptcha-container .clicaptcha-refresh-line{
    position: absolute;
    top: 16px;
    width: 140px;
    height: 1px;
    background-color: #ccc;
}
#clicaptcha-container .clicaptcha-refresh-line-left{
    left: 5px;
}
#clicaptcha-container .clicaptcha-refresh-line-right{
    right: 5px;
}
#clicaptcha-container .clicaptcha-refresh-btn{
    display: block;
    margin: 0 auto;
    width: 32px;
    height: 32px;
    background: url(../image/refresh.png) no-repeat;
}
#clicaptcha-container .clicaptcha-refresh-btn:hover{
    background-position:-32px 0;
}
#clicaptcha-mask{
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background-color: rgb(0, 0, 0);
}
