{extend name="base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body  p-3">
            <!-- Search Form Wrapper-->
            <div class="search-form-wrapper">
                <div class="row">
                    <div class="col-12">
                        <a href="{:url('cash/apply')}" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                            <i class="bx bx-plus me-1"></i>申请结算
                        </a>
                    </div>
                </div>
            </div>

            <div class="divider border-light"></div>
            {foreach $cashs as $v}
            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">提现时间:&nbsp;&nbsp;{$v.create_at|date="Y-m-d H:i:s",###}</h6>
                    {switch name="v.status"}
                    {case value="0"}<span class="badge rounded-pill bg-light float-end">审核中</span>{/case}
                    {case value="1"}<span class="badge rounded-pill bg-success float-end">提现成功</span>{/case}
                    {case value="2"}<span class="badge rounded-pill bg-danger float-end">审核未通过</span>{/case}
                    {/switch}
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>提现金额：</span>
                        <span>{$v.money}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>手续费：</span>
                        <span>{$v.fee}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>实际到账：</span>
                        <span>{$v.actual_money}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>结算时间：</span>
                        <span>{if $v.status==1}{$v.complete_at|date="Y-m-d H:i:s",###}{/if}</span>
                    </div>
                </div>

            </div>
            {/foreach}       

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}

{/block}


