<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
    <meta name="description" content="{:sysconf('site_desc')}">
    <meta name="keywords" content="{:sysconf('site_keywords')}">
    <link rel="shortcut icon" href="{:sysconf('browser_icon')}">
    
    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="__RES__/theme/modern/css/modern.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js">
    
    <style>
        /* Custom styles for this page */
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stats-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-icon-bg {
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="navbar-brand">
                    <img src="{:sysconf('site_logo')}" alt="{:sysconf('site_name')}" class="h-8 w-auto">
                </div>
                
                <!-- Desktop Navigation -->
                <ul class="navbar-nav hidden md:flex">
                    <li><a href="/" class="nav-link active">首页</a></li>
                    <li><a href="/orderquery" class="nav-link">卡密查询</a></li>
                    <li><a href="/complaint" class="nav-link">订单投诉</a></li>
                    <li><a href="/complaintquery" class="nav-link">投诉进度</a></li>
                    <li><a href="/company/contact" class="nav-link">联系我们</a></li>
                    <li><a href="/company/faq" class="nav-link">帮助中心</a></li>
                </ul>
                
                <!-- CTA Button -->
                <div class="hidden md:block">
                    <a href="/login" class="btn btn-primary">商家登录</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="nav-toggle md:hidden">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <div class="nav-menu md:hidden">
                <ul class="flex flex-col gap-4 py-4">
                    <li><a href="/" class="nav-link">首页</a></li>
                    <li><a href="/orderquery" class="nav-link">卡密查询</a></li>
                    <li><a href="/complaint" class="nav-link">订单投诉</a></li>
                    <li><a href="/complaintquery" class="nav-link">投诉进度</a></li>
                    <li><a href="/company/contact" class="nav-link">联系我们</a></li>
                    <li><a href="/company/faq" class="nav-link">帮助中心</a></li>
                    <li><a href="/login" class="btn btn-primary w-full">商家登录</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero hero-gradient py-20">
        <div class="container">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="hero-content">
                    <h1 class="hero-title text-white mb-6">
                        快捷下单，自动发卡
                    </h1>
                    <p class="hero-subtitle text-white/90 mb-8">
                        持续更新的虚拟商品寄售平台，独家支持QQ/微信集成登录，7X24稳定运营
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="/register" class="btn btn-lg bg-white text-primary-600 hover:bg-gray-100">
                            立即入驻，成为商户
                        </a>
                        <a href="/orderquery" class="btn btn-lg btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                            查询订单
                        </a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="relative">
                        <img src="{:sysconf('site_logo')}" alt="平台展示" class="w-full max-w-md mx-auto">
                        <!-- Floating Stats Cards -->
                        <div class="absolute top-4 right-4 stats-card rounded-xl p-4 text-white">
                            <div class="text-2xl font-bold" data-counter="151825">0</div>
                            <div class="text-sm opacity-80">成功寄售</div>
                        </div>
                        <div class="absolute bottom-4 left-4 stats-card rounded-xl p-4 text-white">
                            <div class="text-2xl font-bold" data-counter="8111">0</div>
                            <div class="text-sm opacity-80">服务商户</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    实体公司运营 资金安全有保障
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    {:sysconf('site_name')}交易平台，由重庆市政府采购中心签约软件服务供应商美拓科技研发与运营
                </p>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon feature-icon-bg">
                        <i data-lucide="shield-check"></i>
                    </div>
                    <h3 class="feature-title">高防服务器</h3>
                    <p class="feature-description">安全稳定的高防服务器，保障平台7x24小时稳定运行</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon feature-icon-bg">
                        <i data-lucide="banknote"></i>
                    </div>
                    <h3 class="feature-title">次日结账</h3>
                    <p class="feature-description">满100元自动提现，资金安全有保障，多数用户的信赖之选</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon feature-icon-bg">
                        <i data-lucide="headphones"></i>
                    </div>
                    <h3 class="feature-title">全天候服务</h3>
                    <p class="feature-description">10秒响应服务，专业客服团队为您提供优质服务</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon feature-icon-bg">
                        <i data-lucide="award"></i>
                    </div>
                    <h3 class="feature-title">证照齐全</h3>
                    <p class="feature-description">正规企业，拥有ICP增值电信许可证，合规经营</p>
                </div>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <section class="py-20 bg-gray-50">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">最新公告</h2>
                <p class="text-lg text-gray-600">了解平台最新动态和重要通知</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- System Announcements -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">系统公告</h3>
                    </div>
                    <div class="card-body">
                        {foreach $announceList as $v}
                        <div class="mb-4 last:mb-0">
                            <a href="/article/{$v.id}.html" class="block hover:text-primary-600 transition-colors">
                                <h4 class="font-medium text-gray-900 mb-1">{$v.title}</h4>
                                <p class="text-sm text-gray-600 line-clamp-2">{$v.content|htmlspecialchars_decode|removeXSS}</p>
                            </a>
                        </div>
                        {/foreach}
                    </div>
                </div>
                
                <!-- Settlement Notices -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">结算公告</h3>
                    </div>
                    <div class="card-body">
                        {foreach $withdrawNotice as $v}
                        <div class="mb-4 last:mb-0">
                            <a href="/article/{$v.id}.html" class="block hover:text-primary-600 transition-colors">
                                <h4 class="font-medium text-gray-900 mb-1">{$v.title}</h4>
                                <p class="text-sm text-gray-600 line-clamp-2">{$v.content|htmlspecialchars_decode|removeXSS}</p>
                            </a>
                        </div>
                        {/foreach}
                    </div>
                </div>
                
                <!-- Industry News -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">行业新闻</h3>
                    </div>
                    <div class="card-body">
                        {foreach $newsList as $v}
                        <div class="mb-4 last:mb-0">
                            <a href="/article/{$v.id}.html" class="block hover:text-primary-600 transition-colors">
                                <h4 class="font-medium text-gray-900 mb-1">{$v.title}</h4>
                                <p class="text-sm text-gray-600 line-clamp-2">{$v.content|htmlspecialchars_decode|removeXSS}</p>
                            </a>
                        </div>
                        {/foreach}
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <a href="/company/faq" class="btn btn-outline">查看更多</a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-primary-600">
        <div class="container text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                入驻我们 / 即刻赚钱
            </h2>
            <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                为商品寄售量身打造的服务平台，24小时监控订单，资金无忧
            </p>
            <a href="/register" class="btn btn-lg bg-white text-primary-600 hover:bg-gray-100">
                立即入驻，成为商户
            </a>
        </div>
    </section>

    <!-- Footer -->
    {include file="./default_footer"}

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script src="__RES__/theme/modern/js/modern.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
