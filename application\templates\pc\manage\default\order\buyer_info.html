<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">IP地址</label>
        <div class="layui-input-block">
            <input type="text" value="{$order.create_ip}" class="layui-input" readonly>
        </div>

    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">设备指纹</label>
        <div class="layui-input-block">
            <input type="text" value="{$order.fingerprint}" class="layui-input" readonly>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">微信openid</label>
        <div class="layui-input-block">
            <input type="text" value="{$order.openid}" class="layui-input" readonly>
        </div>

    </div>
    <p style='text-align: center;'> 
        <button type="button" class="layui-btn layui-btn-small layui-btn-danger" onclick="del('{$order.id}')">一键拉黑</button>
    </p>

</form>
<script>

    function del(id)
    {
        layer.confirm('是否确认拉黑买家？', function (index) {
            var loading = '';
            $.ajax({
                url: "{:url('buyerToBlack')}",
                data: {
                    id: id,
                },
                type: 'post',
                dataType: 'json',
                beforeSend: function () {
                    loading = layer.load();
                },
                success: function (res) {
                    layer.close(loading);
                    if (res.code == 1) {
                        layer.msg(res.msg, {icon: 1, time: 1000});
                        location.reload();
                    } else {
                        layer.msg(res.msg, {time: 2000, icon: 'error'});
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    layer.close(loading);
                    layer.msg('连接错误');
                }
            });
        });

    }

</script>
