<?php

/**
 * 缩我短网址suo.im
 */

namespace app\common\util\dwz;

use app\common\util\DWZ;
use service\HttpService;

class Suo extends DWZ {

    const API_URL = 'http://api.suolink.cn/api.htm ';

    protected $key;
    protected $domain;

    public function __construct() {
        $this->key = sysconf('suo_app_key');
        $this->domain = sysconf('suo_domain');
    }

    public function create($url) {
        $res = HttpService::get(SELF::API_URL, [
                    'url' => $url,
                    'format' => 'json',
                    'key' => $this->key,
                    'expireDate' => date('Y-m-d', strtotime('+10 year')),
                    'domain' => $this->domain,
        ]);
        if ($res === false) {
            record_system_log("缩我短链接生成失败：" . $res);
            return false;
        }
        $json = json_decode($res);
        if (!$json) {
            record_system_log("缩我短链接生成失败：" . $res);
            return false;
        }
        if ($json->err != '') {
            record_system_log("缩我短链接生成失败：" . $res);
            return false;
        }
        return $json->url;
    }

}
