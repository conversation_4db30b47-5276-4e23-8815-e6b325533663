{"name": "endroid/qrcode", "description": "Endroid QR Code", "keywords": ["endroid", "qrcode", "qr", "code", "bundle", "symfony"], "homepage": "https://github.com/endroid/QrCode", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://endroid.nl/"}], "require": {"php": ">=5.4", "ext-gd": "*", "symfony/options-resolver": "^2.3|^3.0"}, "require-dev": {"symfony/browser-kit": "^2.3|^3.0", "symfony/framework-bundle": "^2.3|^3.0", "symfony/http-kernel": "^2.3|^3.0", "sensio/framework-extra-bundle": "^3.0", "phpunit/phpunit": "^4.0|^5.0"}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "autoload-dev": {"psr-4": {"Endroid\\QrCode\\Tests\\": "tests/"}}, "config": {"bin-dir": "bin"}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}