<!-- Modern Header Component -->
<header class="navbar" id="navbar">
    <div class="container">
        <div class="flex items-center justify-between py-4">
            <!-- Logo -->
            <div class="navbar-brand">
                <a href="/" class="flex items-center">
                    <img src="{:sysconf('site_logo')}" alt="{:sysconf('site_name')}" class="h-8 w-auto">
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <nav class="hidden lg:block">
                <ul class="navbar-nav">
                    <li><a href="/" class="nav-link">首页</a></li>
                    <li><a href="/orderquery" class="nav-link">卡密查询</a></li>
                    <li><a href="/complaint" class="nav-link">订单投诉</a></li>
                    <li><a href="/complaintquery" class="nav-link">投诉进度</a></li>
                    <li><a href="/company/contact" class="nav-link">联系我们</a></li>
                    <li><a href="/company/faq" class="nav-link">帮助中心</a></li>
                </ul>
            </nav>
            
            <!-- Right Side Actions -->
            <div class="flex items-center gap-4">
                <!-- Search Button -->
                <button class="p-2 text-gray-600 hover:text-primary-600 transition-colors" 
                        data-modal-target="searchModal" 
                        data-tooltip="搜索">
                    <i data-lucide="search" class="w-5 h-5"></i>
                </button>
                
                <!-- Theme Toggle -->
                <button class="theme-toggle p-2 text-gray-600 hover:text-primary-600 transition-colors" 
                        data-tooltip="切换主题">
                    <i data-lucide="sun" class="w-5 h-5"></i>
                </button>
                
                <!-- Login Button -->
                <a href="/login" class="btn btn-primary hidden lg:inline-flex">
                    <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                    商家登录
                </a>
                
                <!-- Mobile Menu Button -->
                <button class="nav-toggle lg:hidden p-2" aria-label="打开菜单">
                    <span class="block w-6 h-0.5 bg-gray-600 transition-all"></span>
                    <span class="block w-6 h-0.5 bg-gray-600 mt-1 transition-all"></span>
                    <span class="block w-6 h-0.5 bg-gray-600 mt-1 transition-all"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="nav-menu lg:hidden">
            <div class="border-t border-gray-200 py-4">
                <ul class="flex flex-col gap-2">
                    <li><a href="/" class="nav-link block py-2">首页</a></li>
                    <li><a href="/orderquery" class="nav-link block py-2">卡密查询</a></li>
                    <li><a href="/complaint" class="nav-link block py-2">订单投诉</a></li>
                    <li><a href="/complaintquery" class="nav-link block py-2">投诉进度</a></li>
                    <li><a href="/company/contact" class="nav-link block py-2">联系我们</a></li>
                    <li><a href="/company/faq" class="nav-link block py-2">帮助中心</a></li>
                    <li class="pt-4 border-t border-gray-200 mt-4">
                        <a href="/login" class="btn btn-primary w-full">
                            <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                            商家登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</header>

<!-- Search Modal -->
<div id="searchModal" class="modal fixed inset-0 z-50 hidden">
    <div class="modal-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
    <div class="modal-content fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-lg mx-4">
        <div class="bg-white rounded-xl shadow-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">搜索</h3>
                <button data-modal-close class="p-1 text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            <form action="/search" method="GET" class="space-y-4">
                <div class="form-group">
                    <input type="text" 
                           name="q" 
                           class="form-input" 
                           placeholder="输入搜索关键词..."
                           autofocus>
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary flex-1">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                        搜索
                    </button>
                    <button type="button" data-modal-close class="btn btn-secondary">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Header Styles */
.navbar {
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.navbar.nav-hidden {
    transform: translateY(-100%);
}

.nav-toggle {
    position: relative;
    z-index: 60;
}

.nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active span:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid var(--gray-200);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.nav-menu.active {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal Styles */
.modal {
    transition: all 0.3s ease;
}

.modal.active {
    display: flex !important;
    animation: modalFadeIn 0.3s ease-out;
}

.modal.active .modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    z-index: 1000;
    padding: 8px 12px;
    background-color: var(--gray-900);
    color: white;
    font-size: 0.75rem;
    border-radius: 6px;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease-out forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode styles */
.dark .navbar {
    background-color: rgba(31, 41, 55, 0.95);
    border-color: var(--gray-700);
}

.dark .nav-menu {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
}

.dark .modal-content {
    background-color: var(--gray-800);
}
</style>
