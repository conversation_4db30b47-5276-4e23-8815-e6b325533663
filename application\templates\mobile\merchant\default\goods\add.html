{extend name="simple_base"}
{block name="css"}
<link href="__RES__/merchant/default/libs/summernote/summernote-bs4.min.css" rel="stylesheet" type="text/css">
<style>
    .note-toolbar {
        background: hsla(0, 0%, 50.2%, 0.03);
    }
    .note-editor.card{
        border:1px solid rgba(0,0,0,.2) !important;
    }
</style>
{/block}


{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body  p-3">


            <form id="form1" class="form-horizontal" role="form" action="" method="post">
                <input type="hidden" name="id" value="{$goods.id|default=''|htmlentities}">
                <div class="form-group row">
                    <label for="cate_id" class="col-md-2 form-label">商品分类</label>
                    <div class="col-md-4">
                        <select id="cate_id" name="cate_id" class="form-select" required>
                            {foreach $categorys as $v}
                            <option value="{$v.id|htmlentities}" {if isset($goods) && $goods.cate_id==$v.id}selected{/if}>{$v.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label  class="form-label" for="theme" class="col-md-2 form-label">页面风格</label>
                    <div class="col-md-4">
                        <select id="theme" name="theme" class="form-select" required>
                            {foreach :config('pay_themes') as $theme}
                            <option value="{$theme.alias|htmlentities}" {if isset($goods) && $goods.theme==$theme.alias}selected{/if}>{$theme.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="name" class="col-md-2 form-label">商品名称</label>
                    <div class="col-md-4">
                        <input id="name" name="name" type="text" class="form-control" placeholder="商品名称" value="{$goods.name|default=''|htmlentities}">
                    </div>
                </div>
                <div class="form-group row">
                    <label for="price" class="col-md-2 form-label">商品价格</label>
                    <div class="col-md-4">
                        <input id="price" name="price" type="text" class="form-control" placeholder="商品价格" value="{$goods.price|default=''|htmlentities}">
                    </div>
                </div>

                <div class="form-group row">
                    <label for="can_proxy" class="col-md-2 form-label">是否开启代理</label>
                    <div class="col-md-4 d-flex align-items-center">

                        <div class="form-check">
                            <input value="0" type="radio" id="can_proxy0" name="can_proxy" class="form-check-input mt-0" {if isset($goods)}{if $goods.can_proxy==0}checked{/if}{else/}checked{/if}>
                                   <label class="form-label mb-0 " for="can_proxy0">关闭</label>
                        </div>

                        <div class="form-check ms-3">
                            <input  value="1" type="radio" id="can_proxy2" name="can_proxy" class="form-check-input mt-0" {if isset($goods) && $goods.can_proxy==1}checked{/if}>
                                    <label class="form-label mb-0 " for="can_proxy2">开启</label>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-center mt-2">
                        <p class="text-muted mb-0 ">代理商品通过密钥或授权码完成对接。</p>
                    </div>
                </div>

                <div class="form-group row block-agent_type" {if !isset($goods) || $goods.can_proxy==0}style="display:none"{/if}>
                     <label for="proxy_price" class="col-md-2 form-label">代理成本价</label>
                    <div class="col-md-4">
                        <input id="proxy_price" name="proxy_price" type="text" class="form-control"  value="{$goods.proxy_price|default=''|htmlentities}" autocomplete="off">
                    </div>
                    <div class="col-md-6 d-flex align-items-center mt-2">
                        <p class="text-muted mb-0 ">代理成本价，修改会直接影响商家代理商品的价格，如5元改为6元，则代理该商品的所有代理的终端售价均增加1元。</p>
                    </div>
                </div>
                <div class="form-group row block-agent_type" {if !isset($goods) || $goods.can_proxy==0}style="display:none"{/if}>
                     <label for="proxy_price_add" class="col-md-2 form-label">代理最低加价</label>
                    <div class="col-md-4">
                        <input id="proxy_price_add" name="proxy_price_add" type="text" class="form-control"  value="{$goods.proxy_price_add|default=''|htmlentities}" autocomplete="off">
                    </div>
                    <div class="col-md-6 d-flex align-items-center mt-2">
                        <p class="text-muted mb-0 ">若设置为1, 代理则最低加价1块钱出售（控价）</p>
                    </div>
                </div>

                <div class="form-group row block-agent_type" {if !isset($goods) || $goods.can_proxy==0}style="display:none"{/if}>
                     <label for="proxy_pool_id" class="col-md-2 form-label">所属对接商品池</label>
                    <div class="col-md-4">
                        <select id="proxy_pool_id" name="proxy_pool_id" class="form-select" required>
                            {foreach $goods_pool as $v}
                            <option value="{$v.id|htmlentities}" {if isset($goods) && $goods.proxy_pool_id==$v.id}selected{/if}>{$v.title}</option>
                            {/foreach}
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-center mt-2">
                        <p class="text-muted mb-0 ">若没有商品池，请提前创建</p>
                    </div>
                </div>

                <div class="form-group row block_wholesale_discount">
                    <label for="wholesale_discount" class="col-md-2 form-label">批发优惠</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="0" type="radio" id="wholesale_discount2" name="wholesale_discount" class="form-check-input mt-0"  {if isset($goods)}{if $goods.wholesale_discount==0}checked{/if}{else/}checked{/if}>
                                    <label class="form-label mb-0 " for="wholesale_discount2">不使用</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="1" type="radio" id="wholesale_discount1" name="wholesale_discount" class="form-check-input mt-0" {if isset($goods) && $goods.wholesale_discount==1}checked{/if}>
                                   <label class="form-label mb-0 " for="wholesale_discount1">使用</label>
                        </div>
                    </div>

                </div>
                <div class="form-group row block-wholesale_discount" {if !isset($goods) || $goods.wholesale_discount==0}style="display:none"{/if}>
                     <label for="block-discount_list" class="col-md-2 form-label"></label>
                    <div class="col-md-4">
                        <div class="block-discount_list" id="block-discount_list">
                            {if isset($goods)}
                            {foreach $goods.wholesale_discount_list as $v}
                            <div class="form-group row discount_item">
                                <div class="col-md-3">
                                    <input name="wholesale_discount_list[num][]" type="number" class="form-control" value="{$v.num|htmlentities}" min=0>
                                </div>
                                <div class="col-md-1">
                                    张
                                </div>
                                <div class="col-md-3">
                                    <input name="wholesale_discount_list[price][]" type="text" class="form-control" value="{$v.price|htmlentities}">
                                </div>
                                <div class="col-md-1">
                                    元
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-danger waves-effect waves-light btn-sm" onclick="del_discount_item(this)">删除</button>
                                </div>
                            </div>
                            {/foreach}
                            {/if}
                        </div>
                        <button type="button" class="btn btn-primary waves-effect waves-light btn-sm" onclick="add_discount_item()"><i class="bx bx-plus-circle"></i> 添加优惠</button>
                    </div>
                </div>


                <div class="form-group row block_send_gift">
                    <label for="send_gift" class="col-md-2 form-label">添加赠送</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="0" type="radio" id="send_gift0" name="send_gift" class="form-check-input mt-0"  {if isset($goods)}{if $goods.send_gift==0}checked{/if}{else/}checked{/if}>
                                    <label class="form-label mb-0 " for="send_gift0">不使用</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="1" type="radio" id="send_gift1" name="send_gift" class="form-check-input mt-0" {if isset($goods) && $goods.send_gift==1}checked{/if}>
                                   <label class="form-label mb-0 " for="send_gift1">叠加赠送</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="2" type="radio" id="send_gift2" name="send_gift" class="form-check-input mt-0" {if isset($goods) && $goods.send_gift==2}checked{/if}>
                                   <label class="form-label mb-0 " for="send_gift2">不叠加赠送</label>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-center">
                        <p class="text-muted mb-0">叠加10张送两张，20张送四张,叠加赠送只能设置一个规则</p>
                    </div>
                </div>
                <div class="form-group row block-send_gift" {if !isset($goods) || $goods.send_gift==0}style="display:none"{/if}>
                     <label for="block-gift_list" class="col-md-2 form-label"></label>
                    <div class="col-md-4">
                        <div class="block-gift_list" id="block-gift_list">
                            {if isset($goods)}
                            {foreach $goods.send_gift_list as $v}
                            <div class="form-group row gift_item d-flex align-items-center">
                                <div class="col-3">
                                    <input name="send_gift_list[limit][]" type="number" class="form-control" value="{$v.limit|htmlentities}" min=0>
                                </div>
                                <div class="col-3">
                                    买满/张
                                </div>
                                <div class="col-3">
                                    <input name="send_gift_list[num][]" type="text" class="form-control" value="{$v.num|htmlentities}">
                                </div>
                                <div class="col-3">
                                    赠/张
                                </div>
                                <div class="col-3">
                                    <button type="button" class="btn btn-danger waves-effect waves-light btn-sm" onclick="del_gift_item(this)">删除</button>
                                </div>
                            </div>
                            {/foreach}
                            {/if}
                        </div>
                        <button type="button" class="btn btn-primary waves-effect waves-light btn-sm" onclick="add_gift_item()"><i class="bx bx-plus-circle"></i> 添加赠送</button>
                    </div>


                </div>


                <div class="form-group row">
                    <label for="sms_payer" class="col-md-2 form-label">短信费用</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="0" type="radio" id="sms_payer1" name="sms_payer" class="form-check-input mt-0"  {if isset($goods)}{if $goods.sms_payer==0}checked{/if}{else/}checked{/if}>
                                    <label class="form-label mb-0 " for="sms_payer1">买家承担</label>
                        </div>
                        <div class="form-check  ms-3">
                            <input value="1" type="radio" id="sms_payer2" name="sms_payer" class="form-check-input mt-0" {if isset($goods) && $goods.sms_payer==1}checked{/if}>
                                   <label class="form-label mb-0 " for="sms_payer2">商户承担</label>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="cost_price" class="col-md-2 form-label">成本价</label>
                    <div class="col-md-4">
                        <input id="cost_price" name="cost_price" type="text" class="form-control" placeholder="成本价" value="{$goods.cost_price|default='0'|htmlentities}">
                    </div>
                </div>

                <div class="form-group row">
                    <label for="limit_quantity" class="col-md-2 form-label">起购数量</label>
                    <div class="col-md-4">
                        <input id="limit_quantity" name="limit_quantity" type="number" class="form-control" placeholder="起购数量" value="{$goods.limit_quantity|default=1|htmlentities}" min=1>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="max_quantity" class="col-md-2 form-label">限购数量</label>
                    <div class="col-md-4">
                        <input id="max_quantity" name="max_quantity" type="number" class="form-control" placeholder="限购数量，0不限制" value="{$goods.max_quantity|default=0|htmlentities}" >
                    </div>
                </div>

                <div class="form-group row">
                    <label for="inventory_notify" class="col-md-2 form-label">库存预警</label>
                    <div class="col-md-4">
                        <input id="inventory_notify" name="inventory_notify" type="number" class="form-control" placeholder="库存预警" value="{$goods.inventory_notify|default=0|htmlentities}" min=0>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="card_order" class="col-md-2 form-label">售卡顺序</label>

                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="0" type="radio" id="card_order1" name="card_order" class="form-check-input mt-0" {if isset($goods)}{if $goods.card_order==0}checked{/if}{else/}checked{/if}>
                                    <label class="form-label mb-0 " for="card_order1">先售老卡</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="1" type="radio" id="card_order2" name="card_order" class="form-check-input mt-0"  {if isset($goods) && $goods.card_order==1}checked{/if}>
                                   <label class="form-label mb-0 " for="card_order2">先售新卡</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="2" type="radio" id="card_order3" name="card_order" class="form-check-input mt-0"  {if isset($goods) && $goods.card_order==2}checked{/if}>
                                   <label class="form-label mb-0 " for="card_order3">随机售卡</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="3" type="radio" id="card_order4" name="card_order" class="form-check-input mt-0"  {if isset($goods) && $goods.card_order==3}checked{/if}>
                                   <label class="form-label mb-0 " for="card_order4"><b>自助选号</b></label>
                        </div>
                    </div>

                </div>


                <div class="form-group row block_selectcard_fee"  {if !isset($goods) || $goods.card_order!=3}style="display:none"{/if}>
                     <label for="selectcard_fee" class="col-md-2 form-label">自助选号费</label>
                    <div class="col-md-4">
                        <input id="limit_quantity" name="selectcard_fee" type="text" class="form-control" placeholder="选号费" value="{$goods.selectcard_fee|default=0|htmlentities}">
                    </div>
                    <div class="col-md-6 d-flex align-items-center mt-2">
                        <p class="text-muted mb-0 ">自助选号功能额外支出的费用，系统限制最低{:plugconf('selectcard', 'selectcard_fee_min')}元，平台需分成{:plugconf('selectcard', 'selectcard_fee_platform_rate')}%</p>
                    </div>
                </div>


                <div class="form-group row block_coupon_type" >
                    <label for="coupon_type" class="col-md-2 form-label">优惠券</label>

                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="1" type="radio" id="coupon_type1" name="coupon_type" class="form-check-input mt-0"  {if isset($goods) && $goods.coupon_type==1}checked{/if}>
                                    <label class="form-label mb-0 " for="coupon_type1">支持</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="0" type="radio" id="coupon_type2" name="coupon_type" class="form-check-input mt-0"  {if isset($goods)}{if $goods.coupon_type==0}checked{/if}{else/}checked{/if}>
                                   <label class="form-label mb-0 " for="coupon_type2">不支持</label>
                        </div>
                    </div>

                </div>

                <div class="form-group row">
                    <label for="sold_notify" class="col-md-2 form-label">售出通知</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input value="0" type="radio" id="sold_notify2" name="sold_notify" class="form-check-input mt-0"  {if isset($goods)}{if $goods.sold_notify==0}checked{/if}{else/}checked{/if}>
                                   <label class="form-label mb-0 " for="sold_notify2">关闭</label>
                        </div>
                        <div class="form-check ms-3">
                            <input  value="1" type="radio" id="sold_notify1" name="sold_notify" class="form-check-input mt-0"  {if isset($goods) && $goods.sold_notify==1}checked{/if}>
                                    <label class="form-label mb-0 " for="sold_notify1">开启</label>
                        </div>
                    </div>

                </div>


                <div class="form-group row">
                    <label for="inventory_notify_type" class="col-md-2 form-label">通知方式</label>

                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input  value="1" type="radio" id="inventory_notify_type1" name="inventory_notify_type" class="form-check-input mt-0"  {if isset($goods)}{if $goods.inventory_notify_type==1}checked{/if}{else/}checked{/if}>
                                    <label class="form-label mb-0 " for="inventory_notify_type1">站内信</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="2" type="radio" id="inventory_notify_type2" name="inventory_notify_type" class="form-check-input mt-0"   {if isset($goods) && $goods.inventory_notify_type==2}checked{/if}>
                                   <label class="form-label mb-0 " for="inventory_notify_type2">邮件</label>
                        </div>
                    </div>

                </div>

                <div class="form-group row">
                    <label for="take_card_type" class="col-md-2 form-label">提卡密码</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input value="0" type="radio" id="take_card_type3" name="take_card_type" class="form-check-input mt-0"  {if isset($goods)}{if $goods.take_card_type==0}checked{/if}{else/}checked{/if}>
                                   <label class="form-label mb-0 " for="take_card_type3">关闭</label>
                        </div>
                        <div class="form-check ms-3">
                            <input value="2" type="radio" id="take_card_type2" name="take_card_type" class="form-check-input mt-0"  {if isset($goods) && $goods.take_card_type==2}checked{/if}>
                                   <label class="form-label mb-0 " for="take_card_type2">选填</label>
                        </div>
                        <div class="form-check ms-3">
                            <input  value="1" type="radio" id="take_card_type1" name="take_card_type" class="form-check-input mt-0"  {if isset($goods) && $goods.take_card_type==1}checked{/if}>
                                    <label class="form-label mb-0 " for="take_card_type1">必填</label>
                        </div>
                    </div>

                </div>

                <div class="form-group row">
                    <label for="visit_type" class="col-md-2 form-label">访问密码</label>
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="form-check">
                            <input value="0" type="radio" id="visit_type2" name="visit_type" class="form-check-input mt-0"  {if isset($goods)}{if $goods.visit_type==0}checked{/if}{else/}checked{/if}>
                                   <label class="form-label mb-0 " for="visit_type2">关闭</label>
                        </div>
                        <div class="form-check ms-3">
                            <input  value="1" type="radio" id="visit_type1" name="visit_type" class="form-check-input mt-0" {if isset($goods) && $goods.visit_type==1}checked{/if}>
                                    <label class="form-label mb-0 " for="visit_type1">开启</label>
                        </div>
                    </div>

                </div>

                <div class="form-group row block-visit_password" {if !isset($goods) || $goods.visit_type==0}style="display:none"{/if}>
                     <label for="visit_password" class="col-md-2 form-label">设置密码</label>
                    <div class="col-md-4">
                        <input id="visit_password" name="visit_password" type="text" class="form-control" placeholder="" value="{$goods.visit_password|default=''|htmlentities}">
                    </div>

                </div>
                <div class="form-group row">
                    <label for="contact_limit" class="col-md-2 form-label">客户信息</label>
                    <div class="col-md-4">
                        <select id="contact_limit" name="contact_limit" class="form-select" required>
                            <option value="default" {if isset($goods) && $goods.contact_limit==='default'}selected{/if}>默认</option>
                            <option value="any" {if isset($goods) && $goods.contact_limit==='any'}selected{/if}>任意字符</option>
                            <option value="qq" {if isset($goods) && $goods.contact_limit==='qq'}selected{/if}>QQ号码</option>
                            <option value="email" {if isset($goods) && $goods.contact_limit==='email'}selected{/if}>邮箱账号</option>
                            <option value="mobile" {if isset($goods) && $goods.contact_limit==='mobile'}selected{/if}>手机号码</option>
                        </select>
                    </div>

                </div>
                <div class="form-group row">
                    <label for="content" class="col-md-2 form-label">商品说明</label>
                    <div class="col-md-4">
                        <textarea class='d-none' id="content" name="content"></textarea>
                        <div id="summernote-content">{$goods.content|default=''|htmlspecialchars_decode|removeXSS}</div>
                    </div>

                </div>
                <div class="form-group row">
                    <label for="remark" class="col-md-2 form-label">使用说明</label>
                    <div class="col-md-4">
                        <textarea id="remark" name="remark" placeholder="建议填写该商品的使用方法，文字不超过200字" class="form-control" rows="5" maxlength="200">{$goods.remark|default=''}</textarea>
                    </div>

                </div>

                <div class="form-group row">
                    <label for="sort" class="col-md-2 form-label">商品排序</label>
                    <div class="col-md-4">
                        <input id="sort" name="sort" type="number" class="form-control" placeholder="商品排序" value="{$goods.sort|default=0|htmlentities}" min=0>
                    </div>

                </div>

                <div class="row">
                    <div class="col-md-12 text-center">
                        <button type="submit" class="btn btn-primary waves-effect waves-light btn-submit"><i class="bx bx-check-square mr-1"></i>确认提交</button>
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>

{/block}
{block name="js"}
<script src="__RES__/merchant/default/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="__RES__/merchant/default/libs/summernote/summernote-bs4.min.js"></script>
<script src="__RES__/merchant/default/libs/summernote/lang/summernote-zh-CN.js"></script>

<script>

                            $(document).ready(function () {
                                $('#summernote-content').summernote({
                                    height: 240,
                                    minHeight: null,
                                    maxHeight: null,
                                    lang: 'zh-CN',
                                    toolbar: [
                                        ['operate', ['undo', 'redo']],
                                        ['magic', ['style']],
                                        ['style', ['bold', 'italic', 'underline', 'clear']],
                                        ['para', ['height', 'fontsize', 'ul', 'ol', 'paragraph']],
                                        ['font', ['strikethrough', 'superscript', 'subscript']],
                                        ['color', ['color']],
                                        ['insert', ['picture', 'link', 'hr']],
                                        ['codeview', ['codeview']],
                                    ],
                                    callbacks: {
                                        onImageUpload: function (files) {
                                            sendFile(files[0]);
                                        }
                                    }
                                });

                                $(".modal-dialog .close").click(function () {
                                    $(this).parents(".modal").click();
                                });
                                function sendFile(files) {
                                    data = new FormData();
                                    data.append("file", files);
                                    $.ajax({
                                        data: data,
                                        dataType: 'json',
                                        type: "POST",
                                        url: "{:url('Plugin/uploadImg')}", //上传路径
                                        cache: false,
                                        contentType: false,
                                        processData: false,
                                        success: function (data) {
                                            if (data.code === 1)
                                            {
                                                $('#summernote-content').summernote('insertImage', data.data.src);
                                            } else {
                                                layer.msg(data.msg);
                                            }
                                        },
                                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                                            layer.msg('上传失败');
                                        }
                                    });
                                }
                            });

                            $('[name="card_order"]').change(function () {
                                var status = $(this).val();
                                if (status == 3) {
                                    $('.block_selectcard_fee').slideDown();
                                } else {
                                    $('.block_selectcard_fee').slideUp();
                                }
                            });

                            $('[name="visit_type"]').change(function () {
                                var status = $(this).val();
                                if (status == 1) {
                                    $('.block-visit_password').slideDown();
                                } else {
                                    $('.block-visit_password').slideUp();
                                }
                            });
                            $('[name="wholesale_discount"]').change(function () {
                                var status = $(this).val();
                                if (status == 1) {
                                    $('.block-wholesale_discount').slideDown();
                                } else {
                                    $('.block-wholesale_discount').slideUp();
                                }
                            });
                            $('[name="can_proxy"]').change(function () {
                                if ($(this).val() == 1) {
                                    $('.block-agent_type').slideDown();
                                } else {
                                    $('.block-agent_type').slideUp();
                                }
                            });
                            function del_discount_item(obj) {
                                $(obj).parents('.discount_item').remove();
                            }
                            function add_discount_item() {
                                var html = '<div class="form-group row discount_item d-flex align-items-center"><div class="col-3"><input name="wholesale_discount_list[num][]" type="number" class="form-control" value="" min=0></div><div class="col-auto">张</div><div class="col-3"><input name="wholesale_discount_list[price][]" type="text" class="form-control" value=""></div><div class="col-auto">元</div><div class="col-3"><button type="button" class="btn btn-danger waves-effect waves-light btn-sm" onclick="del_discount_item(this)">删除</button></div></div>';
                                $('.block-discount_list').append($(html));
                            }
                            $('.btn-submit').click(function () {

                                var good_name = $("form").find("input[name='name']").val();
                                if (!good_name) {
                                    $.alert('商品名称不能为空!');
                                    return false;
                                }
                                var good_price = $("form").find("input[name='price']").val();
                                if (!good_price) {
                                    $.alert('商品价格不能为空!');
                                    return false;
                                }
                                $('#content').html($('#summernote-content').summernote('code'));
                                var loading = layer.load(1, {shade: [0.1, '#fff']});
                                $.ajax({
                                    url: "{:url('goods/add')}",
                                    type: 'post',
                                    data: $('#form1').serialize(),
                                    success: function (info) {
                                        layer.close(loading);

                                        if (info.code != 1) {
                                            $.alert(info.msg);
                                        } else {
                                            $.confirm({title: '提示!', content: info.msg,
                                                type: 'green',
                                                typeAnimated: true,
                                                buttons: {
                                                    tryAgain: {
                                                        text: '返回',
                                                        btnClass: 'btn-light',
                                                        action: function () {
                                                            location.href = "{:url('goods/index')}";
                                                        }
                                                    },
                                                    addStorage: {
                                                        text: '去添加库存',
                                                        btnClass: 'btn-primary',
                                                        action: function () {
                                                            location.href = "{:url('goods_card/add')}?goods_id=" + info.data.goods_id;
                                                        }
                                                    },
                                                }
                                            });
                                        }

                                    },
                                    error: function (xhr, textStatus, errorThrown) {
                                        layer.close(loading);
                                    }
                                });
                                return false;
                            });


                            $('[name="send_gift"]').change(function () {
                                var status = $(this).val();
                                if (status == 1 || status == 2) {
                                    $('.block-send_gift').slideDown();
                                } else {
                                    $('.block-send_gift').slideUp();
                                }
                            });


                            function del_gift_item(obj) {
                                $(obj).parents('.discount_item').remove();
                            }
                            function add_gift_item() {
                                var html = '<div class="form-group row discount_item d-flex align-items-center"><div class="col-3"><input name="send_gift_list[limit][]" type="number" class="form-control" value="" min=0></div><div class="col-3">买满/张</div><div class="col-3"><input name="send_gift_list[num][]" type="text" class="form-control" value=""></div><div class="col-3">赠/张</div><div class="col-3"><button type="button" class="btn btn-danger waves-effect waves-light btn-sm" onclick="del_gift_item(this)">删除</button></div></div>';
                                $('.block-gift_list').append($(html));
                            }
</script>
{/block}


