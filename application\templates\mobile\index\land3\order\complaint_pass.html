<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>投诉详情 - {:sysconf('site_name')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <!-- Bootstrap -->
        <link href="__RES__/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="__RES__/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Slider -->               
        <link rel="stylesheet" href="__RES__/theme/landrick/css/owl.carousel.min.css"/> 
        <link rel="stylesheet" href="__RES__/theme/landrick/css/owl.theme.default.min.css"/> 
        <!-- Main Css -->
        <link href="__RES__/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="__RES__/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">
    </head>

    <body>
        {include file="./default_header"}

        <!-- Hero Start -->
        <section class="bg-half bg-light d-table w-100">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-12 text-center">
                        <div class="page-next-level">
                            <div class="alert alert-light alert-pills shadow" role="alert">
                                <span class="badge badge-pill badge-danger mr-1">投诉查询</span>
                                <span class="content"> 轻松查询投诉，保障您的消费权益</span>
                            </div>

                            <div class="page-next">
                                <nav aria-label="breadcrumb" class="d-inline-block">
                                    <ul class="breadcrumb bg-white rounded shadow mb-0">
                                        <li class="breadcrumb-item"></li>
                                        <li class="breadcrumb-item" aria-current="page">投诉查询</li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div><!--end col-->
                </div><!--end row-->
            </div> <!--end container-->
        </section><!--end section-->
        <!-- Hero End -->


        <!-- Start Section -->
        <section class="section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="section-title text-center mb-4 pb-2">


                            <div class="col mt-4 pt-2" id="forms">
                                <div class="component-wrapper rounded shadow">
                                    <div class="p-4 border-bottom">
                                        <h5 class="mb-0"> 投 诉 详 情 </h5>
                                    </div>

                                    <div class="p-4">{if condition="!empty($complaint)"}
                                        <form name='report'>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>订单编号</label>
                                                        <i data-feather="user" class="fea icon-sm icons"></i>
                                                        <input disabled name="trade_no" type="text" class="form-control pl-5" value="{$complaint->trade_no|htmlentities}">
                                                    </div>
                                                </div><!--end col-->
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>举报原因</label>
                                                        <i data-feather="map-pin" class="fea icon-sm icons"></i>
                                                        <input disabled class="form-control pl-5" name="type" type="text" value="{$complaint->type|htmlentities}">
                                                    </div> 
                                                </div><!--end col-->
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>联系方式</label>
                                                        <i data-feather="at-sign" class="fea icon-sm icons"></i>
                                                        <input  disabled name="name" id="name" type="text" class="form-control pl-5" value="{$complaint->qq|htmlentities}">
                                                    </div>
                                                </div><!--end col-->
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>投诉时间</label>
                                                        <i data-feather="clock" class="fea icon-sm icons"></i>
                                                        <input disabled name="email" id="text" type="text" class="form-control pl-5" value="{$complaint.create_at|date='Y-m-d H:i:s', ###|htmlentities}">
                                                    </div> 
                                                </div><!--end col-->
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>投诉状态</label>
                                                        <i data-feather="align-center" class="fea icon-sm icons"></i>{if condition="$complaint->status == 0"}
                                                        <input disabled name="truck" type="text" class="form-control pl-5" value="已处理">
                                                        {elseif condition="$complaint->status == -1"}
                                                        <input disabled name="mobile" type="text" class="form-control pl-5" value="已撤销">
                                                        {else /}
                                                        <input disabled name="mobile" type="text" class="form-control pl-5" value="已处理">
                                                        {/if}
                                                    </div> 
                                                </div><!--end col-->

                                                {if $complaint->buyer_qrcode!=""}
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>胜诉收款二维码</label>
                                                        <div class="form-control">
                                                            <a target="blank" href="{$complaint.buyer_qrcode}">点击查看</a>
                                                        </div>
                                                    </div> 
                                                </div>
                                                {/if}

                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>关于投诉说明</label>
                                                        <i data-feather="mail" class="fea icon-sm icons"></i>
                                                        <input disabled name="text" id="text" type="text" class="form-control pl-5" value="请买家与商家沟通并说明问题，以便快速解决问题！">
                                                    </div> 
                                                </div><!--end col-->
                                                <div class="col-sm-12">
                                                    {if condition="$complaint->status == 0"}
                                                    <button type="button" class="btn btn-soft-secondary mt-2 mr-2"  onclick="cancel()">撤销投诉</button>
                                                    {/if}
                                                    <button type="button" class="btn btn-soft-secondary mt-2 mr-2"  onclick="detail()">投诉详情</button>

                                                </div><!--end col-->
                                            </div><!--end row-->
                                        </form><!--end form-->
                                    </div>
                                </div>
                            </div><!--end col-->
                            <!-- Forms End -->
                            <script>
                                function cancel() {
                                    layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                        $.post("{:url('Index/Order/complaintCancel')}", {
                                            token: "{$token}",
                                            trade_no: "{$complaint->trade_no}",
                                            pwd: pwd
                                        }, function (data) {
                                            if (data.code != '200') {
                                                layer.msg(data.msg, {icon: 2})
                                            } else {
                                                layer.msg(data.msg, {icon: 1}, function () {
                                                    location.reload();
                                                })
                                            }
                                        });
                                    });
                                }

                                function detail() {
                                    layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                        $.post("{:url('Index/Order/complaintPass')}", {
                                            token: "{$token}",
                                            trade_no: "{$complaint->trade_no}",
                                            pwd: pwd
                                        }, function (data) {
                                            if (data.code != '200') {
                                                layer.msg(data.msg, {icon: 2})
                                            } else {
                                                layer.msg(data.msg, {icon: 1}, function () {
                                                    location.href = data.url;
                                                })
                                            }

                                        });
                                    });
                                }
                            </script>
                            {else /}
                            <div style="display:block;text-align:center; font-size:24px;margin: 5rem 0;">订单不存在或该订单暂无投诉信息</div>
                            {/if}
                        </div><!--end col-->
                    </div><!--end row-->

                </div><!--end container-->

            </div><!--end container-->


        </section><!--end section-->
        <!-- End Section -->

        {include file="./default_footer"}

        <!-- javascript -->
        <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- SLIDER -->
        <script src="__RES__/theme/landrick/js/owl.carousel.min.js "></script>
        <script src="__RES__/theme/landrick/js/owl.init.js "></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="/static/app/js/layer.js"></script>
    </body>
</html>