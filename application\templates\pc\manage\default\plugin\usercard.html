{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >

    <div class="form-group">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
                <p style="font-size:14px;" class="text-center">用于默认购卡页显示商家信息</p>
            </div>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    <div class="form-group">
        <label class="col-sm-2 control-label">功能是否是否开启</label>
        <div class='col-sm-8'>
            <select name="status" class="layui-input">
                <option value="0" {if plugconf('usercard','status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('usercard','status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">功能是否是否开启</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">显示商家是否认证</label>
        <div class='col-sm-8'>
            <select name="auth_status" class="layui-input">
                <option value="0" {if plugconf('usercard','auth_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('usercard','auth_status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">请提前开启实名认证功能</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">显示信誉保证金额度</label>
        <div class='col-sm-8'>
            <select name="deposit_status" class="layui-input">
                <option value="0" {if plugconf('usercard','deposit_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('usercard','deposit_status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">请提前开启信誉保证金功能</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">显示商家开店时间</label>
        <div class='col-sm-8'>
            <select name="time_status" class="layui-input" >
                <option value="0" {if plugconf('usercard','time_status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('usercard','time_status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">根据注册时间计算</p>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>


<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });

</script>
{/block}