{extend name='./content'}

{block name="content"}


<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户账号</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''|htmlentities}" placeholder="请输入商户账号" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select name="status">
                <option value="">全部状态</option>
                <option value="0" {if $Think.get.status === '0'}selected{/if}>待处理</option>
                <option value="1" {if $Think.get.status === '1'}selected{/if}>已发放</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
    <p style="font-size:13px;">提示：直接标记为已发放系统不会自动将奖励结算到商户余额。如果是现金红包类奖励可以一键发送到余额，服务类奖励需手动发货！</p>

</div>
<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action">
    <table class="table table-hover">
        <thead>
            <tr>
                <th class="text-center">ID</th>
                <th class="text-center">用户名</th>
                <th class="text-center">QQ</th>
                <th class="text-center">获奖奖品</th>
                <th class="text-center">获奖时间</th>
                <th class="text-center">奖品价值</th>
                <th class="text-center">发放状态</th>
                <th class="text-center">发放时间</th>
                <th class="text-center">操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $v}
            <tr>
                <td class="text-center">{$v.id}</td>
                <td class="text-center">{$v.user.username}</td>
                <td class="text-center">{$v.user.qq}</td>
                <td class="text-center">{$v.name}</td>
                <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>

                <td class="text-center">

                    {if $v.price>0}
                    {$v.price}
                    {if $v.send_money==0}
                    <a style="color:green" data-tips="确定标记为发放吗？ " data-update="{$v.id}" data-field='send_money' data-value='1' data-action='{:url("lotteryOrderSendMoney")}'
                       href="javascript:void(0)">发放到余额</a>
                    {else}
                    已发放到余额
                    {/if}
                    {else}
                    -
                    {/if}
                </td>

                <td class='text-center'>
                    {if $v.status==1}
                    <span style="color:green"><i class="glyphicon glyphicon-ok"></i> 已发放</span>
                    {else/}
                    <span>待发放</span>
                    {/if}
                </td>

                <td class='text-center'>
                    {if $v.status==1}
                    {$v.success_at|date="Y-m-d H:i:s",###}
                    {/if}
                </td>

                <td class='text-center'>
                    {if $v.status==0}
                    <a style="color:green" data-tips="确定标记为发放吗？ " data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("lotteryOrderChangeStatus")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-ok"></i>标记为已发放</a>
                    {/if}
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });
</script>
{/block}
