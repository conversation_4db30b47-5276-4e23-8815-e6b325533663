{extend name="lite_base" /}

{block name="content"}
<div class="card mb-0">

    <div class="card-content">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-nowrap mb-0">
                    <tbody>
                        <tr>
                            <th scope="row">链接状态</th>
                            <td>
                                {if $category.link_status==1}
                                <span class="badge badge-pill badge-soft-success font-size-12">已开启</span>
                                {else/}
                                <span class="badge badge-pill badge-soft-danger font-size-12">已关闭</span>
                                {/if}
                            </td>
                        </tr>

                        {if sysconf('site_domain_short')!=""}
                        <tr>
                            <th scope="row">短链接<span class="badge badge-pill badge-soft-success font-size-12">推荐</span></th>
                            <td>
                                <a href="{$category.short_link}"  target="_blank">{$category.short_link}</a>
                                <a href="javascript:;" class="btn btn-primary btn-sm waves-effect waves-light clipboard" data-clipboard-text="{$category.short_link}">复制链接</a>
                                <button onclick="reset_link('liebiao', '{$category.id}', 0)" class="btn btn-primary  btn-sm waves-effect waves-light">单独重置</button>
                            </td>
                        </tr>
                        {/if}


                        <tr>
                            <th scope="row">分类购买地址</th>
                            <td>
                                <a href="{$category.link}"  target="_blank">{$category.link}</a>
                                <a href="javascript:;" class="btn btn-primary btn-sm waves-effect waves-light clipboard" data-clipboard-text="{$category.link}">复制链接</a>

                            </td>
                        </tr>
                        <tr>
                            <th scope="row">二维码地址</th>
                            <td>
                                <img src="{:generate_qrcode_link($category.link)}" alt="">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"></th>
                            <td>
                                <button onclick="reset_link('liebiao', '{$category.id}', 1)" class="btn btn-warning  btn-sm waves-effect waves-light">链接重置</button>
                                {if $category.link_status==1}
                                <button onclick="close_link('liebiao', '{$category.id}', 0)" class="btn btn-danger btn-sm waves-effect waves-light">关闭链接</button>
                                {else/}
                                <button onclick="close_link('liebiao', '{$category.id}', 1)"  class="btn btn-success btn-sm waves-effect waves-light">开启链接</button>
                                {/if}
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
{/block}

{block name="js"}
<!-- BEGIN: Page JS-->
<script src="__RES__/app/js/clipboard.js"></script>
<script>

                                    var clipboard = new ClipboardJS('.btn');
                                    clipboard.on('success', function (e) {
                                        layer.msg('链接复制成功！', {
                                            icon: 1
                                        });
                                    });

                                    clipboard.on('error', function (e) {
                                        layer.msg('链接复制失败，请手动复制！', {
                                            icon: 2
                                        });
                                    });
                                    function close_link(type, cate_id, status)
                                    {
                                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                                        $.post(
                                                "{:url('user/closelink')}", {type: type, cate_id: cate_id, status: status},
                                                function (data) {
                                                    if (data.code == 1) {
                                                        layer.msg(data.msg, {time: 1000, icon: 6}, function () {
                                                            location.reload();
                                                        });
                                                    } else {
                                                        layer.msg(data.msg, {time: 1000, icon: 5});
                                                    }
                                                }, "json");
                                    }

                                    function reset_link(type, cate_id, target) {

                                        $.confirm({
                                            title: '温馨提示',
                                            content: '确定要重置链接吗，重置链接后，之前的链接将不能访问！',
                                            type: 'red',
                                            typeAnimated: true,
                                            buttons: {
                                                tryAgain: {
                                                    text: '确定',
                                                    btnClass: 'btn-red',
                                                    action: function () {
                                                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                                                        $.post(
                                                                "{:url('user/relink')}", {type: type, cate_id: cate_id, target: target},
                                                                function (data) {
                                                                    if (data.code == 1) {
                                                                        layer.msg(data.msg, {time: 1000, icon: 6}, function () {
                                                                            location.reload();
                                                                        });
                                                                    } else {
                                                                        layer.msg(data.msg, {time: 1000, icon: 5});
                                                                    }
                                                                }, "json");
                                                    }
                                                },
                                                cancel: {
                                                    text: '取消',
                                                }
                                            }
                                        });

                                    }

</script>

<!-- END: Page JS-->
{/block}
