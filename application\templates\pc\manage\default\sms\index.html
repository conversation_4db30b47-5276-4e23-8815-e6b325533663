{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' style='padding-top:20px'>
    <div class="form-group">
        <label class="col-sm-2 control-label">验证码短信通道</label>
        <div class='col-sm-8'>
            <select name="sms_channel" class="layui-input">
                <option value="" {if sysconf('sms_channel')==''}selected{/if}>关闭</option>
                <option value="yixin" {if sysconf('sms_channel')=='yixin'}selected{/if}>卡酷云短信服务</option>
                <option value="smsbao" {if sysconf('sms_channel')=='smsbao'}selected{/if}>短信宝</option>
                <option value="alidayu" {if sysconf('sms_channel')=='alidayu'}selected{/if}>阿里云短信服务</option>
                <option value="1cloudsp" {if sysconf('sms_channel')=='1cloudsp'}selected{/if}>天瑞云短信</option>
                <option value="253sms" {if sysconf('sms_channel')=='253sms'}selected{/if}>创蓝253短信</option>
            </select>
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">订单通知短信通道</label>
        <div class='col-sm-8'>
            <select name="sms_notify_channel" class="layui-input">
                <option value="" {if sysconf('sms_notify_channel')==''}selected{/if}>关闭</option>
                <option value="yixin" {if sysconf('sms_notify_channel')=='yixin'}selected{/if}>卡酷云短信服务</option>
                <option value="smsbao" {if sysconf('sms_notify_channel')=='smsbao'}selected{/if}>短信宝</option>
                <option value="253sms" {if sysconf('sms_notify_channel')=='253sms'}selected{/if}>创蓝253短信</option>
                <option value="alidayu" {if sysconf('sms_notify_channel')=='alidayu'}selected{/if}>阿里云短信服务</option>
                <option value="1cloudsp" {if sysconf('sms_notify_channel')=='1cloudsp'}selected{/if}>天瑞云短信</option>
            </select>
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉密码短信通道</label>
        <div class='col-sm-8'>
            <select name="sms_complaint_channel" class="layui-input">
                <option value="" {if sysconf('sms_complaint_channel')==''}selected{/if}>关闭</option>
                <option value="yixin" {if sysconf('sms_complaint_channel')=='yixin'}selected{/if}>卡酷云短信服务</option>
                <option value="smsbao" {if sysconf('sms_complaint_channel')=='smsbao'}selected{/if}>短信宝</option>
                <option value="alidayu" {if sysconf('sms_complaint_channel')=='alidayu'}selected{/if}>阿里云短信服务</option>
                <option value="1cloudsp" {if sysconf('sms_complaint_channel')=='1cloudsp'}selected{/if}>天瑞云短信</option>
                <option value="253sms" {if sysconf('sms_complaint_channel')=='253sms'}selected{/if}>创蓝253短信</option>
            </select>
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉通知短信通道</label>
        <div class='col-sm-8'>
            <select name="sms_complaint_notify_channel" class="layui-input">
                <option value="" {if sysconf('sms_complaint_notify_channel')==''}selected{/if}>关闭</option>
                <option value="yixin" {if sysconf('sms_complaint_notify_channel')=='yixin'}selected{/if}>卡酷云短信服务</option>
                <option value="smsbao" {if sysconf('sms_complaint_notify_channel')=='smsbao'}selected{/if}>短信宝</option>
                <option value="alidayu" {if sysconf('sms_complaint_notify_channel')=='alidayu'}selected{/if}>阿里云短信服务</option>
                <option value="1cloudsp" {if sysconf('sms_complaint_notify_channel')=='1cloudsp'}selected{/if}>天瑞云短信</option>
                <option value="253sms" {if sysconf('sms_complaint_notify_channel')=='253sms'}selected{/if}>创蓝253短信</option>
            </select>
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">预存款不足短信通道</label>
        <div class='col-sm-8'>
            <select name="sms_deposit_notify_channel" class="layui-input">
                <option value="" {if sysconf('sms_deposit_notify_channel')==''}selected{/if}>关闭</option>
                <option value="yixin" {if sysconf('sms_deposit_notify_channel')=='yixin'}selected{/if}>卡酷云短信服务</option>
                <option value="smsbao" {if sysconf('sms_deposit_notify_channel')=='smsbao'}selected{/if}>短信宝</option>
                <option value="alidayu" {if sysconf('sms_deposit_notify_channel')=='alidayu'}selected{/if}>阿里云短信服务</option>
                <option value="1cloudsp" {if sysconf('sms_deposit_notify_channel')=='1cloudsp'}selected{/if}>天瑞云短信</option>
                <option value="253sms" {if sysconf('sms_deposit_notify_channel')=='253sms'}selected{/if}>创蓝253短信</option>
            </select>
            <p class="help-block"></p>
        </div>
    </div>


    <div class="hr-line-dashed"></div>
    卡酷云短信配置&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;<a href="https://www.7ka.co/sms/jingfaka" target="_blank">配置教程与视频演示</a>

    <div class="form-group">
        <label class="col-sm-2 control-label">卡酷云会员ID</label>
        <div class='col-sm-8'>
            <input type="text" name="yixin_sms_user" required="required" title="请输入卡酷云会员ID" placeholder="请输入卡酷云会员ID" value="{:sysconf('yixin_sms_user')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">卡酷云短信密钥</label>
        <div class='col-sm-8'>
            <input type="text" name="yixin_sms_pass" required="required" title="请输入卡酷云短信密钥" placeholder="请输入卡酷云短信密钥" value="{:sysconf('yixin_sms_pass')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">短信签名</label>
        <div class='col-sm-8'>
            <input type="text" name="yixin_sms_signature" required="required" title="请输入短信签名" placeholder="请输入短信签名" value="{:sysconf('yixin_sms_signature')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">短信费用（元/条）</label>
        <div class='col-sm-8'>
            <input type="text" name="yixin_sms_cost" required="required" title="请输入短信费用" placeholder="请输入短信费用" value="{:sysconf('yixin_sms_cost')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>


    <div class="hr-line-dashed"></div>
    短信宝配置

    <div class="form-group">
        <label class="col-sm-2 control-label">短信宝账号</label>
        <div class='col-sm-8'>
            <input type="text" name="smsbao_user" required="required" title="请输入短信宝账号" placeholder="请输入短信宝账号" value="{:sysconf('smsbao_user')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">短信宝密码</label>
        <div class='col-sm-8'>
            <input type="text" name="smsbao_pass" required="required" title="请输入短信宝密码" placeholder="请输入短信宝密码" value="{:sysconf('smsbao_pass')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">短信签名</label>
        <div class='col-sm-8'>
            <input type="text" name="smsbao_signature" required="required" title="请输入短信签名" placeholder="请输入短信签名" value="{:sysconf('smsbao_signature')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">短信费用（元/条）</label>
        <div class='col-sm-8'>
            <input type="text" name="smsbao_cost" required="required" title="请输入短信费用" placeholder="请输入短信费用" value="{:sysconf('smsbao_cost')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    阿里云短信配置

    <div class="form-group">
        <label class="col-sm-2 control-label">app key</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_key" required="required" title="请输入app key" placeholder="请输入app key" value="{:sysconf('alidayu_key')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">app secret</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_secret" required="required" title="请输入app secret" placeholder="请输入app secret" value="{:sysconf('alidayu_secret')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">验证码模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('alidayu_smstpl')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉密码模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_complaint_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('alidayu_complaint_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单：${trade_no},已投诉成功，投诉密码为：${code}，在卖家未给您处理好问题前，请勿将密码告知任何人！</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉通知模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_complaint_notify_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('alidayu_complaint_notify_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单：${trade_no}，已经有买家投诉，请您及时登录后台处理。</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">订单通知模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_order_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('alidayu_order_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单已支付成功，订单号：${trade_no}，若您付款成功后没有领取虚拟卡信息，请您及时通过订单查询提取。</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">预存款不足模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_deposit_notify_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('alidayu_deposit_notify_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的平台预存款已不足${money}元，为防止交易中断，请及时登录后台充值！</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">signature (签名)</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_signature" required="required" title="请输入签名" placeholder="请输入签名" value="{:sysconf('alidayu_signature')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">短信费用（元/条）</label>
        <div class='col-sm-8'>
            <input type="text" name="alidayu_cost" required="required" title="请输入短信费用" placeholder="请输入短信费用" value="{:sysconf('alidayu_cost')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    天瑞云短信配置

    <div class="form-group">
        <label class="col-sm-2 control-label">AccessKey</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_key" required="required" title="请输入AccessKey" placeholder="请输入AccessKey" value="{:sysconf('1cloudsp_key')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">AccessSecret</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_secret" required="required" title="请输入AccessSecret" placeholder="请输入AccessSecret" value="{:sysconf('1cloudsp_secret')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">验证码模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('1cloudsp_smstpl')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉密码模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_complaint_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('1cloudsp_complaint_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单：{1},已投诉成功，投诉密码为：{2}，在卖家未给您处理好问题前，请勿将密码告知任何人！</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">投诉通知模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_complaint_notify_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('1cloudsp_complaint_notify_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单：{1}，已经有买家投诉，请您及时登录后台处理。</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">订单通知模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_order_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('1cloudsp_order_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的订单已支付成功，订单号：{1}，若您付款成功后没有领取虚拟卡信息，请您及时通过订单查询提取。</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">预存款不足模板编号</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_deposit_notify_smstpl" required="required" title="请输入模板编号" placeholder="请输入模板编号" value="{:sysconf('1cloudsp_deposit_notify_smstpl')}" class="layui-input">
            <p class="help-block">短信模板应如下：您的平台预存款已不足{1}元，为防止交易中断，请及时登录后台充值！</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">signature (签名)</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_signature" required="required" title="请输入签名" placeholder="请输入签名" value="{:sysconf('1cloudsp_signature')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">短信费用（元/条）</label>
        <div class='col-sm-8'>
            <input type="text" name="1cloudsp_cost" required="required" title="请输入短信费用" placeholder="请输入短信费用" value="{:sysconf('1cloudsp_cost')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    创蓝235短信配置

    <div class="form-group">
        <label class="col-sm-2 control-label">api_account</label>
        <div class='col-sm-8'>
            <input type="text" name="253sms_key" required="required" title="请输入api_account" placeholder="请输入api_account" value="{:sysconf('253sms_key')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">api_password</label>
        <div class='col-sm-8'>
            <input type="text" name="253sms_secret" required="required" title="请输入api_password" placeholder="请输入api_password" value="{:sysconf('253sms_secret')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">signature (签名)</label>
        <div class='col-sm-8'>
            <input type="text" name="253sms_signature" required="required" title="请输入签名" placeholder="请输入签名" value="{:sysconf('253sms_signature')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">短信费用（元/条）</label>
        <div class='col-sm-8'>
            <input type="text" name="253sms_cost" required="required" title="请输入短信费用" placeholder="请输入短信费用" value="{:sysconf('253sms_cost')}" class="layui-input">
            <p class="help-block"></p>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    <div class="col-sm-4 col-sm-offset-2">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
{/block}
