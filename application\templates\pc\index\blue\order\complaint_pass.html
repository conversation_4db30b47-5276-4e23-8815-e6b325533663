<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, maximum-scale=1, initial-scale=1, user-scalable=yes">
    <title>投诉详情</title>
    <link rel="stylesheet" href="__RES__/theme/blue/layui/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/style.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/response.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/order.css">
    <style>
        .layui-layer-btn a {font-size: 14px;}
		.complaint-info {padding-top: .1rem;}
		.complaint-info .title {border-left: 3px solid #3476fe;font-size: .4rem;padding-top: .2rem;padding-left: .1rem;padding-bottom: .2rem;}
		.complaint-info .form-item {display: flex;margin-bottom: 15px;font-size: .2rem;}
		.complaint-info .form-item .label {margin: 0 10px 0 0;flex-shrink: 0;line-height: 37px;height: 37px;user-select: none;font-family: 'é»‘ä½“';}
		.complaint-info .form-item .input-box input[type="text"] {line-height: 37px;height: 37px;font-size: 14px;}
		.complaint-info .form-item .input-box input[type="file"] {line-height: 37px;height: 37px;font-size: 14px;}
		.complaint-info .form-item .input-box textarea {font-size: 14px;}
		.complaint-info .form-item .input-box {width: 100%;display: flex;align-items: center;flex-wrap: wrap;}
		.complaint-info .form-control {padding: 0 .1rem;}
		.complaint-info .input-box .type-item {border: 1px solid #eee;display: inline-block;padding: 0 10px;border-radius: 3px;margin: 0 10px 10px 0;font-size: 14px;line-height: 35px;cursor: pointer;user-select: none;position: relative;overflow: hidden;}
		.complaint-info .input-box .type-item input {display: none;}
		.complaint-info .input-box .type-item.checked {border: 1px solid #3476fe;}
		.complaint-info .input-box .type-item.checked:before {content: '';position: absolute;right: -9px;bottom: -5px;background-color: #3476fe;width: 26px;height: 15px;transform: rotate(-45deg);}
		.complaint-info .input-box .type-item.checked:after {opacity: 1;content: '';position: absolute;width: 5px;height: 11px;background: transparent;bottom: 1px;right: 1px;border: 2px solid #fff;border-top: none;border-left: none;-webkit-transform: rotate(35deg);-moz-transform: rotate(35deg);-o-transform: rotate(35deg);-ms-transform: rotate(35deg);transform: rotate(35deg);}
		.complaint-info form [type="button"] {width: 150px;display: block;}
		.complaint-info .input-box .btn-experience {height: .5rem;line-height: 36px;padding: 0;background-color: #3476fe;color: #fff;-webkit-border-radius: 18px;-moz-border-radius: 18px;border-radius: 18px;font-size: .2rem;}
		@media (max-width: 768px) {.complaint-info .input-box .btn-experience {height: .8rem;font-size: .3rem;}}
		.form-control:focus {box-shadow: 0 0 0 0.03rem rgba(0, 123, 255, .25);}
		.no-pay {background-color: #999 !important}
		.no-order {text-align: center;color: #999;padding: 50px 0;background-color: #fff;}
		.order-search .title .search-btn {padding: .1rem .25rem;border: .1rem solid #e1ebff;background-color: #3476fe;border-radius: 25px;color: #fff;cursor: pointer;margin-right: .6rem;}
		.order-search .title .active {opacity: 1;}
		.faq-check {color: #3476fe;font-size: 16px;float: right;cursor: pointer;}
		.faq-check p {color: #3476fe;margin-left: .1rem;display: inline;}
		@media (max-width: 768px) {.faq-check {float: none;margin-top: .5rem;font-size: 16px;}.order-search .title .search-btn {margin-right: .2rem;}}
    </style>
</head>
<body>
<header class="header query-header">
    <div class="bgimg"><img src="__RES__/theme/blue/images/header_bg.png" alt=""></div>
    <div class="container">
{include file="./default_header"}
        <div class="banner">
            <div class="text-introduce">
                <div class="h1">轻松查询订单，享受卡密自动交易</div>
            </div>
            <div class="img"><img src="__RES__/theme/blue/imgs/banner_query_img.png" alt=""></div>
        </div>
    </div>
</header>
<div class="query">
    <div class="container">
        <div class="order-search">
            <div class="order-search-box complaint-info">
                <div class="title">投诉订单</div>
                {if condition="!empty($complaint)"}
                
                	                	<form class="form" style="margin-top: .2rem;padding-bottom: .2rem;">

                    <div class="form-item">
                        <label class="label">订单编号</label>
                        <div class="input-box">
                            <input disabled type="text" class="form-control" name="trade_no" value="{$complaint->trade_no|htmlentities}"/>
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">举报原因</label>
                        <div class="input-box">
                            <input disabled type="text" class="form-control" name="type" value="{$complaint->type|htmlentities}"/>
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">联系方式</label>
                        <div class="input-box">
                            <a target="_blank" href="//wpa.qq.com/msgrd?v=3&amp;uin={$complaint->qq|htmlentities}&amp;site=qq&amp;menu=yes">
                                <input disabled type="text" class="form-control" name="qq" value="{$complaint->qq|htmlentities}"/>
                            </a>
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">投诉时间</label>
                        <div class="input-box">
                            <input disabled type="text" class="form-control" name="mobile" value="{$complaint.create_at|date='Y-m-d H:i:s', ###|htmlentities}"/>
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">投诉状态</label>
                        <div class="input-box">
                            
                            
                           {if condition="$complaint->status == 0"}
                                                        未处理
                                                        {elseif condition="$complaint->status == -1"}
                                                        已撤销
                                                        {else /}
                                                        已处理
                                                        {/if}
                                                    </div>
                    </div>

                    
                    <div class="form-item">
                        <label class="label"><span style="opacity: 0;">操作按钮</span></label>
                        <div class="input-box">
                            {if condition="$complaint->status == 0"}
                        	                            <button type="button" class="btn btn-experience" style="margin-right: 30px" onclick="cancel()">撤销</button>                            
                        	                            {/if}
                        	                            <button type="button" class="btn btn-experience" onclick="detail()">查看</button>
                        </div>
                    </div>
                </form>
                
                
                
                 {else /}
                            <div style="display:block;text-align:center; font-size:24px;margin: 5rem 0;">订单不存在或该订单暂无投诉信息</div>
                            {/if}
                
				        	</div>
				        	
				        	
				        	
				        	<script>
                                function cancel() {
                                    layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                        $.post("{:url('Index/Order/complaintCancel')}", {
                                            token: "{$token}",
                                            trade_no: "{$complaint->trade_no}",
                                            pwd: pwd
                                        }, function (data) {
                                            if (data.code != '200') {
                                                layer.msg(data.msg, {icon: 2})
                                            } else {
                                                layer.msg(data.msg, {icon: 1}, function () {
                                                    location.reload();
                                                })
                                            }
                                        });
                                    });
                                }

                                function detail() {
                                    layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                        $.post("{:url('Index/Order/complaintPass')}", {
                                            token: "{$token}",
                                            trade_no: "{$complaint->trade_no}",
                                            pwd: pwd
                                        }, function (data) {
                                            if (data.code != '200') {
                                                layer.msg(data.msg, {icon: 2})
                                            } else {
                                                layer.msg(data.msg, {icon: 1}, function () {
                                                    location.href = data.url;
                                                })
                                            }

                                        });
                                    });
                                }
                            </script>
				        	
				        	
				        	
				        	
				        	
				        	
				        	
				        	
        </div>
    </div>
</div>
{include file="./default_footer"}
<script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="/static/app/js/layer.js"></script>
</body>
</html>