/**
 * Modern Theme JavaScript
 * 现代化主题交互脚本
 */

class ModernTheme {
  constructor() {
    this.init();
  }

  init() {
    this.setupNavigation();
    this.setupAnimations();
    this.setupForms();
    this.setupModals();
    this.setupTooltips();
    this.setupLazyLoading();
    this.setupThemeToggle();
  }

  // 导航栏功能
  setupNavigation() {
    const navbar = document.querySelector('.navbar');
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    // 滚动时导航栏效果
    let lastScrollY = window.scrollY;
    window.addEventListener('scroll', () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > 100) {
        navbar?.classList.add('scrolled');
      } else {
        navbar?.classList.remove('scrolled');
      }

      // 自动隐藏导航栏
      if (currentScrollY > lastScrollY && currentScrollY > 200) {
        navbar?.classList.add('nav-hidden');
      } else {
        navbar?.classList.remove('nav-hidden');
      }
      
      lastScrollY = currentScrollY;
    });

    // 移动端菜单切换
    navToggle?.addEventListener('click', () => {
      navMenu?.classList.toggle('active');
      navToggle?.classList.toggle('active');
    });

    // 点击菜单项后关闭移动端菜单
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', () => {
        navMenu?.classList.remove('active');
        navToggle?.classList.remove('active');
      });
    });
  }

  // 动画效果
  setupAnimations() {
    // 滚动动画观察器
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in');
        }
      });
    }, observerOptions);

    // 观察所有需要动画的元素
    document.querySelectorAll('.card, .feature-card, .hero-content').forEach(el => {
      observer.observe(el);
    });

    // 数字计数动画
    this.animateCounters();
  }

  // 数字计数动画
  animateCounters() {
    const counters = document.querySelectorAll('[data-counter]');
    
    counters.forEach(counter => {
      const target = parseInt(counter.dataset.counter);
      const duration = 2000;
      const step = target / (duration / 16);
      let current = 0;

      const updateCounter = () => {
        current += step;
        if (current < target) {
          counter.textContent = Math.floor(current).toLocaleString();
          requestAnimationFrame(updateCounter);
        } else {
          counter.textContent = target.toLocaleString();
        }
      };

      // 当元素进入视口时开始动画
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            updateCounter();
            observer.unobserve(entry.target);
          }
        });
      });

      observer.observe(counter);
    });
  }

  // 表单增强
  setupForms() {
    // 浮动标签效果
    document.querySelectorAll('.form-input').forEach(input => {
      const label = input.previousElementSibling;
      
      if (label && label.classList.contains('form-label')) {
        input.addEventListener('focus', () => {
          label.classList.add('focused');
        });

        input.addEventListener('blur', () => {
          if (!input.value) {
            label.classList.remove('focused');
          }
        });

        // 初始状态检查
        if (input.value) {
          label.classList.add('focused');
        }
      }
    });

    // 表单验证
    document.querySelectorAll('form').forEach(form => {
      form.addEventListener('submit', (e) => {
        if (!this.validateForm(form)) {
          e.preventDefault();
        }
      });
    });
  }

  // 表单验证
  validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('.form-input[required]');

    inputs.forEach(input => {
      const value = input.value.trim();
      const errorElement = input.parentNode.querySelector('.error-message');

      // 移除之前的错误状态
      input.classList.remove('error');
      errorElement?.remove();

      if (!value) {
        this.showFieldError(input, '此字段为必填项');
        isValid = false;
      } else if (input.type === 'email' && !this.isValidEmail(value)) {
        this.showFieldError(input, '请输入有效的邮箱地址');
        isValid = false;
      }
    });

    return isValid;
  }

  // 显示字段错误
  showFieldError(input, message) {
    input.classList.add('error');
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message text-sm text-error-500 mt-1';
    errorElement.textContent = message;
    input.parentNode.appendChild(errorElement);
  }

  // 邮箱验证
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 模态框功能
  setupModals() {
    // 打开模态框
    document.querySelectorAll('[data-modal-target]').forEach(trigger => {
      trigger.addEventListener('click', (e) => {
        e.preventDefault();
        const modalId = trigger.dataset.modalTarget;
        const modal = document.getElementById(modalId);
        if (modal) {
          this.openModal(modal);
        }
      });
    });

    // 关闭模态框
    document.querySelectorAll('[data-modal-close]').forEach(closeBtn => {
      closeBtn.addEventListener('click', () => {
        const modal = closeBtn.closest('.modal');
        if (modal) {
          this.closeModal(modal);
        }
      });
    });

    // 点击背景关闭模态框
    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeModal(modal);
        }
      });
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.active');
        if (openModal) {
          this.closeModal(openModal);
        }
      }
    });
  }

  // 打开模态框
  openModal(modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // 焦点管理
    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }

  // 关闭模态框
  closeModal(modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }

  // 工具提示
  setupTooltips() {
    document.querySelectorAll('[data-tooltip]').forEach(element => {
      element.addEventListener('mouseenter', (e) => {
        this.showTooltip(e.target);
      });

      element.addEventListener('mouseleave', (e) => {
        this.hideTooltip(e.target);
      });
    });
  }

  // 显示工具提示
  showTooltip(element) {
    const text = element.dataset.tooltip;
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

    element._tooltip = tooltip;
  }

  // 隐藏工具提示
  hideTooltip(element) {
    if (element._tooltip) {
      element._tooltip.remove();
      delete element._tooltip;
    }
  }

  // 懒加载
  setupLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('loading');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => {
      img.classList.add('loading');
      imageObserver.observe(img);
    });
  }

  // 主题切换
  setupThemeToggle() {
    const themeToggle = document.querySelector('.theme-toggle');
    
    themeToggle?.addEventListener('click', () => {
      document.documentElement.classList.toggle('dark');
      
      // 保存主题偏好
      const isDark = document.documentElement.classList.contains('dark');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });

    // 加载保存的主题
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    }
  }

  // 工具方法：防抖
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // 工具方法：节流
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// 初始化主题
document.addEventListener('DOMContentLoaded', () => {
  new ModernTheme();
});

// 导出供其他脚本使用
window.ModernTheme = ModernTheme;
