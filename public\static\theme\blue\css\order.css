.order-list-title {
    max-width: 300px;
    height: 30px;
    line-height: 30px;
    background-color: #3476fe;
    color: #fff;
    font-weight: bold;
    padding: 0 15px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    margin: -35px 0 0 15px;
}
.order-list .h dt {
    margin-bottom: 10px;
}

.order-list .h {
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    padding: 0 15px;
    margin-top: 20px;
    color: #4d4d4d;
}

.order-list .h dl {
    margin-right: 20px;
    padding-bottom: 20px;
}
.order-list .h dt {
    margin-bottom: 10px;
}
.order-list .h dd {
    font-size: 14px;
}
.order-list .h dd a {
    color: #4d4d4d;
}
.order-list-item{
    display: flex;
    background-color: #fafafa;
    margin: 0 15px;
    align-items: center;
    padding: 10px 15px;
    -webkit-box-shadow: 0 2px 2px rgba(156,130,130,.3);
    -moz-box-shadow: 0 2px 2px rgba(156,130,130,.3);
    box-shadow: 0 0 5px rgba(156,130,130,.3);
    margin-bottom: 15px;
    border-radius: 5px;
}
.order-list-item .btn-copy {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    color: #3476fe;
    font-weight: bold;
    background-color: #d9dff5;
    margin-left: 30px;
    padding: 0 20px;
}
.order-list-item .card{
    flex: 1;
    border: none;
    background-color: transparent;
}
.order-list-item dl {
    margin: 10px 0;
}
.order-list-item dd {
    overflow: hidden;
    white-space: nowrap;
    color: #4d4d4d;
}
.order-explain {
    padding-top: 10px;
    margin: 0 15px;
}
.order-explain dt {
    margin-bottom: 10px;
}
.order-user-explain {
    margin: 0 15px;
}
.order-user-explain dt {
    margin-bottom: 15px;
}
.order-user-explain dt {
    margin-top: 20px;
}
.order-user-explain dd {
    color: #33334f;
}
.order-user-explain .btn {
    color: #fff;
    font-weight: bold;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    background-color: #3476fe;
    letter-spacing: 2px;
}
.btn-qq {
    background-color: red;
    color: #fff;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
}
.order-btnbox {
    display: -ms-flexbox;
    display: flex;
    padding: 30px 0px 30px 15px;
    font-size: 0.9rem;
}
.order-btnbox .btn-group {
    display: -ms-flexbox;
    display: flex;
    padding: 0 20px;
    height: 40px;
    align-items: center;
    border: 1px solid #3476fe;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    margin-right: 20px;
    color: #3476fe;
    font-weight: bold;
    cursor: pointer;
}
.order-btnbox .btn-group img {
    margin-right: 10px;
}
.order-modal .modal-dialog {
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0 15px;
}
.order-modal .modal-content {
    width: 100%;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    padding: 30px 40px;
}
.order-modal .modal-header {
    padding: 0;
    border: none;
}
.order-modal .modal-header img {
    width: 33px;
}
.order-modal .modal-header dl {
    margin-left: 15px;
}
.order-modal .modal-header dt {
    font-size: 16px;
    color: #4d4d4d;
    margin-bottom: 5px;
}
.order-modal .modal-header dd {
    font-size: 12px;
    color: #999;
}
.order-modal .modal-header .close {
    padding: 10px 20px 20px;
    margin-top: -20px;
    margin-right: -30px;
    color: #999;
    font-size: 30px;
}
.order-modal .modal-body {
    padding: 20px 0;
}
.order-modal .modal-body .form-control {
    height: 46px;
    line-height: 46px;
    background-color: #fafafa;
    border-color: #e5e5e5;
    outline: none;
    box-shadow: none;
    padding: 0 10px;
    font-size: 12px;
}
.order-modal .modal-footer {
    justify-content: space-between;
    padding: 0;
    border: none;
}
.order-modal .modal-footer .btn {
    flex: 1;
    height: 56px;
    line-height: 56px;
    font-size: 18px;
    color: #666;
    border: 1px solid #e5e5e5;
    padding: 0;
    margin: 0;
    box-shadow: none;
    margin-left: 20px;
}
.order-modal .modal-footer .btn:first-child {
    margin-left: 0;
}
.order-modal .modal-footer .btn-submit {
    background-color: #3476fe;
    color: #fff;
}

/* 鐠併垹宕熼幎鏇＄様 */
.complaint-box{
    background-color: #fff;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    padding:15px;
    box-shadow:0 5px 5px #eeeeee73;
    margin-bottom: 50px;
    margin-top: 90px;
}
.complaint-box .title{
    border-left: 3px solid #3476fe;
    padding-left: 10px;
    margin-bottom: 15px;
}
.complaint-box .form-item{
    display: flex;
    margin-bottom: 15px;
}
.complaint-box .form{
    max-width: 700px;
    margin: 0 auto;
}
.complaint-box .form-item .label{
    margin: 0 10px 0 0;
    flex-shrink: 0;
    line-height: 37px;
    height: 37px;
    user-select: none;
    font-family: '鑼呯閳ユ◤銇㈣В鈧拷';
}
.complaint-box .form-item .input-box input[type="text"]{
    line-height: 37px;
    height: 37px;
    font-size: 14px;
}
.complaint-box .form-item .input-box textarea{
    font-size: 14px;
}
.complaint-box .form-item .input-box{
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.complaint-box .input-box .type-item{
    border: 1px solid #eee;
    display: inline-block;
    padding: 0 10px;
    border-radius: 3px;
    margin: 0 10px 10px 0;
    font-size: 14px;
    line-height: 35px;
    cursor: pointer;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.complaint-box .input-box .type-item input{
    display: none;
}
.complaint-box .input-box .type-item.checked{
    border: 1px solid #3476fe;
}
.complaint-box .input-box .type-item.checked:before{
    content: '';
    position: absolute;
    right: -9px;
    bottom: -5px;
    background-color: #3476fe;
    width: 26px;
    height: 15px;
    transform: rotate(-45deg);
}
.complaint-box .input-box .type-item.checked:after{
    opacity: 1;
    content: '';
    position: absolute;
    width: 5px;
    height: 11px;
    background: transparent;
    bottom: 1px;
    right: 1px;
    border: 2px solid #fff;
    border-top: none;
    border-left: none;
    -webkit-transform: rotate(35deg);
    -moz-transform: rotate(35deg);
    -o-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
}
.complaint-box form [type="submit"]{
    width: 150px;
    display: block;
}

.complaint-box .input-box .btn-experience{
    height: 36px;
    line-height: 36px;
    padding: 0;
    background-color: #3476fe;
    color: #fff;
    -webkit-border-radius: 18px;
    -moz-border-radius: 18px;
    border-radius: 18px;
}
.item-question .img-box{
    max-width: 100%;
    background-color: #fff;
    padding: 5px;
}
.item-question .img-box img{
    max-width: 100%;
    max-height: 100%;
}


.order-massage{
    background-color: #fff;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    border:1px solid #e5e5e5;
    margin-top: 25px;
}
.order-massage .hd{
    line-height:65px;
    border-bottom:1px solid #e5e5e5;
    color:#5375e0;
    text-align:center;
}
.order-massage .complaint-help{
    padding: 0 20px;
}
.order-massage .msg-record{
    width:388px;
    border-left:1px solid #e5e5e5;
    padding:25px 40px;
    display:none;
}
.order-massage .msg-record dl{
    margin-bottom: 30px;
}
.order-massage .msg-record dt{
    color:#b8babf;
    font-weight: normal;
    margin-bottom: 10px;
}
.order-massage .msg-record dd{
    color:#33334f;
}
.order-massage .btns img{
    margin-right: 10px;
}
.order-massage .msg-box{

}

.order-massage .item-answer{
    padding:20px 15px 0;
}
.order-massage .item-answer .answer{
    width:88px;
    height:28px;
    line-height:28px;
    background-color: #5375e0;
    font-size:14px;
    color:#fff;
    text-align: center;
    border-top-left-radius:5px;
    border-top-right-radius:5px;
}
.order-massage .item-answer .answer-content{
    padding: 5px;
}
.order-massage .date{
    font-size:14px;
    color:#c1c1ca;
    padding:10px;
}

.order-massage .item-question{
    padding:20px 15px 0;
    text-align: right;
}
.question-content p{
    display:inline-block;
    max-width:420px;
    line-height:30px;
    color:#fff;
    text-align:left;
    padding:10px 20px;
    background-color: #5375e0;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    border-bottom-right-radius: 0;
}
.question-content .img{
    display:inline-block;
    max-width:420px;
    line-height:30px;
    color:#fff;
    text-align:left;
    padding:10px;
    background-color: #5375e0;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    border-bottom-right-radius: 0;
}

.order-massage .inputbox{
    border-top:1px solid #e5e5e5;
    margin:0 15px;
}
.order-massage .inputbox-hd{
    height:48px;
    display:-ms-flexbox;
    display:flex;
    align-items:center;
}
.order-massage .inputbox-hd span{
    margin-right: 20px;
}
.order-massage .inputbox-bd{
    display:-ms-flexbox;
    display:flex;
    align-items:center;
}
.order-massage .input-content{
    height:120px;
    color:#c1c1ca;
    line-height:30px;
    flex:1;
    -ms-flex:1;
}
.order-massage .btns{
    display:-ms-flexbox;
    display:flex;
    justify-content:center;
    align-items:center;
    width:145px;
    height:44px;
    font-size: 16px;
    color:#fff;
    background-color: #5375e0;
    -webkit-border-radius: 22px;
    -moz-border-radius: 22px;
    border-radius: 22px;
    cursor:pointer;
}


@media (min-width: 992px){
    .order-list-item {
        margin: 0 20px 15px;
    }
    .order-list-item .d-lg-block {
        display: inline-block !important;
    }
    .order-list-item .card {
        flex-direction: row;
    }
    .order-list-item .card dl {
        margin-left: 55px;
    }
    .order-explain {
        margin: 0 20px;
    }
    .order-user-explain {
        margin: 0 20px;
        padding: 20px 0;
    }
    .order-user-explain dl {
        display: -ms-flexbox;
        display: flex;
        align-items: center;
    }
    .order-user-explain dt {
        margin: 5px 0;
    }
    .order-user-explain dd {
        margin-left: 20px;
    }
    .d-lg-none {
        display: none!important;
    }
    .order-user-explain .y {
        display: inline-block !important;
        /* color: #ffc867; */
        margin-left: 20px;
    }
    .order-btnbox {
        justify-content: flex-end;
        padding-top: 66px;
    }
    .order-btnbox .btn-group {
        -webkit-border-radius: 20px;
        -moz-border-radius: 20px;
        border-radius: 20px;
    }
    .order-modal .modal-dialog {
        max-width: 100%;
    }
    .modal-dialog .modal-content {
        max-width: 480px;
        padding: 30px 40px;
    }
    .order-modal .modal-header img {
        width: 43px;
    }
    .order-modal .modal-header dl {
        margin-left: 30px;
    }
    .order-modal .modal-header dt {
        font-size: 22px;
        margin-bottom: 10px;
    }
    .order-modal .modal-header dd {
        font-size: 14px;
    }
    .order-modal .modal-header .close {
        font-size: 30px;
        margin-top: -30px;
        margin-right: -40px;
    }
    .order-modal .modal-body {
        padding: 30px 0;
    }

    .order-massage .bd{
        display:-ms-flexbox;
        display:flex;
    }
    .order-massage .msg-record{
        display: block;
    }
    .order-massage .hd{
        font-size:16px;
        line-height:65px;
        padding-right: 388px;
    }
    .order-massage .item-answer,.order-massage .item-question{
        padding-left: 40px;
        padding-right: 40px;
    }
    .order-massage .inputbox{
        margin:0 40px;
    }
    .msg-box {
        flex: 1;
        -ms-flex: 1;
    }

}