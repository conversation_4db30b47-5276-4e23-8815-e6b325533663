{extend name='./content'}

{block name="button"}
<!--<div class="nowrap pull-right margin-top-10" style="margin-top:10px">
    {if auth("$classuri/del")}
    <button data-update data-field='delete' data-action='{:url("$classuri/del")}' class='layui-btn layui-btn-small layui-btn-danger'>
        <i class='fa fa-remove'></i> 删除日志
    </button>
    {/if}
</div>-->
{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户用户名</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''}" placeholder="请输入商户用户名" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">操作行为</label>
        <div class="layui-input-inline">
            <select name='action' class='layui-select' lay-search="">
                <option value=''> - 所有记录 -</option>
                <!--{foreach $actions as $action}-->
                <!--{if $action===$Think.get.action}-->
                <option selected="selected" value='{$action}'>{$action}</option>
                <!--{else}-->
                <option value='{$action}'>{$action}</option>
                <!--{/if}-->
                <!--{/foreach}-->
            </select>
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">操作内容</label>
        <div class="layui-input-inline">
            <input name="content" value="{$Think.get.content|default=''}" placeholder="请输入操作内容" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">操作时间</label>
        <div class="layui-input-inline">
            <input name="date" id='range-date' value="{$Think.get.date|default=''}"
                   placeholder="请选择操作时间" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <button class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<!-- 表单搜索 结束 -->

<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
        <tr>
<!--            <th class='list-table-check-td'>
                <input data-none-auto="" data-check-target='.list-check-box' type='checkbox'/>
            </th>-->
            <th class='text-center'>商户ID</th>
            <th class='text-center'>商户用户名</th>
            <th class='text-left'>节点</th>
            <th class='text-left'>行为</th>
            <th class='text-left'>操作内容</th>
            <th class='text-left'>操作位置</th>
            <th class='text-left'>操作时间</th>
        </tr>
        </thead>
        <tbody>
        {foreach $list as $key=>$vo}
        <tr>
<!--            <td class='list-table-check-td'>
                <input class="list-check-box" value='{$vo.id}' type='checkbox'/>
            </td>-->
            <td class='text-center'>{$vo.user_id}</td>
            <td class='text-center'>{$vo.username}</td>
            <td class='text-left'>{$vo.node}</td>
            <td class='text-left'>{$vo.action}</td>
            <td class='text-left'>{$vo.content}</td>
            <td class='text-left'>{$vo.isp|default=$vo.ip}</td>
            <td class='text-left'>{$vo.create_at|format_datetime}</td>
        </tr>
        {/foreach}
        </tbody>
    </table>
    {if isset($page)}<p>{$page}</p>{/if}
    <script>
        window.form.render();
        window.laydate.render({range: true, elem: '#range-date', format: 'yyyy/MM/dd'});
    </script>
</form>
{/block}