{extend name='./content'}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户ID</label>
        <div class="layui-input-inline">
            <input name="user_id" value="{$Think.get.user_id|default=''|htmlentities}" placeholder="请输入商户ID" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户账号</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''|htmlentities}" placeholder="请输入商户账号" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商品名</label>
        <div class="layui-input-inline">
            <input name="name" value="{$Think.get.name|default=''|htmlentities}" placeholder="请输入商品名" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select name="status">
                <option value="">全部状态</option>
                <option value="1" {if $Think.get.status === '1'}selected{/if}>已上架</option>
                <option value="0" {if $Think.get.status === '0'}selected{/if}>已下架</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">代理类型</label>
        <div class="layui-input-inline">
            <select name="is_proxy">
                <option value="">全部商品</option>
                <option value="0" {if $Think.get.is_proxy === '1'}selected{/if}>普通商品</option>
                <option value="1" {if $Think.get.is_proxy === '2'}selected{/if}>代理商品</option>

            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">时间范围</label>
        <div class="layui-input-inline">
            <input name="date_range" id="date_range" value="{:urldecode($Think.get.date_range)}" placeholder="请选择时间范围" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<!-- 表单搜索 结束 -->
<div class="layui-form-item layui-inline">
    <button id="heand_del_batch" type="button" class="layui-btn layui-btn-small">批量移动到回收站</button>
</div>
<div class="layui-form-item layui-inline">
    <label class="layui-form-label">统计金额</label>
    <div class="layui-input-inline">
        <input type="text" class="layui-input" value="{$sum_money|default="0"} 元" readonly>
    </div>
</div>
<div class="layui-form-item layui-inline">
    <label class="layui-form-label">商品数</label>
    <div class="layui-input-inline">
        <input type="text" class="layui-input" value="{$sum_order|default='0'} 个" readonly>
    </div>
</div>
<div class="layui-form-item layui-inline">
    <a class="layui-btn  layui-btn-small" data-title="查看商品回收站" data-open="{:url('goodsTrash')}" href="javascript:void(0)">查看商品回收站</a>
</div>
<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
            <tr>
                <th class='list-table-check-td'>
                    <input data-none-auto="" data-check-target='.list-check-box' type='checkbox' />
                </th>
                <th>商户ID</th>
                <th>商户账号</th>
                <th>商品名称</th>
                <th>商品价格</th>
                <th>状态</th>
                <th>冻结状态</th>
                <th>是否代理商品</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $goodsList as $v}
            <tr>
                <td class='list-table-check-td'>
                    <input class="list-check-box" value='{$v.id}' type='checkbox' />
                </td>
                <td>{$v.user_id}</td>
                <td>{$v.user.username}</td>
                <td style="width: 13%;" ><a href="{$v.link}" target="_blank">{$v.name}</a></td>
                <td>{$v.price}</td>
                <td>
                    {if $v.status==1}
                    <span style="color:green"><i class="glyphicon glyphicon-ok"></i> 已上架</span>
                    <a class="btn btn-danger btn-xs text-white" data-tips="确定取消冻结吗？ " data-update="{$v.id}" data-field='status' data-value='0' data-action='{:url("change_status")}'
                       href="javascript:void(0)">下架</a>
                    {else/}
                    <span style="color:red"><i class="glyphicon glyphicon-remove"></i> 已下架</span>
                    <a class="btn btn-warning btn-xs" data-tips="确定冻结吗？" data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("change_status")}'
                       href="javascript:void(0)">上架</a>
                    {/if}
                </td>
                <td>
                    {if $v.is_freeze===0}
                    <span style="color:green"><i class="glyphicon glyphicon-ok"></i> 未冻结</span>
                    <a class="btn btn-xs btn-danger" data-tips="确定取消冻结吗？ " data-update="{$v.id}" data-field='is_freeze' data-value='1' data-action='{:url("change_freeze")}'
                       href="javascript:void(0)">冻结</a>
                    {else/}
                    <span style="color:red"><i class="glyphicon glyphicon-remove"></i> 已冻结</span>
                    <a class="btn btn-warning btn-xs" data-tips="确定冻结吗？" data-update="{$v.id}" data-field='is_freeze' data-value='0' data-action='{:url("change_freeze")}'
                       href="javascript:void(0)">解冻</a>
                    {/if}
                </td>
                <td>
                    {switch name="v.is_proxy"}
                    {case value="0"}<font color="green">否</font>{/case}
                    {case value="1"}<font color="red">是</font>{/case}
                    {default /}
                    {/switch}
                </td>
                <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                <td>
                    <a onclick="del(this, '{$v.id}')">移动到回收站</a>
                    <a href="{:url('manage/user/login',['user_id'=>$v.user_id])}" target="_blank">登录</a>
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //日期范围
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });

    function del(obj, id) {
        layer.confirm('确定要移动到回收站？', function (val) {
            if (val) {
                $.post('/manage/goods/del', {
                    id: id
                }, function (res) {
                    if (res.code != 200) {
                        layer.msg('操作失败');
                    } else {
                        layer.msg('操作成功！', function () {
                            location.reload();
                        });
                    }
                });
            }
        })
    }


    $('#heand_trash_del_batch').click(function () {
        layer.confirm('确定要删除回收站超过15天商品和卡密吗？', function (index) {
            $.ajax({
                url: "/manage/goods/handTrashBatchDel",
                type: 'post',
                success: function (res) {
                    if (res.code == 1) {
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                        layer.msg(res.msg, {icon: 1, time: 1000});
                    } else {
                        layer.msg('操作失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    });


    $('#heand_del_batch').click(function () {
        var ids = '';
        $('.list-check-box').each(function () {
            var _this = $(this);
            if (_this.is(':checked')) {
                ids = ids + _this.val() + ',';
            }
        });
        layer.confirm('确定要批量移动到回收站吗？', function (index) {
            if (ids == '') {
                layer.msg('请选择选项！');
                return false;
            }
            $.ajax({
                url: "/manage/goods/handBatchDel",
                type: 'post',
                data: {
                    'ids': ids,
                },
                success: function (res) {
                    if (res.code == 1) {
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                        layer.msg('操作成功!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('操作失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    });

</script>
{/block}
