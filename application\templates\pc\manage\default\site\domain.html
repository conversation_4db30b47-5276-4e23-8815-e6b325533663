{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' style='padding-top:20px'>

    <div class="form-group">
        <label class="col-sm-2 control-label">主站域名</label>
        <div class='col-sm-8'>
            <input type="text" name="site_domain" required="required" title="请输入主站域名" placeholder="请输入主站域名" value="{:sysconf('site_domain')}" class="layui-input">
            <p class="help-block">主站域名</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">全站资源域名</label>
        <div class='col-sm-8'>
            <input type="text" name="site_domain_res" required="required" title="请输入全站资源域名" placeholder="请输入全站资源域名" value="{:sysconf('site_domain_res')}" class="layui-input">
            <p class="help-block">全站资源域名</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">店铺推广域名</label>
        <div class='col-sm-8'>
            <input type="text" name="site_shop_domain" required="required" title="请输入店铺推广域名" placeholder="请输入店铺推广域名" value="{:sysconf('site_shop_domain')}" class="layui-input">
            <p class="help-block">用于店铺推广使用的域名，防止主域名报毒</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">短网址功能</label>
        <div class='col-sm-8'>
            <select id="site_domain_short" name="site_domain_short" class="layui-input" required>
                <option value="" {if sysconf('site_domain_short')==''}selected{/if}>不启用</option>
                <option value="tbh" {if sysconf('site_domain_short')=='tbh'}selected{/if}>tbh绿标短链接(https//www.tbhwp.com自助开通)</option>
                <option value="Nswmw" {if sysconf('site_domain_short')=='Nswmw'}selected{/if}>Nswmw绿标短链接(新|内部群内申请)</option>
                <option value="Sina" {if sysconf('site_domain_short')=='Sina'}selected{/if}>新浪短网址(老|已失效)</option>
                <option value="Suo" {if sysconf('site_domain_short')=='Suo' }selected{/if}>缩我短网址(新|收费)</option>
                <option value="Baidu" {if sysconf('site_domain_short')=='Baidu'}selected{/if}>百度短网址(老|已失效)</option>
                <option value="NewBaidu" {if sysconf('site_domain_short')=='NewBaidu'}selected{/if}>百度短网址(新|收费)</option>
                <option value="Uomg" {if sysconf('site_domain_short')=='Uomg'}selected{/if}>Uomg短网址(新|免费)</option>
                <option value="UomgVip" {if sysconf('site_domain_short')=='UomgVip'}selected{/if}>Uomg高级短网址(新|收费)</option>
            </select>

        </div>
    </div>


    <div class="short-domain" id="NewBaidu" {if sysconf('site_domain_short')!='NewBaidu'}style="display:none"{/if} >
         <div class="form-group">
            <label class="col-sm-2 control-label">百度短网址配置（TOKEN）</label>
            <div class='col-sm-8'>
                <input type="text" name="short_newbaidu_token" required="required" title="请输入TOKEN" placeholder="请输入TOKEN" value="{:sysconf('short_newbaidu_token')}" class="layui-input">
                <p class="help-block">百度短网址申请网址：https://dwz.cn/</p>
            </div>
        </div>
    </div>

    <div class="short-domain" id="Uomg" {if sysconf('site_domain_short')!='Uomg'}style="display:none"{/if} >

         <div class="form-group">
            <label class="col-sm-2 control-label">Uomg短网址配置（类别）</label>
            <div class='col-sm-8'>
                <input type="text" name="uomg_domain" required="required" title="请输入域名" placeholder="请输入域名" value="{:sysconf('uomg_domain')}" class="layui-input">
                <p class="help-block">tcn | dwzcn | urlcn | suoim | mrwso 中任选其一，不带空格</p>
            </div>
        </div>
    </div>

    <div class="short-domain" id="Sina" {if sysconf('site_domain_short')!='Sina'}style="display:none"{/if}>
         <div class="form-group">
            <label class="col-sm-2 control-label">新浪短网址配置（APP_KEY）</label>
            <div class='col-sm-8'>
                <input type="text" name="sina_app_key" required="required" title="请输入APP_KEY" placeholder="请输入APP_KEY" value="{:sysconf('sina_app_key')}" class="layui-input">
                <p class="help-block"></p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">新浪短网址配置（APP_SECRET）</label>
            <div class='col-sm-8'>
                <input type="text" name="sina_app_secret" required="required" title="请输入APP_SECRET" placeholder="请输入APP_SECRET" value="{:sysconf('sina_app_secret')}" class="layui-input">
                <p class="help-block"></p>
            </div>
        </div>
    </div>
    <div class="short-domain" id="Suo" {if sysconf('site_domain_short')!='Suo'}style="display:none"{/if} >
         <div class="form-group">
            <label class="col-sm-2 control-label">缩我短网址配置（KEY）</label>
            <div class='col-sm-8'>
                <input type="text" name="suo_app_key" required="required" title="请输入KEY" placeholder="请输入KEY" value="{:sysconf('suo_app_key')}" class="layui-input">
                <p class="help-block">缩我短网址申请网址：https://suowo.cn/</p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">缩我短网址配置（独享域名）</label>
            <div class='col-sm-8'>
                <input type="text" name="suo_domain" required="required" title="请输入域名" placeholder="请输入域名" value="{:sysconf('suo_domain')}" class="layui-input">
                <p class="help-block">请填写独享域名</p>
            </div>
        </div>
    </div>

    <div class="short-domain" id="UomgVip" {if sysconf('site_domain_short')!='UomgVip'}style="display:none"{/if} >
         <div class="form-group">
            <label class="col-sm-2 control-label">Uomg高级（TOKEN）</label>
            <div class='col-sm-8'>
                <input type="text" name="short_uomgvip_token" required="required" title="请输入TOKEN" placeholder="请输入TOKEN" value="{:sysconf('short_uomgvip_token')}" class="layui-input">
                <p class="help-block">短网址购买网址：http://check.uomg.com</p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">Uomg高级短网址配置（类别）</label>
            <div class='col-sm-8'>
                <input type="text" name="uomgvip_domain" required="required" title="请输入域名" placeholder="请输入域名" value="{:sysconf('uomgvip_domain')}" class="layui-input">
                <p class="help-block">tcn | urlcn | dwzcn | wurlcn | slink | unu | gitio | qqrand | vxrand | sogou | dwztax 中任选其一，不带空格,购买指定接口前请检查该类别是否停用/是否好用</p>
            </div>
        </div>
    </div>




    <div class="short-domain" id="Nswmw" {if sysconf('site_domain_short')!='Nswmw'}style="display:none"{/if} >
         <div class="form-group">
            <label class="col-sm-2 control-label">Nswmw（TOKEN）</label>
            <div class='col-sm-8'>
                <input type="text" name="nswmw_token" required="required" title="请输入TOKEN" placeholder="请输入TOKEN" value="{:sysconf('nswmw_token')}" class="layui-input">
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">Nswmw（短网址类型）</label>
            <div class='col-sm-8'>
                <input type="text" name="nswmw_type" required="required" title="请输入短网址类型" placeholder="请输入短网址类型" value="{:sysconf('nswmw_type')}" class="layui-input">
                <p class="help-block">短网址类型，默认tcn1，suiji则随机短网址类型</p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">Nswmw（短网址类型）</label>
            <div class='col-sm-8'>
                <input type="text" name="nswmw_pattern" required="required" title="请输入短网址模式" placeholder="请输入短网址模式" value="{:sysconf('nswmw_pattern')}" class="layui-input">
                <p class="help-block">短网址模式（普通：1，防红：2，直链：3, 缩短：4）</p>
            </div>
        </div>
    </div>



    <div class="short-domain" id="tbh" {if sysconf('site_domain_short')!='tbh'}style="display:none"{/if} >
         <div class="form-group">
            <label class="col-sm-2 control-label">tbh（TOKEN）</label>
            <div class='col-sm-8'>
                <input type="text" name="tbh_token" required="required" title="请输入TOKEN" placeholder="请输入TOKEN" value="{:sysconf('tbh_token')}" class="layui-input">
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">tbh（短网址类型）</label>
            <div class='col-sm-8'>
                <input type="text" name="tbh_type" required="required" title="请输入短网址类型" placeholder="请输入短网址类型" value="{:sysconf('tbh_type')}" class="layui-input">
                <p class="help-block">短网址类型，默认tcn，t0fcn是绿标短链，suiji则随机短网址类型</p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">tbh（短网址类型）</label>
            <div class='col-sm-8'>
                <input type="text" name="tbh_pattern" required="required" title="请输入短网址模式" placeholder="请输入短网址模式" value="{:sysconf('tbh_pattern')}" class="layui-input">
                <p class="help-block">短网址模式（普通：1，防红：2，直链：3, 缩短：4）</p>
            </div>
        </div>
    </div>

    <div class="hr-line-dashed"></div>
    <div class="col-sm-4 col-sm-offset-2">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    $(function () {
        $('#site_domain_short').change(function () {
            $('.short-domain').hide();
            var current = $(this).val();
            if (current) {
                $('#' + current).show();
            }
        })
    })
</script>
{/block}
