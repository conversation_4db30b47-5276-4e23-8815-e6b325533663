<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Exception;
use service\UpgradeService;
use think\Cache;
use think\Config;

/**
 * API订单补发任务计划
 */
class Upgrade extends Command {

    protected function configure() {
        $this->setName('Upgrade')->setDescription('手动升级');
    }

    /**
     * 执行命令
     * @param Input  $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output) {
        $version = "";

        if ($version == "") {
            $output->writeln('未设置升级版本号');
        } else {
            define('UPGRADE_PATH', ROOT_PATH . Config::get('cloud.system_dir') . DS);
            try {
                $extend = ['name' => 'system', 'version' => $version];
                UpgradeService::upgrade($extend);
                Cache::rm('__menu__');
                upgradefile(['VERSION' => $version]);
                deldir(UPGRADE_PATH);
                $output->writeln('success');
            } catch (Exception $e) {
                $output->writeln($e->getMessage());
            }
        }
    }

}
