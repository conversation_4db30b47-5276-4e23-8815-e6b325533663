<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">
    <div class="layui-form-item">
        <label class="layui-form-label">置顶服务链接</label>
        <div class="layui-input-block">
            <input  type="text" name="pool_top_link" title="可以输入QQ跳转链接或店铺链接" placeholder="请输入链接" value="{:sysconf('pool_top_link')}" class="layui-input" >
            <p class="help-block">实例1(QQ跳转链接)：http://wpa.qq.com/msgrd?v=3&uin=替换您的QQ&Site=</p>
            <p class="help-block">实例2(店铺链接)：http://www.shop.com/links/xxxx</p>
            <p class="help-block">不填默认平台的QQ跳转链接</p>
        </div>
    </div>

    <div class="hr-line-dashed"></div>

    <div class="layui-form-item text-center">
        <button class="layui-btn" type='submit'>保存</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消吗？" data-close>取消</button>
    </div>

</form>

<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
