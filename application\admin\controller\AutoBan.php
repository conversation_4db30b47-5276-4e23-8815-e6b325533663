<?php

namespace app\admin\controller;

use controller\BasicAdmin;
use service\DataService;
use service\LogService;
use think\Db;

/**
 * 自动封禁管理控制器
 * Class AutoBan
 * @package app\admin\controller
 */
class AutoBan extends BasicAdmin
{
    /**
     * 指定当前数据表
     * @var string
     */
    public $table = 'SystemConfig';

    /**
     * 自动封禁配置页面
     */
    public function index()
    {
        $this->title = '自动封禁配置';
        $autoban_time = sysconf('autoban_time') ?: 0; // 获取自动封禁时间设置，单位：天
        $autoban_status = sysconf('autoban_status') ?: 0; // 获取自动封禁功能开关状态
        
        // 获取白名单
        $whitelist = sysconf('autoban_whitelist') ?: '';
        
        $this->assign('autoban_time', $autoban_time);
        $this->assign('autoban_status', $autoban_status);
        $this->assign('whitelist', $whitelist);
        return $this->fetch();
    }

    /**
     * 保存自动封禁配置
     */
    public function save()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            foreach ($data as $key => $val) {
                sysconf($key, $val);
            }
            LogService::write('系统配置', '修改自动封禁配置成功');
            $this->success('数据修改成功！', '');
        }
        $this->error('访问错误，请重试！');
    }

    /**
     * 执行自动封禁
     */
    public function execute()
    {
        try {
            $data = ['code' => 0, 'msg' => '', 'data' => []];
            
            $autoban_status = sysconf('autoban_status');
            if (empty($autoban_status)) {
                $data['msg'] = '自动封禁功能未开启！';
                return json($data);
            }
            
            $autoban_time = intval(sysconf('autoban_time'));
            if ($autoban_time < 0) {
                $data['msg'] = '自动封禁时间设置错误！';
                return json($data);
            }
            
            // 如果设置为0，表示不限制（不执行自动封禁）
            if ($autoban_time == 0) {
                $data['msg'] = '自动封禁时间设置为0，表示不执行自动封禁！';
                return json($data);
            }
            
            // 获取当前系统时间
            $current_time = time();
            
            // 获取白名单用户
            $whitelist = $this->getWhitelistArray();

            // 使用JOIN查询优化性能，一次性获取用户信息和最后登录时间
            $userQuery = Db::name('user')
                ->alias('u')
                ->field('u.id, u.username, u.is_freeze, u.create_at, l.last_login')
                ->leftJoin([
                    '(SELECT user_id, MAX(create_at) as last_login FROM user_login_log GROUP BY user_id)' => 'l'
                ], 'u.id = l.user_id')
                ->where('u.is_freeze', 0) // 只查询未冻结的用户
                ->select();

            if (empty($userQuery)) {
                // 记录调试信息到日志
                LogService::write('系统配置', '自动封禁：系统中没有有效用户');

                $data['code'] = 1;
                $data['msg'] = '系统检测完成！没有符合条件需要封禁的用户。';
                return json($data);
            }

            // 找出需要封禁的用户
            $users_to_ban = [];
            $skipped_whitelist = 0;
            $never_logged_in = 0;

            foreach ($userQuery as $user) {
                // 检查用户是否在白名单中
                if (in_array($user['username'], $whitelist)) {
                    $skipped_whitelist++;
                    continue;
                }

                // 获取用户最后活动时间（登录时间或注册时间）
                $last_activity_time = null;

                if (!empty($user['last_login'])) {
                    // 有登录记录，使用最后登录时间
                    $last_activity_time = $user['last_login'];
                } else {
                    // 没有登录记录，使用注册时间
                    $last_activity_time = $user['create_at'];
                    $never_logged_in++;
                }

                // 计算距离最后活动的天数
                $days_since_activity = round(($current_time - $last_activity_time) / 86400, 2);

                if ($days_since_activity > $autoban_time) {
                    $users_to_ban[] = $user['id'];
                }
            }
                
            if (empty($users_to_ban)) {
                // 记录调试信息到日志
                $whitelist_msg = $skipped_whitelist > 0 ? "（跳过了{$skipped_whitelist}个白名单用户）" : "";
                LogService::write('系统配置', "自动封禁调试信息：没有符合条件的用户需要封禁{$whitelist_msg}");
                
                $data['code'] = 1;
                $data['msg'] = '系统检测完成！没有符合条件需要封禁的用户。' . $whitelist_msg;
                return json($data);
            }
            
            // 批量更新用户状态为冻结
            $update = Db::name('user')->where('id', 'in', $users_to_ban)->update(['is_freeze' => 1]);
            
            // 记录调试信息到日志
            LogService::write('系统配置', "自动封禁执行结果：" . ($update !== false ? "成功" : "失败"));
            
            if ($update !== false) {
                $count = count($users_to_ban);
                $whitelist_msg = $skipped_whitelist > 0 ? "（跳过了{$skipped_whitelist}个白名单用户）" : "";
                LogService::write('系统配置', "自动封禁成功，共封禁了{$count}个用户{$whitelist_msg}");
                
                $data['code'] = 1;
                $data['msg'] = "自动封禁执行成功！系统共封禁了{$count}个长期未登录的用户。{$whitelist_msg}";
            } else {
                $data['msg'] = '自动封禁执行失败，请检查系统日志并重试！';
            }
            
            return json($data);
            
        } catch (\Exception $e) {
            LogService::write('系统配置', "自动封禁出错：" . $e->getMessage());
            return json(['code' => 0, 'msg' => '执行出错：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取白名单用户数组
     * @return array 白名单用户名数组
     */
    protected function getWhitelistArray()
    {
        $whitelist_str = sysconf('autoban_whitelist') ?: '';
        if (empty($whitelist_str)) {
            return [];
        }
        
        // 分割白名单字符串为数组，支持多种分隔符(逗号、分号、换行符)
        $whitelist = preg_split('/[,;\r\n]+/', $whitelist_str);
        // 移除空白元素和去除每项的首尾空格
        $whitelist = array_filter(array_map('trim', $whitelist));
        
        return $whitelist;
    }
} 