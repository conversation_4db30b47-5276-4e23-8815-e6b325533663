<?php
namespace app\pay\controller;
use service\PayService;

class YuxuanPay extends PayService {
    protected $gateway='https://gateway.tonzen.cn/Pay';

    public function pay($trade_no, $subject, $totalAmount) {
        $order = $this->loadOrder($trade_no);
        $data=array(
            'appid'=>$order->channelAccount->params->appid,
            'bankcode'=>$order->channelAccount->params->bankcode,
            'timestamp'=>date('Y-m-d H:i:s'),
            'out_trade_no'=>$trade_no,
            'goods_name'=>$subject,
            'total_amount'=>$totalAmount,
            'attach'=>'online_pay',
            'notifyurl'=>url("pay/YuxuanPay/notify"),
            'callbackurl'=>url("pay/YuxuanPay/callback"),
        );
		
        $data['sign']=$this->_sign($data,$order->channelAccount->params->key);
        $data['return_type']='json';
        $data['sign_type']='MD5';
        record_file_log('YuxuanPay',json_encode($data));

        $http = new Http($this->gateway.'/index', $data);
        $http->toUrl();
        $result=$http->getResContent();
        record_file_log('YuxuanPay',$result);

        $ret = json_decode($result,true);
        if($ret['code'] == "1000" ){
            return $this->qrcode($ret['data']['payurl']);
        }
        $error = isset($ret['msg']) ? $ret['msg'] : '';
        echo '获取支付信息失败!' . $error;
    }

    /**
     * 页面回调
     */
    public function callback() {
     $out_trade_no = input("out_trade_no");
        header("Location:" . url('index/Pay/pay_result', ['orderid' => $out_trade_no]));
        die;
    }

    /**
     * 服务器回调
     */
    public function notify() {
        $ret = input('');
        record_file_log('YuxuanPay',json_encode($ret));
        $order = $this->loadOrder($ret['out_trade_no']);

        unset($ret['sign_type']);
        $sign=$this->_sign($ret,$order->channelAccount->params->key);
        if($sign == $ret['sign']) {
            $order->transaction_id = $ret['trade_no'];
            $this->completeOrder($order);
            exit('success');
        } else {
            exit('fail');
        }
    }

    public function _sign($data,$userkey){
        ksort($data);
        $signstr='';
        foreach($data as $key=>$val){
            if($key!='sign' && $val!==''){
                $signstr.=$signstr ? '&' : '';
                $signstr.=$key.'='.$val;
            }
        }
        $signstr.='&key='.$userkey;
        $sign=strtoupper(md5($signstr));
        return $sign;
    }
}
class Http{private $resCode;private $errInfo;private $resContent;private $header=array();private $buildData=array();function __construct($url,$data,$timeout=10){$this->url=$url;$this->data=$data;$this->timeout=$timeout;}public function toUrl(){$ch = curl_init();curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,false);curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,false);curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);curl_setopt($ch, CURLOPT_POST, 1);curl_setopt($ch, CURLOPT_URL, $this->url);curl_setopt($ch, CURLOPT_HTTPHEADER,$this->getHeader());curl_setopt($ch,CURLOPT_USERAGENT,'Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:*******) Gecko/2008092417 Firefox/3.0.3');curl_setopt($ch, CURLOPT_POSTFIELDS,$this->getBuild());$res = curl_exec($ch);$this->resCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);if ($res == NULL) {$this->errInfo = "call http err :" . curl_errno($ch) . " - " . curl_error($ch) ;curl_close($ch);return false;} else if($this->resCode != "200") {$this->errInfo = "call http err httpcode=" . $this->resCode  ;curl_close($ch);return false;}curl_close($ch);$this->resContent = $res;return true;}public function getResContent(){return $this->resContent;}public function setHeader($head='form'){switch($head){case 'form':$h='Content-Type:application/x-www-form-urlencoded'; break;case 'json':$h='Content-Type:application/json'; break;case 'xml':$h='Content-Type:application/xml'; break;case 'text':$h='Content-Type:text/plain'; break;case 'html':$h='Content-Type:text/html'; break;default:$h='Content-Type:application/x-www-form-urlencoded';}$this->header=array($h.';charset=utf-8');return $this->header;}private function getHeader(){return $this->header ? $this->header : $this->setHeader();}public function setBuild($build=true){$this->buildData=$build ? http_build_query($this->data) : $this->data;return $this->buildData;}private function getBuild(){return $this->buildData ? $this->buildData : $this->setBuild();}public function getResCode(){return $this->resCode;}public function getErrInfo(){return $this->errInfo;}}
?>
