<div class="framework-topbar">
    <div class="console-topbar">
        <div class="topbar-wrap topbar-clearfix">
            <div class="topbar-head topbar-left">
                <a href="{:url('@admin')}" class="topbar-logo topbar-left">
                    <span class="icon-logo">
                        {:sysconf('app_name')} <sup>{:getVersion()}</sup>
                    </span>
                </a>
            </div>
            {volist name='menus' id='pmenu'}
            {empty name='pmenu.sub'}
            <a data-menu-node='m-{$pmenu.id}' data-open="{$pmenu.url}"
               class="topbar-home-link topbar-btn topbar-left">
                <span>{notempty name='$pmenu.icon'}<i class="{$pmenu.icon}"></i>{/notempty} {$pmenu.title}</span>
            </a>
            {else}
            <a data-menu-target='m-{$pmenu.id}' class="topbar-home-link topbar-btn topbar-left">
                <span>{notempty name='$pmenu.icon'}<i class="{$pmenu.icon}"></i>{/notempty} {$pmenu.title}</span>
            </a>
            {/empty}
            {/volist}
            <div class="topbar-info topbar-right">

                <div class="topbar-left topbar-user">
                    {if session('user')}
                    <div class="dropdown topbar-info-item">
                        <a href="#" class="dropdown-toggle topbar-btn text-center" data-toggle="dropdown">
                            <span class='glyphicon glyphicon-bell'></span> 待处理事件</span>
                            <span class="glyphicon glyphicon-menu-up transition" style="font-size:12px"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/complaint/index',['status'=>0])}">
                                    <span>投诉待处理 
                                        {if $peddingtask.complaint_count>0}
                                        <b style="color:red">{$peddingtask.complaint_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.complaint_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/cash/index',['status'=>0])}">
                                    <span>结算待审核 
                                        {if $peddingtask.cash_count>0}
                                        <b style="color:red">{$peddingtask.cash_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.cash_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/user/index',['status'=>0])}">
                                    <span>商户待审核 
                                        {if $peddingtask.user_count>0}
                                        <b style="color:red">{$peddingtask.user_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.user_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/goods/pool_list',['status'=>0])}">
                                    <span>商品池待审核 
                                        {if $peddingtask.goodspool_count>0}
                                        <b style="color:red">{$peddingtask.goodspool_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.goodspool_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/plugin/poolauth',['status'=>0])}">
                                    <span>浏览全网通申请
                                        {if $peddingtask.pluginpool_count>0}
                                        <b style="color:red">{$peddingtask.pluginpool_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.pluginpool_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/plugin/crossauth',['status'=>0])}">
                                    <span>使用跨平台申请 
                                        {if $peddingtask.plugincross_count>0}
                                        <b style="color:red">{$peddingtask.plugincross_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.plugincross_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>

                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/plugin/custompayauth',['status'=>0])}">
                                    <span>使用自定义支付申请 
                                        {if $peddingtask.custompay_count>0}
                                        <b style="color:red">{$peddingtask.custompay_count}</b>
                                        {else/}
                                        <b style="color:green">{$peddingtask.custompay_count}</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>

                            <li class="topbar-info-btn">
                                <a data-load="{:url('manage/article/index')}">
                                    <span>今日结算公告 
                                        {if $peddingtask.settlement_count>0}
                                        <b style="color:green">已发布</b>
                                        {else/}
                                        <b style="color:red">未发布</b>
                                        {/if}
                                    </span>
                                </a>
                            </li>

                        </ul>
                    </div>
                    {else}
                    <div class=" topbar-info-item">
                        <a data-href="{:url('@admin/login')}" data-toggle="dropdown" class=" topbar-btn text-center">
                            <span class='glyphicon glyphicon-user'></span> 立即登录 </span>
                        </a>
                    </div>
                    {/if}
                </div>

                <a data-reload data-tips-text='刷新' style='width:50px'
                   class=" topbar-btn topbar-left topbar-info-item text-center">
                    <span class='glyphicon glyphicon-refresh'></span>
                </a>

                <a href="/index.php" target="_blank" class="topbar-btn topbar-left topbar-info-item text-center">
                    首页
                </a>

                <a data-tips-text='清除缓存'  data-load="{:url('manage/site/clearcache')}" data-confirm='确定要清除缓存吗？'
                   class=" topbar-btn topbar-left topbar-info-item text-center">
                    清除缓存
                </a>
                <div class="topbar-left topbar-user">
                    {if session('user')}
                    <div class="dropdown topbar-info-item">
                        <a href="#" class="dropdown-toggle topbar-btn text-center" data-toggle="dropdown">
                            <span class='glyphicon glyphicon-user'></span> {:session('user.username')} </span>
                            <span class="glyphicon glyphicon-menu-up transition" style="font-size:12px"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="topbar-info-btn">
                                <a data-modal="{:url('admin/index/pass')}?id={:session('user.id')}">
                                    <span><i class='glyphicon glyphicon-lock'></i> 修改密码</span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-modal="{:url('admin/index/info')}?id={:session('user.id')}">
                                    <span><i class='glyphicon glyphicon-edit'></i> 修改资料</span>
                                </a>
                            </li>
                            <li class="topbar-info-btn">
                                <a data-load="{:url('admin/login/out')}" data-confirm='确定要退出登录吗？'>
                                    <span><i class="glyphicon glyphicon-log-out"></i> 退出登录</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    {else}
                    <div class=" topbar-info-item">
                        <a data-href="{:url('@admin/login')}" data-toggle="dropdown" class=" topbar-btn text-center">
                            <span class='glyphicon glyphicon-user'></span> 立即登录 </span>
                        </a>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>