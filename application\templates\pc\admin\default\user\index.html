{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-modal='{:url("$classuri/add")}' data-title="添加用户" class='layui-btn layui-btn-small'>
        <i class='fa fa-plus'></i> 添加用户
    </button>
    <button data-update data-field='delete' data-action='{:url("$classuri/del")}' class='layui-btn layui-btn-small layui-btn-danger'>
        <i class='fa fa-remove'></i> 删除用户
    </button>
</div>
{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">用户名</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''}" placeholder="请输入用户名" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">手机号</label>
        <div class="layui-input-inline">
            <input name="phone" value="{$Think.get.phone|default=''}" placeholder="请输入手机号" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">电子邮箱</label>
        <div class="layui-input-inline">
            <input name="mail" value="{$Think.get.mail|default=''}" placeholder="请输入电子邮箱" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">登录时间</label>
        <div class="layui-input-inline">
            <input name="date" id='range-date' value="{$Think.get.date|default=''}"
                   placeholder="请选择登录时间" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <button class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<!-- 表单搜索 结束 -->

<form onsubmit="return false;" data-auto="true" method="post">
    {if empty($list)}
    <p class="help-block text-center well">没 有 记 录 哦！</p>
    {else}
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
            <tr>
                <th class='list-table-check-td'>
                    <input data-none-auto="" data-check-target='.list-check-box' type='checkbox'/>
                </th>
                <th class='text-left nowrap'>用户名</th>
                <th class='text-left nowrap'>手机号</th>
                <th class='text-left nowrap'>电子邮箱</th>
                <th class='text-left nowrap'>登录次数</th>
                <th class='text-left nowrap'>最后登录</th>
                <th class='text-left nowrap'>状态</th>
                <th class='text-left nowrap'>操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $key=>$vo}
            <tr>
                <td class='list-table-check-td'>
                    <input class="list-check-box" value='{$vo.id}' type='checkbox'/>
                </td>
                <td class='text-left nowrap'>
                    {$vo.username}
                </td>
                <td class='text-left nowrap'>
                    {$vo.phone|default="<span class='color-desc'>还没有设置手机号</span>"}
                </td>
                <td class='text-left nowrap'>
                    {$vo.mail|default="<span class='color-desc'>还没有设置邮箱</span>"}
                </td>
                <td class='text-left nowrap'>
                    {$vo.login_num|default="<span class='color-desc'>从未登录</span>"}
                </td>
                <td class='text-left nowrap'>
                    {$vo.login_at|format_datetime|default="<span class='color-desc'>从未登录</span>"}
                </td>
                <td class='text-left nowrap'>
                    {if $vo.status eq 0}
                    <span>已禁用</span>
                    {elseif $vo.status eq 1}
                    <span style="color:#090">使用中</span>
                    {/if}
                </td>
                <td class='text-left nowrap'>
                    {if auth("$classuri/edit")}
                    <span class="text-explode">|</span>
                    <a data-modal='{:url("$classuri/edit")}?id={$vo.id}' href="javascript:void(0)">编辑</a>
                    {/if}
                    {if auth("$classuri/auth")}
                    <span class="text-explode">|</span>
                    <a data-modal='{:url("$classuri/auth")}?id={$vo.id}' href="javascript:void(0)">授权</a>
                    {/if}
                    {if auth("$classuri/pass")}
                    <span class="text-explode">|</span>
                    <a data-modal='{:url("$classuri/pass")}?id={$vo.id}' href="javascript:void(0)">密码</a>
                    {/if}
                    {if $vo.status eq 1 and auth("$classuri/forbid")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='status' data-value='0' data-action='{:url("$classuri/forbid")}'
                       href="javascript:void(0)">禁用</a>
                    {elseif auth("$classuri/resume")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='status' data-value='1' data-action='{:url("$classuri/resume")}'
                       href="javascript:void(0)">启用</a>
                    {/if}
                    {if auth("$classuri/del")}
                    <span class="text-explode">|</span>
                    <a data-update="{$vo.id}" data-field='delete' data-action='{:url("$classuri/del")}'
                       href="javascript:void(0)">删除</a>
                    {/if}
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
    {if isset($page)}<p>{$page}</p>{/if}
    {/if}
    <script>
        window.laydate.render({range: true, elem: '#range-date', format: 'yyyy/MM/dd'});
    </script>
</form>
{/block}