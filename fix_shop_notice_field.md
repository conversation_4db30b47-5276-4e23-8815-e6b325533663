# 修复 shop_notice 字段长度问题

## 问题描述
当商家公告内容超过200个字符时，会出现以下错误：
```
SQLSTATE[22001]: String data, right truncated: 1406 Data too long for column 'shop_notice' at row 1
```

## 解决方案

### 方法一：直接执行 SQL 命令（推荐）

1. 登录到你的数据库管理工具（如 phpMyAdmin、Navicat 等）
2. 选择你的数据库
3. 执行以下 SQL 命令：

```sql
ALTER TABLE `user` MODIFY COLUMN `shop_notice` TEXT COMMENT '公告通知';
```

### 方法二：使用命令行

如果你有数据库命令行访问权限：

```bash
mysql -u 用户名 -p 数据库名
```

然后执行：
```sql
ALTER TABLE `user` MODIFY COLUMN `shop_notice` TEXT COMMENT '公告通知';
```

### 方法三：使用提供的 PHP 脚本

在项目根目录下运行：
```bash
php migrate_shop_notice.php
```

## 验证修改

执行以下 SQL 查看字段类型是否修改成功：
```sql
DESCRIBE user;
```

在结果中找到 `shop_notice` 字段，确认 `Type` 列显示为 `text`。

## 修改说明

- **修改前**: `varchar(200)` - 最多200个字符
- **修改后**: `text` - 最多65,535个字符
- **影响**: 现有数据不会丢失，只是扩展了字段容量

## 注意事项

1. **生产环境**: 建议在执行前备份数据库
2. **应用重启**: 修改后可能需要清除应用缓存或重启应用
3. **测试**: 修改后请测试商家公告功能是否正常

## 备份命令（可选）

在执行修改前，可以先备份 user 表：
```sql
CREATE TABLE user_backup_20250807 AS SELECT * FROM user;
```

修改完成后，如果一切正常，可以删除备份表：
```sql
DROP TABLE user_backup_20250807;
```
