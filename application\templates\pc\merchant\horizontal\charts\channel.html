{extend name="base"}


{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-auto">

                                <form class="form-inline" role="form" action="" method="get">

                                    <div class="form-group">
                                        <input class="form-control input-daterange-datepicker"  type="text" name="date_range" value="{:urldecode($Think.get.date_range)}" placeholder="点击选择查询日期">
                                    </div>

                                    <button type="submit" class="btn btn-light waves-effect waves-light ml-1"><i class="bx bx-search mr-1"></i>搜索</button>
                                </form>
                            </div>
                            <div class="col-sm-auto">
                                <a href="?date_range={:urldecode($Think.get.date_range)}&action=dump" class="pull-right btn btn-success waves-effect waves-light"><i class='bx bx-export   mr-1'></i>批量导出</a>
                            </div>
                        </div>


                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>支付方式</th>
                                        <th>提交订单数</th>
                                        <th>已付订单数</th>
                                        <th>未付订单数</th>
                                        <th>订单总金额</th>
                                        <th>订单实收金额</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    {foreach $statis as $v}
                                    <tr>
                                        <th>{$v.title}</th>
                                        <td>{$v.count}</td>
                                        <td>{$v.paid}</td>
                                        <td>{$v.unpaid}</td>
                                        <td>{$v.sum_money}</td>
                                        <td>{$v.sum_actual_money}</td>
                                    </tr>
                                    {/foreach}
                                    <tr>
                                        <th>合计</th>
                                        <td>{$counts.count}</td>
                                        <td>{$counts.paid}</td>
                                        <td>{$counts.unpaid}</td>
                                        <td>{$counts.sum_money}</td>
                                        <td>{$counts.sum_actual_money}</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>

                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}
<script>

</script>
{/block}
