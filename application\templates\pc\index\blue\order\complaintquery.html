<!DOCTYPE html>
<html lang="zh">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>投诉查询</title>
    <link rel="stylesheet" href="__RES__/theme/blue/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/style.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/response.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/order.css">
	<style>
	    .no-pay{background-color:#999 !important}
	    .no-order {text-align: center;color: #999;padding: 20px 0;background-color: #fff;}
		.order-search .title .search-btn{padding: .1rem .25rem;border: .1rem solid #e1ebff;background-color: #3476fe;border-radius: 25px;color: #fff;cursor: pointer;margin-right: .6rem;}
		.order-search .title .active{opacity: 1;}
		@media (max-width: 768px){.order-search .title .search-btn{margin-right: .2rem;}}
	</style>
</head>
<body>
<header class="header query-header">
    <div class="bgimg"><img src="__RES__/theme/blue/picture/header_bg.png" alt=""></div>
    <div class="container">
{include file="./default_header"}
        <div class="banner">
            <div class="text-introduce">
                <div class="h1">轻松查询投诉，保障您的消费权益</div>
            </div>
            <div class="img"><img src="__RES__/theme/blue/picture/banner_query_img.png" alt=""></div>
        </div>
    </div>
</header>
<div class="query">
    <div class="container" style="padding-bottom: .5rem;">
        <div class="order-search" style="padding-bottom: 30px;">
            <div class="order-search-box">
                <div class="title">
                    <span class="search-btn " onclick="window.location.href='/orderquery'">订单查询</span>
                    <span class="search-btn active">投诉查询</span>
                    <span class="search-btn" onclick="window.location.href='/complaint'">订单投诉</span>
                </div>
                <form name='report' action="{:url('Index/order/complaintPass')}" method='get'  onsubmit="return checker()" class="search">
                    <input name="trade_no" type="text" value="{$Request.get.id}"  required="" placeholder="请输入订单编号" class="search-text">
                    <input type="hidden" name="token" value="{$token}">  
    
                    <input type="submit" value="查询订单" class="btn-search">
                </form>
                <div class="statement">
                    <img src="__RES__/theme/blue/picture/query_light.png" alt="">
                    <span>温馨提示：</span>
                    <p>如果您对订单存在疑惑，请在购买当天23：30前向平台客服反馈，逾期请自行与卖家协商解决，平台将于24h内处理您的订单投诉。</p><br><br>
                     <div class="statement">
                    <img src="__RES__/theme/blue/picture/query_light.png" alt="">
                    <span>免责声明：</span>
                    <p>平台为次日结算，款项结算给商户后所出现的售后问题请自行与卖家协商。订单投诉：通过订单号查询订单，可在【订单投诉】等待平台处理。</p>
                </div>
            </div>
        </div>
    </div>
</div>

{include file="./default_footer"}

</div>


</body>
</html>