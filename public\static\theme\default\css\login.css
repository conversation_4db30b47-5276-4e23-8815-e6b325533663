@charset "UTF-8";

html, body {height:100%;width:100%;overflow:hidden!important;overflow-y:hidden !important;min-width:0}

.login-container {background-color:#2494F2;overflow:hidden;font-family:'微软雅黑', serif;margin:0 auto;min-width:1000px;font-size:9pt}
.login-container ul {display:block;margin:0;padding:0;list-style:none}
.login-container ul li {display:block;margin:0;padding:0;list-style:none}
.login-container img {border:0}
.login-container .clouds-container{position:absolute;overflow:hidden;height:100%;width:100%;}
.login-container .clouds {background:url("../img/login/cloud_three.png") repeat-x left 15%;position:absolute;left:0;top:0;height:100%;width:300%;-webkit-animation:cloudmove 200s linear infinite;-moz-animation:cloudmove 200s linear infinite;-o-animation:cloudmove 200s linear infinite;animation:cloudmove 200s linear infinite;-webkit-transform:translate3d(0, 0, 0);-ms-transform:translate3d(0, 0, 0);-o-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0)}
.login-container .clouds-fast {background:url("../img/login/cloud.png") no-repeat 0px 40%;-webkit-animation:cloudmove 30s linear infinite;-moz-animation:cloudmove 30s linear infinite;-o-animation:cloudmove 30s linear infinite;animation:cloudmove 30s linear infinite}
.login-container .clouds-footer {background:url("../img/login/cloud_one.png") no-repeat left 100%;animation:none}

@-webkit-keyframes cloudmove {0% {left:-50%}100% {left:150%}}
@-moz-keyframes cloudmove {0% {left:-50%}100% {left:150%}}
@keyframes cloudmove {0% {left:-50%}100% {left:150%}}

.login-container a,
.login-container a:hover {color:#00a4ac;text-decoration:none}
.login-container a:focus {text-decoration:none;color:#000;outline:none;blur:expression(this.onFocus=this.blur())}
.login-container .header {height:47px;position:absolute;position:fixed;top:0;z-index:100;width:100%}
.login-container .header span.title {line-height:47px;text-indent:44px;float:left;font-family:"Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "\9ED1\4F53", Arial, sans-serif;color:#fff;font-size:16px;letter-spacing:0.5px}
.login-container .header ul {float:right;padding-right:30px}
.login-container .header ul li {float:left;margin-left:20px;line-height:47px;letter-spacing:0.5px}
.login-container .header ul li a {color:#fff}
.login-container .header ul li a:hover {color:#fff;}
.login-container .container {background:url(../img/login/loginbg3.png) no-repeat center center;width:100%;height:585px;overflow:hidden;position:relative;top:47px}

/* 登录表单 */
.login-container .content {width:692px;height:336px;background:url(../img/login/logininfo.png) no-repeat;margin:80px auto auto auto;position:relative;display:block}
.login-container .content ul {margin-left:285px;margin-right:60px}
.login-container .content ul li {position:relative}
.login-container .content ul li input {margin-bottom:15px;position:relative;background:url(../img/icon.png) no-repeat;background-size:20px;padding-left:38px}
.login-container .content ul li input.username {background-position:8px 7px}
.login-container .content ul li input.password {background-position:8px -20px}
.login-container .content ul li input.captcha {background-position:8px -50px;width:60%;float:left;}
.login-container .login-input {display:block;width:100%;padding-left:10px;height:38px;line-height:38px;line-height:36px \9;border:1px solid #e6e6e6;background-color:#fff;border-radius:2px}
.login-container .captcha_img {float:right;width:120px;cursor: pointer;}
/* 头像 */
.login-container .people {width:165px;height:96px;position:relative;top:8px;left:-70px}
.login-container .people .tou {background:url("../img/login/tou.png") no-repeat;width:97px;height:92px;position:absolute;top:-87px;left:140px}
.login-container .people .left_hand {background:url("../img/login/left_hand.png") no-repeat;width:32px;height:37px;position:absolute;top:-38px;left:150px}
.login-container .people .right_hand {background:url("../img/login/right_hand.png") no-repeat;width:32px;height:37px;position:absolute;top:-38px;right:-64px}
.login-container .people .initial_left_hand {background:url("../img/login/hand.png") no-repeat;width:30px;height:20px;position:absolute;top:-12px;left:100px}
.login-container .people .initial_right_hand {background:url("../img/login/hand.png") no-repeat;width:30px;height:20px;position:absolute;top:-12px;right:-112px}

/* 底部版权 */
.login-container .footer {height:50px;line-height:50px;text-align:center;position:fixed;_position:absolute;*position:absolute;bottom:0;width:100%;color:#fff}
.login-container .footer a {font-weight:bold;color:#0b3a58;letter-spacing:0.5px}
.login-container .footer a:hover {color:#fff}
