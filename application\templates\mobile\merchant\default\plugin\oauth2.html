{extend name="simple_base"}
{block name="css"}
<link href="__RES__/merchant/default/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />
{/block}
{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body  p-3">

            <div class='row'>
                {if plugconf("oauth2", "qq_open_merchant")==1}
                <div class="col-md-12 mb-2">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">绑定QQ</h6>
                            <p class="card-title-desc">绑定后，可以使用QQ快速登录</p>
                            <div class="text-center">
                                {if $_user.oauth2_qq_openid==''}
                                <a href="{:url('plugin/bindOauth2',['type'=>'qq'])}" target="_blank" class="btn btn-primary btn-sm waves-effect waves-light">立即绑定</a>
                                {else/}
                                <p><span class="badge badge-pill bg-success">已绑定</span></p>
                                <button onclick="unbind('qq')"  class="btn btn-danger btn-sm waves-effect waves-light">解除绑定</button>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
                {/if}
                {if plugconf("oauth2", "wechat_open_merchant")==1}
                <div class="col-md-12 mb-2">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">绑定微信</h6>
                            <p class="card-title-desc">绑定后，可以使用微信快速登录</p>
                            <div class="text-center">
                                {if $_user.oauth2_wechat_openid==''}
                                <a href="{:url('plugin/bindOauth2',['type'=>'wechat'])}" target="_blank" class="btn btn-primary btn-sm waves-effect waves-light">立即绑定</a>
                                {else/}
                                <p><span class="badge badge-pill bg-success">已绑定</span></p>
                                <button onclick="unbind('wechat')"  class="btn btn-danger btn-sm waves-effect waves-light">解除绑定</button>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
                {/if}
            </div>

        </div>
    </div>
</div>

{/block}
{block name="js"}
<script>

    function unbind(type)
    {
        $.confirm({
            title: '提示！',
            content: '确定要解除绑定吗？',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('plugin/unbindOauth2')}", {type: type
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert("解绑失败");
                            } else {
                                $.alert(res.msg);
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }


</script>

{/block}

