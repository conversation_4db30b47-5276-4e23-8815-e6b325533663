{extend name="./content"}

{block name="content"}

<div class="row" style='margin-top:16px'>
    <div class="col-sm-8 col-sm-offset-2">
        <div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
            <p style="font-size:13px">功能介绍：开启后买家关注微信后会自动回复系统智能菜单，示例：</p>
            <p style="font-size:13px">👉 回复“1” 查询最近订单</p>
            <p style="font-size:13px">👉 回复“2” 查询最近购买店铺</p>
            <p style="font-size:13px;color:red">提示：需要配合开启买家黑名单收集买家微信信息，关键词“1”,“2”不要与系统微信关键词重复</p>
        </div>
    </div>
</div>

<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >

    <div class="form-group">
        <label class="col-sm-2 control-label">功能是否开启</label>
        <div class='col-sm-8'>
            <select name="status" class="layui-input" >
                <option value="0" {if plugconf('buyerweixin','status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('buyerweixin','status')=='1'}selected{/if}>开启</option>
            </select>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>

<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });

</script>
{/block}