
<!DOCTYPE html>
<html class="no-js" lang="en">

    <head>
        <meta charset="utf-8" />
        <title>订单查询 - {:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- Bootstrap -->
        <link href="/static/theme/landrick/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <!-- Icons -->
        <link href="/static/theme/landrick/css/materialdesignicons.min.css" rel="stylesheet" type="text/css" />
        <!-- Slider -->               
        <link rel="stylesheet" href="/static/theme/landrick/css/owl.carousel.min.css"/> 
        <link rel="stylesheet" href="/static/theme/landrick/css/owl.theme.default.min.css"/> 
        <!-- Main Css -->
        <link href="/static/theme/landrick/css/land1.css" rel="stylesheet" type="text/css" id="theme-opt" />
        <link href="/static/theme/landrick/css/colors/default.css" rel="stylesheet" id="color-opt">
        <script src="/static/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <link href="/static/plugs/clicaptcha/css/captcha.css" rel="stylesheet" id="color-opt">



        <link rel="stylesheet" href="__RES__/theme/box/css/fontawesome.min.css">
        <link rel="stylesheet" href="__RES__/theme/box/css/bootstrap.min.css">
        <link rel="stylesheet" href="__RES__/theme/box/css/slick.css">
        <link rel="stylesheet" href="__RES__/theme/box/css/style1.css">
    </head>

    <body class="cs-dark">

        <div class="cs-preloader cs-center">
            <div class="cs-preloader_in"></div>
            <span>Loading</span>
        </div>

        <header class="cs-site_header cs-style1 cs-sticky-header cs-white_bg">
            <div class="cs-main_header">
                <div class="container-fluid">
                    <div class="cs-main_header_in">
                        <div class="cs-main_header_left">
                            <a href="/" rel="home" class="main-logo">
                                <img id="logo_header" src="{:sysconf('site_logo')}" alt="nft-gaming" width="133" height="56" data-retina="{:sysconf('site_logo')}" data-width="133" data-height="56">
                            </a>
                        </div>
                        <div class="cs-main_header_right">
                            <div class="cs-search_wrap">
                                <form action="/orderquery" class="cs-search">
                                    <input type="hidden" name="querytype" value="2" />
                                    <input type="text" name="orderid" class="cs-search_input" placeholder="输入订单号">
                                    <button class="cs-search_btn">
                                        <svg width="20" height="21" viewbox="0 0 20 21" fill="none" xmlns="https://www.w3.org/2000/svg">
                                        <path d="M9.16667 16.3333C12.8486 16.3333 15.8333 13.3486 15.8333 9.66667C15.8333 5.98477 12.8486 3 9.16667 3C5.48477 3 2.5 5.98477 2.5 9.66667C2.5 13.3486 5.48477 16.3333 9.16667 16.3333Z" stroke="currentColor" stroke-opacity="0.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M17.5 18L13.875 14.375" stroke="currentColor" stroke-opacity="0.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>                  
                                    </button>
                                </form>
                            </div>
                            <div class="cs-nav_wrap">
                                <div class="cs-nav_out">
                                    <div class="cs-nav_in">
                                        <div class="cs-nav">
                                            <ul class="cs-nav_list">
                                                <li class="menu-item-has-children">
                                                    <a href="/">平台首页</a>

                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a>订单处理</a>
                                                    <ul>
                                                        <li><a href="/orderquery">订单查询</a></li>
                                                        <li><a href="/complaint">订单投诉</a></li>
                                                        <li><a href="/complaintquery">投诉查询</a></li>

                                                    </ul>
                                                </li>
                                                <li><a href="/company/faq">常见问题</a></li>
                                                <li class="menu-item-has-children">
                                                    <a >官方动态</a>
                                                    <ul>
                                                        <li><a href="/company/notice">平台公告</a></li>
                                                        <li><a href="/company/news">新闻动态</a></li>
                                                        <li><a href="/company/settlement">平台结算</a></li>
                                                    </ul>
                                                </li>
                                                <li><a href="/company/contact">联系我们</a></li>

                                                <li class="menu-item-has-children cs-mega-menu">
                                                    <a >服务协议</a>
                                                    <ul class="cs-mega-wrapper">
                                                        <li class="menu-item-has-children">
                                                            <a href="">商户协议</a>
                                                            <ul>
                                                                <li><a href="/index/index/content/id/20.html">注册协议</a></li>

                                                            </ul>
                                                        </li>
                                                        <li class="menu-item-has-children">
                                                            <a href="">买家协议</a>
                                                            <ul>
                                                                <li><a href="/index/index/content/id/20.html">购买协议</a></li>

                                                            </ul>
                                                        </li>
                                                    </ul>
                                                </li>


                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                     </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="cs-height_90 cs-height_lg_80"></div>
        <!-- Start Collection Details -->
        <div class="cs-bg" data-src="__RES__/theme/box/img/page_head_bg.svg">
            <div class="cs-height_100 cs-height_lg_70"></div>
            <div class="container">
                <div class="cs-collection_card">
                    <div class="cs-collection_bottom">
                        <div class="cs-collection_avatar"><img src="__RES__/theme/box/picture/avatar_20.png" alt="Avatar"></div>
                        <div class="cs-collection_info">
                            <div class="cs-collection_info_in cs-white_bg">
                                <div class="cs-collection_info_left">
                                    <h2 class="cs-collection_avatar_name">订单在线查询</h2>
                                    <div class="cs-collection_user">有问题请联系平台客服</div>
                                    <a href="#" class="cs-btn cs-style1">
                                        <span><i class="far fa-star"></i> 联系客服</span>
                                    </a>

                                </div>
                                <div class="cs-collection_right">
                                    <div class="cs-collection_list_wrap">
                                        <ul class="cs-collection_list cs-white_bg cs-mp0">
                                            <li>
                                                <div class="cs-collection_list_title">客服QQ：</div>
                                                <div class="cs-collection_list_number">{:sysconf('site_info_qq')}</div>
                                            </li>
                                            <li>
                                                <div class="cs-collection_list_title">客服邮箱</div>
                                                <div class="cs-collection_list_number">{:sysconf('site_info_email')}</div>
                                            </li>
                                            <li>
                                                <div class="cs-collection_list_title">在线时间</div>
                                                <div class="cs-collection_list_number">10:00-24:00</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Collection Details -->


        <!-- Start Section -->
        <section class="section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="section-title text-center mb-4 pb-2">

                            <div class="row projects-wrapper">  
                                {foreach $order as $v}            
                                <div class="col-lg-4 col-md-6 col-12 mt-4 pt-2 business">
                                    <div class="card blog border-0 work-container work-classic shadow rounded-md overflow-hidden">
                                        <div class="card-body">
                                            <div class="content">
                                                <div>
                                                    <a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="badge badge-primary">{$v.trade_no}</a>
                                                    {switch name="$v.status"}
                                                    {case value="0"}<span class="badge badge-light">未支付</span>{/case}
                                                    {case value="1"}<span class="badge badge-success">已支付</span>{/case}
                                                    {case value="2"}<span class="badge badge-dark">已关闭</span>{/case}
                                                    {case value="3"}<span class="badge badge-warning">已退款</span>{/case}
                                                    {/switch}
                                                </div>
                                                <h5 class="mt-3"><a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="text-dark title">{$v.goods_name} </a></h5>
                                                <p class="text-muted">¥{$v.goods_price} X {$v.quantity}<a href="/orderquery/orderid/{$v.trade_no}/l/{:md5($v.trade_no . $sekey)}" class="text-primary h6">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i data-feather="arrow-right" class="fea icon-sm"></i></a></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {/foreach}

                            </div>
                        </div><!--end col-->
                    </div><!--end row-->

                </div><!--end container-->

            </div><!--end container-->

        </section><!--end section-->
        <!-- End Section -->








    </div>

    <div class="cs-height_100 cs-height_lg_70"></div>
    <!-- Start Footer -->
    <footer class="cs-footer cs-style1">
        <div class="cs-footer_bg"></div>
        <div class="cs-height_100 cs-height_lg_60"></div>
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="row">
                        <div class="col-lg-4 col-sm-4">
                            <div class="cs-footer_widget">
                                <h2 class="cs-widget_title">快速通道</h2>
                                <ul class="cs-widget_nav">
                                    <li><a href="/merchant">个人中心</a></li>
                                    <li><a href="/register">快速开店
                                        </a></li>
                                    <li><a href="/orderquery">订单查询</a></li>
                                    <li><a href="/complaintquery">投诉查询</a></li>

                                </ul>
                            </div>
                        </div><!-- .col -->
                        <div class="col-lg-4 col-sm-4">
                            <div class="cs-footer_widget">
                                <h2 class="cs-widget_title">帮助中心</h2>
                                <ul class="cs-widget_nav">
                                    <li><a href="/company/notice">最新公告</a></li>
                                    <li><a href="/index/index/content/id/20.html">免责声明</a></li>
                                    <li><a href="/company/faq">使用帮助</a></li>
                                    <li><a href="/index/index/content/id/13.html">用户协议</a></li>

                                </ul>
                            </div>
                        </div><!-- .col -->
                        <div class="col-lg-4 col-sm-4">
                            <div class="cs-footer_widget">
                                <h2 class="cs-widget_title">联系我们</h2>
                                <ul class="cs-widget_nav">
                                    <li><a href="http://wpa.qq.com/msgrd?v=3&uin={:sysconf('site_info_qq')}&site=qq&menu=yes">QQ客服：{:sysconf('site_info_qq')}</a></li>
                                    <li><a href="how-it-works.html">企业邮箱：{:sysconf('site_info_email')}</a></li>
                                    <li><a href="about.html">工作时间：8:30 ~ 23:00</a></li>


                                </ul>
                            </div>
                        </div><!-- .col -->
                    </div>
                </div>
                <div class="col-lg-4 col-sm-12">
                    <div class="cs-footer_widget">
                        <h2 class="cs-widget_title">平台商业合作</h2>
                        <form class="cs-footer_newsletter">
                            <input type="text" placeholder="{:sysconf('site_info_email')}" class="cs-newsletter_input">
                            <button class="cs-newsletter_btn">
                                <svg width="25" height="16" viewbox="0 0 25 16" fill="none" xmlns="https://www.w3.org/2000/svg">
                                <path d="M24.7014 9.03523C25.0919 8.64471 25.0919 8.01154 24.7014 7.62102L18.3374 1.25706C17.9469 0.866533 17.3137 0.866533 16.9232 1.25706C16.5327 1.64758 16.5327 2.28075 16.9232 2.67127L22.5801 8.32812L16.9232 13.985C16.5327 14.3755 16.5327 15.0087 16.9232 15.3992C17.3137 15.7897 17.9469 15.7897 18.3374 15.3992L24.7014 9.03523ZM0.806641 9.32812H23.9943V7.32812H0.806641V9.32812Z" fill="white"></path>
                                </svg>                  
                            </button>
                        </form>
                        <div class="cs-footer_social_btns">
                            <a href="#"><i class="fab fa-facebook-f fa-fw"></i></a>
                            <a href="#"><i class="fab fa-twitter fa-fw"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in fa-fw"></i></a>
                            <a href="#"><i class="fab fa-instagram fa-fw"></i></a>
                            <a href="#"><i class="fab fa-whatsapp fa-fw"></i></a>
                            <a href="#"><i class="fab fa-github fa-fw"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="cs-height_60 cs-height_lg_20"></div>

        <div class="cs-footer_bottom">
            <div class="container">
                <div class="cs-footer_separetor"></div>
                <div class="cs-footer_bottom_in">
                    <div class="cs-copyright">{:sysconf('site_info_copyright')}所有，盗版必究！

                        <a class="text-muted" href="https://beian.miit.gov.cn/">备案号：{:sysconf('site_info_icp')}</a></div>
                    <ul class="cs-footer_menu">
                        <li><a href="/register">商户注册</a></li>
                        <li><a href="/login">商户登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
    <!-- Script -->
    <script src="__RES__/theme/box/js/jquery-3.6.0.min.js"></script>


    <script src="__RES__/theme/box/js/main.js"></script>

    <!-- SLIDER -->



    <!-- Main Js -->

    <script src="/static/theme/landrick/js/bootstrap.bundle.min.js"></script>
    <script src="/static/theme/landrick/js/jquery.easing.min.js"></script>
    <script src="/static/theme/landrick/js/scrollspy.min.js"></script>
    <!-- SLIDER -->
    <script src="/static/theme/landrick/js/owl.carousel.min.js "></script>
    <script src="/static/theme/landrick/js/owl.init.js "></script>
    <!-- Icons -->
    <script src="/static/theme/landrick/js/feather.min.js"></script>
    <script src="/static/theme/landrick/js/bundle.js"></script>
    <!-- Main Js -->
    <script src="/static/theme/landrick/js/app.js"></script>
    <script src="/static/app/js/clipboard.js"></script>
    <script src="/static/app/js/layer.js"></script>
    <script src="/static/plugs/clicaptcha/cookie.min.js"></script>
    <script src="/static/plugs/clicaptcha/CryptoJS.js"></script>
    <script src="/static/plugs/clicaptcha/clicaptcha.js"></script>





</body>
</html>