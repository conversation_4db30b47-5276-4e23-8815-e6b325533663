<?php

namespace app\common\util\notify;

use app\common\util\Sms;
use think\Db;

/**
 * 预存款不足通知
 * Class Sell
 * @package app\common\notify
 */
class Feelimit {

    /**
     * @param $order
     * @param $freezeMoney
     * @throws \Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function notify($user) {
        if (cache("fee_limit_" . $user['id']) !== 1) {
            sendMessage(0, $user['id'], "预存款不足通知", "您的平台预存款不足" . plugconf('custompay', 'fee_limit_notify') . "元，请您及时充值。");
            if (is_mobile_number($user['mobile'])) {
                $sms = new Sms();
                $sms->sendDepositNotify($user['mobile']);
                cache("fee_limit_" . $user['id'], 1);
            }
        }
    }

}
