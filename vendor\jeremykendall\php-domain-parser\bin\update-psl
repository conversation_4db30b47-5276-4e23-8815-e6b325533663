#!/usr/bin/env php
<?php

/**
 * PHP Domain Parser: Public Suffix List based URL parsing.
 *
 * @see http://github.com/jeremykendall/php-domain-parser for the canonical source repository
 *
 * @copyright Copyright (c) 2017 <PERSON> (http://jeremykendall.net)
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

use Pdp\Logger;
use Pdp\Installer;
use Pdp\Manager;

require dirname(__DIR__).'/vendor/autoload.php';

function writeln(array $messages, $output): int
{
    $message = implode(PHP_EOL, $messages). PHP_EOL;

    return fwrite($output, $message);
}

function success(string $message, $output = STDOUT): int
{
    $messages = (array) $message;

    return writeln($messages, $output);
}

function fail(string $message, $output = STDERR): int
{
    $messages = (array) $message;

    return writeln($messages, $output);
}

/**
 * CLI colors
 */
$cyan = chr(27)."[36m";
$green = chr(27)."[32m";
$reset = chr(27)."[0m";
$redbg = chr(27)."[41m";
$yellow = chr(27)."[33m";

$arguments = array_replace([
    Installer::CACHE_DIR_KEY => '',
    Installer::REFRESH_PSL_KEY => false,
    Installer::REFRESH_RZD_KEY => false,
    Installer::TTL_KEY => '1 DAY',
], getopt('h::', [
    'h',
    'help',
    Installer::CACHE_DIR_KEY.':',
    Installer::REFRESH_PSL_KEY,
    Installer::REFRESH_PSL_URL_KEY.':',
    Installer::REFRESH_RZD_KEY,
    Installer::REFRESH_RZD_URL_KEY.':',
    Installer::TTL_KEY.':',
]));

if (isset($arguments['help']) || isset($arguments['h'])) {
    $default_cache_dir = dirname(__DIR__).'/data';
    $script = basename(__FILE__);
    $helpText = <<<HELP
{$yellow}Usage:$reset
    $script [options]
    
{$yellow}Options:$reset
    $green    --%s$reset                 refreshes the Public Suffix List cache
    $green    --%s=URL$reset         set the URL to use to refresh the PSL cache ({$yellow}default:$reset %s)
    $green    --%s$reset                 refreshes the IANA Root Zone Database cache
    $green    --%s=URL$reset         set the URL to use to refresh the RZD cache ({$yellow}default:$reset %s)
    $green    --%s=TTL$reset             set the TTL for the cache ({$yellow}default:$reset '1 DAY')
    $green    --%s=CACHE-DIR$reset set the cache root directory ({$yellow}default:$reset $default_cache_dir')
    $green-h, --help$reset                show the following help message

{$yellow}Help:$reset
    The {$green}update-psl$reset command updates your PDP local cache.
    
{$yellow}Examples:$reset

     Refresh all caches using the default settings
     $green$script$reset
     
     Refresh only the PSL cache for a TTL of 3 DAY 
     $green$script --psl --ttl="3 DAYS"$reset
     
     Refresh all caches using another cache directory
     $green$script --cache-dir=/temp$reset

     Read more at https://github.com/jeremykendall/php-domain-parser/

HELP;

    $helpText = sprintf($helpText,
        Installer::REFRESH_PSL_KEY,
        Installer::REFRESH_PSL_URL_KEY,
        Manager::PSL_URL,
        Installer::REFRESH_RZD_KEY,
        Installer::REFRESH_RZD_URL_KEY,
        Manager::RZD_URL,
        Installer::TTL_KEY,
        Installer::CACHE_DIR_KEY
    );

    success($helpText);

    die(0);
}

if (!extension_loaded('curl')) {
    fail("$redbg The required PHP cURL extension is missing. $reset");

    die(1);
}

$installer = Installer::createFromCacheDir(new Logger( STDOUT, STDERR), $arguments[Installer::CACHE_DIR_KEY]);
success("$yellow Updating your Pdp local cache.$reset");
if ($installer->refresh($arguments)) {
    success("$green Pdp local cache successfully updated. $reset");

    die(0);
}

fail("$redbg The command failed to update Pdp local cache successfully. $reset");

die(1);
