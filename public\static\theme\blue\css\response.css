/*pc*/
@media (min-width: 992px){
	html{
		font-size: 100px;
	}
  
	.container {
		max-width: 1380px;
	}
	.header .container{
		max-width: 1380px;
		padding-left: 15px;
		padding-right: 15px;
	}
	.header .logo{
		padding-left: 0;
	}
	.navbar{
		padding:0;
	}
	.navbar-nav{
		position: relative;
		width:auto;
		height:auto;
		top:0;
		left:0;
		padding-top: 0;
		background-color: transparent;
	}
	.navbar-expand-lg .navbar-collapse{
		justify-content:flex-end;
	}
	.navbar .navbar-nav .nav-link{
		margin-left: 40px;
	}
	.navbar .navbar-nav .nav-link:before{
		content:"";
		display: block;
		width:0;
		height:4px;
		position: absolute;
		left:50%;
		margin-left: 0;
		bottom:-4px;
		background-color: #fff;
	}
	.navbar .navbar-nav .nav-link:hover:before,.navbar .navbar-nav .nav-link.active:before{
		width:70%;
		left:50%;
		margin-left: -35%;
		color: #fff;
		opacity:1;
	}
	.navbar .navbar-nav .nav-link.store-login{
		background-color: #fff;
		color: #3476fe;
		padding:0 20px;
		-webkit-border-radius: 20px;
		-moz-border-radius: 20px;
		border-radius: 20px;
	}
	.navbar .navbar-nav .nav-link.store-login:before,.navbar .navbar-nav .nav-link.buyer-login:before,.navbar .navbar-nav .nav-link.order-search:before{
		display: none;
	}
	.navbar .navbar-nav .nav-link.buyer-login{
		margin-left: 20px;
		border:2px solid #fff;
		line-height:36px;
		padding:0 20px;
		-webkit-border-radius: 20px;
		-moz-border-radius: 20px;
		border-radius: 20px;
	}
	.navbar .navbar-nav .nav-link.order-search{		
		margin-left: 20px;
		background-color: #fff;
		color: #3476fe;
		padding:0 20px;
		-webkit-border-radius: 20px;
		-moz-border-radius: 20px;
		border-radius: 20px;
	}
	.banner{
		padding:0;
	}
	.banner .text-introduce .h1{
		font-size: 44px;
	}
	.banner .text-introduce .h2{
		font-size: 40px;
	}
	.banner .text-introduce .p{
		font-size: 18px;
		line-height:28px;
	}
	.banner .text-introduce .btn-jion{
		height:72px;
		line-height:50px;
		font-size: 16px;
		-webkit-border-radius: 36px;
		-moz-border-radius: 36px;
		border-radius: 36px;
		font-weight: bold;
	}
	.banner .img{
		display: block;
		margin-top: 75px;
	}
	.home-news-item .img{
		width: .26rem; 
		height: .28rem;
		margin-right: .16rem;
	}
	.home-news-item dl dt a{
		font-size: .2rem;
	}
	.home-news-item dl dd{
		font-size: 16px;
		height:48px;
		overflow:hidden;
	}
	.home-news .row{
		margin-left: -30px; 
		margin-right: -30px;
	}
	.home-news .col-lg-3{
		padding-left: 30px;
		padding-right: 30px;
	}
	.morebox .more{
		height:70px;
		line-height:50px;
		font-size: 16px;
		-webkit-border-radius: 35rem;
		-moz-border-radius: 35rem;
		border-radius: 35rem;
	}
	.fixedright{
		display: block;
	}
	/*首页内容部分标题*/
	.home-content-title .h1{
		font-size: 30px;
	}
	.home-content-title .h2{
		font-size: 16px;
	}
	.experience .bd .item .p{
		font-size: 18px;
	}
	.experience .bt{
		display:flex;
		justify-content:center;
		align-items:center;
	}
	.experience .bt .text{
		font-size: 18px;
	}
	.experience .bt .btn-experience{
		line-height:52px;
		font-size: 16px;
		margin-left: 20px;
		margin-top: 0;
	}
	.question .bd{
		justify-content:space-around;
	}
	.question .bd .item{
		width:330px;
	}
	.question .bd .item dt{
		font-size: 20px;
	}
	.question .bd .item dd{
		font-size: 16px;
	}
	/*入驻流程*/
	.stationed-process .bd .item{
		flex:0 0 16.66%;
		max-width:16.66%;
	}
	.stationed-process .bd .item .p{
		font-size: 18px;
	}
	/*购买流程*/
	.buy-process .bd{
		display:flex;
		justify-content:space-between;
	}
	.buy-process .img{
		display: block;
	}
	.buy-process .step{
		padding-right: 2.2rem;
	}
	.buy-process .step .item:nth-child(2){
		padding-left: 1.1rem;
	}
	.buy-process .bt{
		display:flex;
		justify-content:center;
		align-items:center;
	}
	.buy-process .bt .text{
		font-size: 18px;
	}
	.buy-process .bt .btn-experience{
		line-height:52px;
		font-size: 16px;
		margin-left: 20px;
		margin-top: 0;
		-webkit-border-radius: 36px;
		-moz-border-radius: 36px;
		border-radius: 36px;
	}
	/*早发卡平台*/	
	.platform .nav-tabs{
		justify-content:space-between;
		overflow: visible;
	}
	.platform .bd{
		justify-content:space-around;
		padding-top: 30px;
	}
	.platform .bd .item{
		width:330px;
	}
	.platform .bd .item dt{
		font-size: 20px;
	}
	.platform .bd .item dd{
		font-size: 16px;
		line-height:26px;
	}
	/*底部*/
	.footer .qq{
		width:420px;
		font-size: 20px;
		padding:0 30px;
	}
	
	/*关于我们*/
	.about-header .banner .text-introduce .p{
		height:auto;
	}
	.feature .bd{
		display:flex;
		justify-content:space-between;
	}
	.feature .bd .item{
		width:280px;
	}
	.feature .item dl dt{
		font-size: 22px;
	}
	.feature .item dl dd{
		font-size: 16px;
		line-height:24px;
	}
	/*联系我们*/
	.contact{}
	.contact  .h1{
		font-size: 30px;
	}
	.contact .bd{
		display:flex;
		justify-content:space-around;
		padding-left: 100px; 
		padding-right: 100px;
	}
	.contact .bd .item dl dt{
		font-size: 24px;
	}
	.map .bd{
		display:flex;
		justify-content:space-between;
	}
	.map .bd dl dt{
		padding-top: 40px;
		font-size: 24px;
	}
	.map .bd dl dd{
		font-size: 18px;
		padding-bottom: 130px;
	}
	.map .bd .gps a{
		font-size: 18px;
	}
	.query-header .banner .text-introduce{
		margin-top: 100px;
	}
	.query-header .banner .text-introduce .h1{
		font-size: 40px;
	}
	.order-search .search{
		font-size: 20px;
	}
	.order-search .search-text{
		-webkit-background-size: 30px 30px;
		background-size: 30px 30px;
	}	
	.order-search .statement p{
		margin-top: 0;
	}
	.order-search-result .title{
		align-items:center;
	}
	.order-search-result .title .order-num{
		font-size: 20px;
	}
	.order-search-result .date{
		font-size: 16px;
	}
	.order-search-result .bd{
		display:flex;
	}
    .order-search-result .bd .leftdata{
		flex:1;
	}	
	.order-search-result .bd .leftdata dt{
		font-size: .28rem;
	}
	.order-search-result .bd .leftdata dd{
		font-size: 20px;
	}
	.order-search-result .bd .btnbox{
		padding:0 100px;
		border-left:1px solid #e8eaee;
		border-top: none;
	}
	.order-search-result .bd .btnbox .btn-card{
		height:70px;
		line-height:50px;
		-webkit-border-radius: 35px;
		-moz-border-radius: 35px;
		border-radius: 35px;
		font-size: 22px;
	}
	.pagination a{
		width:34px;
		height:34px;
		line-height:34px;		
	}
	
	/*帮助中心*/
	.help-header .banner .text-introduce{
		margin-top: 80px;
	}
	.help-search .icon{
		width:30px;
		height:30px;
	}
	.help-search .text{
		font-size: 20px;
	}
	.help-search .btn-search{
		font-size: 22px;
	}
	.helpbox .nav-tabs{
		padding-left: 84px;
		padding-right: 84px;
	}
	.helpbox .nav-tabs .nav-link{
		font-size: 22px;		
	}
	.helpbox .item{
		line-height:35px;
	}
	.helpbox .item dt{
		font-size: 22px;
	}
	.helpbox .item dd{
		font-size: 20px;
	}
	.helpbox .item .icon{
		width:42px;
	}
	.bread{
		padding:0 84px;
	}
	.acticle-page{
		padding:0 100px;
	}
	.acticle-page-header .h1{
		font-size: 40px;
	}
	.acticle-page-header .share{
		position: absolute;
		right:0;
		bottom:28px;		
	}
	.acticle-page-header .share a{
		width:30px;
	}
	.acticle-page-content{
		font-size: 20px;
	}
	.acticle-other .title{
		font-size: 20px;
	}
	.acticle-other .item{
		padding-left: 50px; 
		padding-right: 50px;
	}
	.acticle-other .item dl{
		margin:0 40px;
	}
	.acticle-other .item .date .h2{
		font-size: 20px;
	}
	.acticle-other .item .more{
		width:34px;
	}
	/*底部*/
	.footer .logo-f img{
		width:254px;
	}
	.footer .h1{
		font-size: 20px;
	}
	.footer .qq{
		height:54px;
		font-size: 18px;
	}
	.footer .qq img{
		width:30px;
	}
	.footer .info{
		font-size: 18px;
	}
	.footer .info img{
		width:32px;
	}
	.footer .links, .footer .links a{
		font-size: 20px;
	}
	.footer .validation{
		justify-content:flex-end;
	}
	.footer .validation img{
		margin-left: 50px;
	}
	.copyright{
		font-size: 16px;
	}
	/*产品服务*/
	.about-header .banner .text-introduce .h1{
		line-height:60px;
	}
	.about-header .banner .img{
		margin-top: 40px; 
		padding-left: 0;
		padding-right: 40px;
	}
	.feature-product .item-arrow img{
		transform: rotate(0deg);	
	}
	.product-info .h1{
		font-size: 34px;
		line-height: 55px;
	}
	.product-info .p{
		font-size: 18px;
		line-height:34px;
	}
	.column-reverse .row {
		flex-direction: row;
	}
	.product-info .text-item{
		font-size: .34rem;
	}
	.product-info .text-item img{
		width:48px;
	}
	.product-info .wx dl{
		font-size: 18px;
	}
	
}

@media (min-width: 1400px){
	.navbar .navbar-nav .nav-link{
		margin-left: 60px;
	}
	.round-bg-display{
		display: block;
	}
}
