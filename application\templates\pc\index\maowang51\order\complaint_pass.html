<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="theme-color" content="#1E78FF">	
        <meta name="msapplication-navbutton-color" content="#1E78FF">		
        <meta name="apple-mobile-web-app-status-bar-style" content="#1E78FF">
        <link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/style.css" media="all">		
        <link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/responsive.css" media="all">	


    </head>

    <body>
        <div class="main-page-wrapper">

            {include file="./default_her"}


            <div class="offcanvas offcanvas-top theme-search-form justify-content-center" tabindex="-1" id="offcanvasTop">
                <button type="button" class="close-btn tran3s" data-bs-dismiss="offcanvas" aria-label="Close"><i class="bi bi-x-lg"></i></button>
                <div class="form-wrapper">
                    <form action="orderquery">
                        <input type="text" name="orderid" placeholder="请输入您的订单号...">
                        <input type="hidden" name="querytype" value="3">      
                        <button class="btn-eight">开始查询</button>
                    </form>
                </div> <!-- /.form-wrapper -->
            </div>



            <header class="theme-main-menu sticky-menu theme-menu-two">
                {include file="./default_header"}
            </header>
            <div class="hero-banner-four">
                <div class="container">
                    <div class="row">
                        <div class="col-xl-8 col-xl-7 col-lg-8 col-md-11 m-auto">
                            <a href="#" class="slogan"><strong> 投 诉 详 情  </strong> ----轻松查询订单，即刻享受卡密自动交易 <i class="fas fa-chevron-right"></i></a>

                            <p class="mb-50 lg-mb-30"></p>

                        </div>
                    </div>
                </div> <!-- /.container -->

                <div class="illustration-holder-oneee">
                    <img src="__RES__/theme/maowang51/picture/ils_10.svg" alt="">
                    <img src="__RES__/theme/maowang51/picture/ils_10_1.svg" alt="" class="shapes shape-one">
                    <img src="__RES__/theme/maowang51/picture/ils_10_2.svg" alt="" class="shapes shape-two">
                </div> <!-- /.illustration-holder-one -->
                <div class="illustration-holder-tee">
                    <img src="__RES__/theme/maowang51/picture/ils_11.svg" alt="">
                    <img src="__RES__/theme/maowang51/picture/ils_10_1.svg" alt="" class="shapes shape-one">
                </div>
            </div> 




            <div class="container">


                <div class="row">

                    <div class="section-title text-center mb-4 pb-2">


                        <div class="col mt-4 pt-2" id="forms">
                            <div class="component-wrapper rounded shadow">
                                <div class="p-4 border-bottom">
                                    <h5 class="mb-0"> 投 诉 详 情 </h5>
                                </div>

                                <div class="p-4">{if condition="!empty($complaint)"}
                                    <form name='report'>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group position-relative">
                                                    <label>订单编号</label>
                                                    <i data-feather="user" class="fea icon-sm icons"></i>
                                                    <input disabled name="trade_no" type="text" class="form-control pl-5" value="{$complaint->trade_no|htmlentities}">
                                                </div>
                                            </div><!--end col-->
                                            <div class="col-md-6">
                                                <div class="form-group position-relative">
                                                    <label>举报原因</label>
                                                    <i data-feather="map-pin" class="fea icon-sm icons"></i>
                                                    <input disabled class="form-control pl-5" name="type" type="text" value="{$complaint->type|htmlentities}">
                                                </div> 
                                            </div><!--end col-->
                                            <div class="col-md-6"><br>
                                                <div class="form-group position-relative">
                                                    <label>联系方式</label>
                                                    <i data-feather="at-sign" class="fea icon-sm icons"></i>
                                                    <input  disabled name="name" id="name" type="text" class="form-control pl-5" value="{$complaint->qq|htmlentities}">
                                                </div>
                                            </div><!--end col-->
                                            <div class="col-md-6"><br>
                                                <div class="form-group position-relative">
                                                    <label>投诉时间</label>
                                                    <i data-feather="clock" class="fea icon-sm icons"></i>
                                                    <input disabled name="email" id="text" type="text" class="form-control pl-5" value="{$complaint.create_at|date='Y-m-d H:i:s', ###|htmlentities}">
                                                </div> 
                                            </div><!--end col-->
                                            <div class="col-md-6">
                                                <div class="form-group position-relative"><br>
                                                    <label>投诉状态</label>
                                                    <i data-feather="align-center" class="fea icon-sm icons"></i>
                                                    {if condition="$complaint->status == 0"}
                                                    <input disabled name="truck" type="text" class="form-control pl-5" value="未处理">
                                                    {elseif condition="$complaint->status == -1"}
                                                    <input disabled name="mobile" type="text" class="form-control pl-5" value="已撤销">
                                                    {else /}
                                                    <input disabled name="mobile" type="text" class="form-control pl-5" value="已处理">
                                                    {/if}
                                                </div> 
                                            </div>


                                            {if $complaint->buyer_qrcode!=""}
                                            <div class="col-md-6"><br>
                                                <div class="form-group position-relative">
                                                    <label>胜诉收款二维码</label>
                                                    <div class="form-control">
                                                        <a target="blank" href="{$complaint.buyer_qrcode}">点击查看</a>
                                                    </div>
                                                </div> 
                                            </div>
                                            {/if}

                                            <div class="col-md-6">
                                                <div class="form-group position-relative"><br>
                                                    <label>关于投诉说明</label>
                                                    <i data-feather="mail" class="fea icon-sm icons"></i>
                                                    <input disabled name="text" id="text" type="text" class="form-control pl-5" value="请买家与商家沟通并说明问题，以便快速解决问题！">
                                                </div> 
                                            </div><!--end col-->
                                            <div class="col-sm-12"><br>

                                                {if condition="$complaint->status == 0"}
                                                <button type="button" class="btn-eight w-100 mt-50 mb-40 lg-mt-30 lg-mb-30"  onclick="cancel()">撤销投诉</button>
                                                {/if}
                                                <button type="button" class="btn-eight w-100 mt-50 mb-40 lg-mt-30 lg-mb-30"  onclick="detail()">投诉详情</button>



                                            </div><!--end col-->
                                        </div><!--end row-->
                                    </form><!--end form-->
                                </div>
                            </div>
                        </div><!--end col-->
                        <!-- Forms End -->
                        <script src="__RES__/merchant/default/libs/jquery/jquery.min.js"></script>
                        <script src="__RES__/merchant/default/libs/jquery-confirm/js/jquery-confirm.js"></script>
                        <script src="__RES__/merchant/default/libs/layer/layer.js"></script>
                        <script>
                                                    function cancel() {
                                                        layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                                            $.post("{:url('Index/Order/complaintCancel')}", {
                                                                token: "{$token}",
                                                                trade_no: "{$complaint->trade_no}",
                                                                pwd: pwd
                                                            }, function (data) {
                                                                if (data.code != '200') {
                                                                    layer.msg(data.msg, {icon: 2})
                                                                } else {
                                                                    layer.msg(data.msg, {icon: 1}, function () {
                                                                        location.reload();
                                                                    })
                                                                }
                                                            });
                                                        });
                                                    }

                                                    function detail() {
                                                        layer.prompt({title: '请输入投诉密码'}, function (pwd) {
                                                            $.post("{:url('Index/Order/complaintPass')}", {
                                                                token: "{$token}",
                                                                trade_no: "{$complaint->trade_no}",
                                                                pwd: pwd
                                                            }, function (data) {
                                                                if (data.code != '200') {
                                                                    layer.msg(data.msg, {icon: 2})
                                                                } else {
                                                                    layer.msg(data.msg, {icon: 1}, function () {
                                                                        location.href = data.url;
                                                                    })
                                                                }

                                                            });
                                                        });
                                                    }
                        </script>
                        {else /}
                        <div style="display:block;text-align:center; font-size:24px;margin: 5rem 0;">订单不存在或该订单暂无投诉信息</div>
                        {/if}
                    </div><!--end col-->



                </div>


            </div>  































            <div class="fancy-short-banner-three position-relative mt-160 lg-mt-80">
                <div class="container">
                    <div class="bg-wrapper">
                        <div class="row align-items-center">
                            <div class="col-lg-8 m-auto" data-aos="fade-up">
                                <div class="title-style-one text-center white-vr mb-30" data-aos="fade-up">
                                    <h2 class="main-title">入驻我们，即刻赚钱 <br>24小时监控订单资金无忧</h2>
                                </div> <!-- /.title-style-one -->
                                <a href="contact-us.html" class="btn-six ripple-btn">立即入驻，成为商户 <i class="fas fa-chevron-right"></i></a>
                            </div>
                        </div>
                    </div> <!-- /.bg-wrapper -->
                </div> <!-- /.container -->
            </div> <!-- /.fancy-short-banner-three -->




            <!--
            =====================================================
                    Footer
            =====================================================
            -->
            <div class="footer-style-one theme-basic-footer">
                <div class="container">
                    <div class="inner-wrapper">




                        {include file="./default_footer"}

                    </div> <!-- /.inner-wrapper -->
                </div>
            </div> <!-- /.footer-style-one -->


            <button class="scroll-top">
                <i class="bi bi-arrow-up-short"></i>
            </button>




            <!-- Optional JavaScript _____________________________  -->

            <!-- jQuery first, then Bootstrap JS -->
            <!-- jQuery -->
            <script src="__RES__/theme/maowang51/js/jquery.min.js"></script>
            <!-- Bootstrap JS -->
            <script src="__RES__/theme/maowang51/js/bootstrap.bundle.min.js"></script>
            <!-- AOS js -->
            <script src="__RES__/theme/maowang51/js/aos.js"></script>
            <!-- Slick Slider -->
            <script src="__RES__/theme/maowang51/js/slick.min.js"></script>
            <!-- js Counter -->
            <script src="__RES__/theme/maowang51/js/jquery.counterup.min.js"></script>
            <script src="__RES__/theme/maowang51/js/jquery.waypoints.min.js"></script>
            <!-- Fancybox -->
            <script src="__RES__/theme/maowang51/js/jquery.fancybox.min.js"></script>
            <!-- Progress Bar js -->
            <script src="__RES__/theme/maowang51/js/jquery.skills.js"></script>

            <!-- Theme js -->
            <script src="__RES__/theme/maowang51/js/theme.js"></script>











        </div> <!-- /.main-page-wrapper -->
    </body>
</html>