{extend name='./content'}

{block name="content"}
<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商户账号</label>
        <div class="layui-input-inline">
            <input name="username" value="{$Think.get.username|default=''|htmlentities}" placeholder="请输入商户账号" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">系统流水号</label>
        <div class="layui-input-inline">
            <input name="trade_no" value="{$Think.get.trade_no|default=''|htmlentities}" placeholder="请输入系统流水号" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">商家订单号</label>
        <div class="layui-input-inline">
            <input name="out_trade_no" value="{$Think.get.out_trade_no|default=''|htmlentities}" placeholder="请输入商家订单号" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>


<div class="layui-form-item layui-inline">
    <button id="heand_del_batch" type="button" class="layui-btn layui-btn-small layui-btn-danger"><i class="fa fa-remove"></i>批量删除</button>
</div>


<table class="layui-table" lay-skin="line" lay-size="sm">
    <thead>
        <tr>
            <th class='list-table-check-td'>
                <input data-none-auto="" data-check-target='.list-check-box' type='checkbox' />
            </th>
            <th class='text-center'>ID</th>
            <th class='text-center'>用户名</th>
            <th class='text-center'>系统流水号</th>
            <th class='text-center'>商家订单号</th>
            <th class='text-center'>商品名</th>
            <th class='text-center'>金额</th>
            <th class='text-center'>网站名称</th>
            <th class='text-center'>创建时间</th>
            <th class='text-center'>支付状态</th>
            <th class='text-center'>跳转地址</th>
            <th class='text-center'>回调地址</th>
            <th class='text-center'>操作</th>
        </tr>
    </thead>
    <tbody>
        {foreach $list as $v}
        <tr>
            <td class='list-table-check-td'>
                <input class="list-check-box" value='{$v.id}' type='checkbox' />
            </td>
            <td class='text-center'>{$v.id}</td>
            <td class='text-center'><a data-open="{:url('manage/user/index')}?field=1&keyword={$v.user_id}"  href="javascript:void(0)">{$v.user.username}</a></td>
            <td class='text-center'>{$v.trade_no}</td>
            <td class='text-center'>{$v.out_trade_no}</td>
            <td class='text-center'>{$v.goods_name}</td>
            <td class='text-center'>{$v.total_price}</td>
            <td class='text-center'>{$v.sitename}</td>
            <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>
            <td class='text-center'>
                {switch name="$v.status"}
                {case value="0"}<font color="red">未支付</font><a href="javascript:;" class="text-s" onclick="notify(this, '{$v.id}')"> 补单</a>{/case}
                {case value="1"}<font color="green">已支付</font><a href="javascript:;" class="text-s" onclick="refund(this, '{$v.id}')"> 退款</a>{/case}
                {case value="2"}<font color="#999">已关闭</font>{/case}
                {case value="3"}<font color="#999">已退款</font>{/case}
                {/switch}
            </td>
            <td class='text-center'>{$v.return_url}</td>
            <td class='text-center'>{$v.notify_url}</td>
            <td class='text-center'>
                <a href="javascript:;" onclick="del(this, '{$v.id}')">删除</a>
            </td>
        </tr>
        {/foreach}
    </tbody>
</table>
{$page}

<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //日期范围
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });

    function notify(obj, id)
    {
        layer.confirm('确认要标记为已支付吗？', function (index) {
            $.ajax({
                url: "{:url('payapiOrder')}",
                type: 'post',
                data: 'act=notify&id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        layer.msg("操作成功");
                        setTimeout(function () {
                            location.reload();
                        }, 200);
                    } else {
                        layer.alert(res.msg);
                    }
                }
            });
        });
    }
    function refund(obj, id)
    {
        layer.confirm('确认要退款吗？', function (index) {
            $.ajax({
                url: "{:url('payapiOrder')}",
                type: 'post',
                data: 'act=refund&id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        layer.msg(res.msg);
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.alert(res.msg);
                    }
                }
            });
        });
    }


    function del(obj, id) {
        layer.confirm('确认要删除此项吗？', function (index) {
            $.ajax({
                url: "{:url('payapiOrder')}",
                type: 'post',
                data: 'act=del&id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        layer.msg("操作成功");
                        setTimeout(function () {
                            location.reload();
                        }, 200);
                    } else {
                        layer.alert(res.msg);
                    }
                }
            });
        });
    }

    $('#heand_del_batch').click(function () {
        var ids = '';
        $('.list-check-box').each(function () {
            var _this = $(this);
            if (_this.is(':checked')) {
                ids = ids + _this.val() + ',';
            }
        });
        layer.confirm('确定要批量删除吗？', function (index) {
            if (ids == '') {
                layer.msg('请选择选项！');
                return false;
            }
            $.ajax({
                url: "{:url('payapiOrder')}",
                type: 'post',
                data: {
                    'ids': ids,
                    'act': 'handBatchDel'
                },
                success: function (res) {
                    if (res.code == 1) {
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                        layer.msg('已删除!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('删除失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    });
</script>
{/block}
