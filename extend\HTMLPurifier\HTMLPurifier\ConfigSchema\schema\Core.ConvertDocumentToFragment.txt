Core.ConvertDocumentToFragment
TYPE: bool
DEFAULT: true
--DESCRIPTION--

This parameter determines whether or not the filter should convert
input that is a full document with html and body tags to a fragment
of just the contents of a body tag. This parameter is simply something
HTML Purifier can do during an edge-case: for most inputs, this
processing is not necessary.

--ALIASES--
Core.AcceptFullDocuments
--# vim: et sw=4 sts=4
