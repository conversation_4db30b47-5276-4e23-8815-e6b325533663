
/* CSS Document */
html{
	font-size: 50px;
}
body{
	font-family: "微软雅黑";
	line-height:1;
	font-size: .32rem;
}
input{
	outline:none;
}
img{
	vertical-align: top;
}
a:hover {
  color: #3476fe;
  text-decoration: none;
}
button:focus{
	outline:none;
}
p,ul,li,dl,dt,dd{
	margin:0;
	padding:0;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6{
	margin:0;
}
.btn:focus{
	outline:none;
	border: none;
	
}
p{
	margin: 0;
}
ul{
	margin: 0;
	padding: 0;
}
li{
	list-style: none;
}

.header{
	height: 6.8rem;
	background-image: linear-gradient(to right,#3476fe,#34aafe);
	position: relative;
}
.header .bgimg{
	width:100%;
	position: absolute;
	left:0;
	bottom:0;
	z-index:1;
}
.header .bgimg img{
	width:100%;
}
.header .container{
	max-width:100%;
	padding-left: 0;
	padding-right: 0;
	position: relative;
	z-index:3;
}
.navbar{
	padding:.15rem 0;
	z-index:2;
}
.navbar .logo{
	display:flex;
	align-items:center;
	height: .8rem;
	padding-left: 15px;
}
.navbar .logo img{
	max-height:100%;
}
.navbar .navbar-nav .nav-link{
	color: #fff;
	padding:0;
	font-size: 18px;
	line-height:40px;
	text-align: center;
	position: relative;
}
.navbar-toggler{
	padding:0;
	font-size: .6rem;
	border:none;
	padding-right: 15px;
}
.navbar .navbar-nav .nav-link.order-search{
	display:flex;
	justify-content:center;
    align-items:center;
}
.navbar .navbar-nav .nav-link.order-search img{
	margin-right: 10px;
	margin-top: 2px;
}
.navbar-dark .navbar-nav .nav-link:hover{
	color: #fff;
}
.navbar-nav{
	width:100%;
	height:100%;
	position: fixed;
	left:0;
	top:60px;
	background-color: rgba(52,118,254,.8);
	padding-top: 15px;
}
/*banner*/
.banner{
	display: -ms-flexbox;
    display: flex;
	justify-content:space-between;
	align-items:center;
	padding:0 15px;
	z-index:1;
}
.banner .text-introduce{
	color: #fff;
	margin-top: 40px;
}
.banner .text-introduce .h1{
	font-size: 26px;
    font-weight: bold;	
}
.banner .text-introduce .h2{
	font-size: 20px;
	margin-top: .3rem;
}
.banner .text-introduce .p{
	margin-top: .52rem;
    font-size: 14px;	
	line-height:20px;
}
.banner .text-introduce .btn-jion{
	display: inline-block;
	height: 40px;
	line-height:30px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	border:.1rem solid #5d99fe;
	background-color: #fff;
	text-align: center;
	color: #3476fe;
	padding:0 20px;
	font-size: 12px;
	margin-top: .5rem;
	font-weight: bold;
}
.banner .img{
	display: none;
}
/*公告*/
.home-news{
	background-color: #f9fbff;
	padding-top: 25px;
	position: relative;	
	z-index:2;
}
.home-news-title{
	display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	justify-content:space-between;
}
.home-news-title .more{
	font-size: 16px;
	color: #adafb5;
}
.home-news-title .more i{
	font-family: "宋体";
	font-size: 12px;
	font-weight: bold;
	font-style: normal;
	padding-left: 10px;
}
.home-news-title .more:hover{
	text-decoration: none;
	color: #5375e0;
}
.home-news .tab-title{
	display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	align-items:center;
	color: #999999;
	margin-bottom: 20px;
}
.home-news .tab-title li{
	display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	align-items:center;
	font-size: 18px;
	color: #21355d;
	margin-right: .5rem;
	margin-bottom: .3rem;
	transition:all .1s ease;
}
.home-news .tab-title li:last-child{
	margin-right: 0;
}
.home-news .tab-title li img{
	width:30px;
	margin-right: 20px;
}
.home-news .tab-title li .name{
	position: relative;
}
.home-news .tab-title li.on,.home-news .tab-title li:hover{
	font-size: 20px;
	font-weight: bold;
	color: #3476fe;
	cursor:pointer;
}
.home-news .tab-title li.on .name:before,.home-news .tab-title li:hover .name:before{
	display:block;
	content:"";
	width:70%;
	height:4px;
	background-color: #3476fe;
	position: absolute;
	left:50%;
	margin-left: -35%;
	bottom:-20px;
}
.home-news-item{
	display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	box-shadow:0 0 10px rgba(52,118,254,.2);
	padding:20px;
	margin-bottom: 20px;
	cursor:pointer;
}

.home-news-item .img{
	width:28px;
	margin-right: 16px;
}
.home-news-item .img img{
	width:100%;
}
.home-news-item dl{
	flex:1;
	margin-bottom: 0;
}
.home-news-item dl dt a{
	font-size: 16px;
	color: #333333;
	font-weight: bold;
	display: block;
	overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:1;
	margin-bottom: .2rem;
}
.home-news-item dl dt a:hover{
	color: #3476fe;
	text-decoration: none;
}
.home-news-item dl dd{
	font-size: 14px;
	color: #a8adb9;
	line-height:24px;
	margin-bottom: 0;
}
.home-news-item .item-icon{
	text-align: right;
	visibility: hidden;
}
.home-news-item:hover .item-icon{
	visibility:visible;
}

.home-news .swiper-slide{
	padding:5px;
}
.home-news .container{
	position: relative;
	z-index:2;
	overflow:hidden;
}

.swiper-arrow{
	width:100%;
	height:100%;
	position:absolute;
	left:0;
	top:60px;
}
.swiper-arrow .swiper-button-next,.swiper-arrow .swiper-button-prev{
	width:14px;
	height:24px;
	background: no-repeat;
	margin-top: -40px;
	opacity:1;
	outline:none;
	font-size: 28px;
	font-weight: bold;
	color: #3476fe;
	font-family: "宋体";
}
.swiper-arrow .swiper-button-prev{
	left:-50px;
}
.swiper-arrow .swiper-button-next{
	right:-50px;
}
.swiper-arrow .swiper-button-disabled{
	color: #e7e7e7;
}
.morebox{
	display:flex;
	justify-content:center;
	padding-top: .1rem; 
	padding-bottom: .4rem;
	position: relative;
	z-index:2;
}
.morebox .more{
	display: block;
	cursor:pointer;
	height:40px;
	line-height:30px;
	font-size: 14px;
	color: #fff;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	padding:0 .5rem;
	background-color: #3476fe;
	border:.1rem solid #d2e0ff;
	cursor:pointer;
	font-weight: bold;
}
.notice-bg{
	max-width:100%;
	position: absolute;
	bottom:0;
	right:0;
	z-index:1;
}
.notice-bg img{
	width:100%;
}

/*右侧功能栏*/
.fixedright{
	width:45px;
	position: fixed;
	top:200px;
	right:10px;
	z-index:999;
	display: none;
}
.fixedright ul{
	background-color: #3476fe;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}
.fixedright ul li{
	display:flex;
	justify-content:center;
	align-items:center;
	height:60px;
	position: relative;
	cursor:pointer;
}
.fixedright ul li:first-child{
	border-top-left-radius:5px;
	border-top-right-radius:5px;
}
.fixedright ul li:last-child{
	border-bottom-left-radius:5px;
	border-bottom-right-radius:5px;
}
.fixedright ul li .showinfo{
	background-color: #3442fe;
	position:absolute;
	right:65px;
	top:0;
	font-size: 25px;
	color: #fff;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	padding:10px 15px;
	white-space: nowrap;
	display:none;
}
.fixedright ul li .showinfo:before{
	content:"";
	display:block;
	border:8px solid transparent;
	border-left:8px solid #3442fe;
	position: absolute;
	top:14px;
    right:-16px;	
}
.fixedright ul li:hover{
	background-color: #3442fe;
}
.fixedright ul li:hover .showinfo{
	display:block;
}
.fixedright ul li .ewm{
	padding:8px;	
}

.fixedright ul li .ewm p{
	font-size: 14px;
	color: #3442fe;
	font-weight: bold;
	position:absolute;
	bottom:-25px;
	left:-10px;
}
.fixedright .top{
	display:flex;
	flex-direction:column;
	justify-content:center;
	align-items:center;
	font-weight: bold;
	font-size: 14px;
	color: #3476fe;
	padding-top: 10px;
	cursor: pointer;
}
.fixedright .top p{
	padding-top: 5px;
}
/*首页内容部分标题*/
.home-content-title{
	text-align: center;
	padding-bottom: .5rem;
}
.home-content-title .h1{
	padding-top: .5rem;
	color: #21355d;
	font-size:20px;
	font-weight: bold;
	padding-bottom: .3rem;
}
.home-content-title .h2{
	font-size: 14px; 
	color: rgba(33,53,93,.5);
	line-height:1.3;	
}
/*立即体验*/
.experience{
	position: relative;
	z-index:1;
}
.experience .bd{
	display:flex;
	flex-wrap:wrap;
	justify-content:space-around;
}
.experience .bd .item{
	flex: 0 0 25%;
    max-width: 25%;
	text-align: center;
}
.experience .bd .item .img{
	width: 1.44rem; 
	height: 1.44rem;
	margin:0 auto;
}
.experience .bd .item .img img{
	width:100%;
}
.experience .bd .item .p{
	font-size: 16px;
	color: #21355d;
	line-height:1.4;
	margin-top: .5rem;
}
.experience .bt{
	text-align: center;
	padding-top: .85rem;
	padding-bottom: .5rem;
}
.experience .bt .text{
	line-height: 1.4;
	font-size: 16px;
	color: rgba(33,53,93,.5);
}
.experience .bt .btn-experience{
	display: inline-block;
	line-height:40px;
	background-image:linear-gradient(#1493fe,#1467fe);
	box-shadow:0 5px 10px rgba(20,106,254,.4);
	padding:0 .3rem;
	color:#fff;
	font-size: 14px;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	border-radius: 8px;
	margin-top: 20px;
}
/*解决用户体验差问题*/
.question{
	background-color: #f9fbff;
}
.question .bd{
	display:flex;
	flex-wrap:wrap;
	justify-content:space-between;
}
.question .bd .item{
	width:100%;
	text-align: center;
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	padding: .68rem .4rem;	
	margin-bottom: .9rem;
}
.question .bd .item:hover{
	box-shadow:0 0 10px rgba(20,106,254,.3);
}
.question .bd .item .img{
	width: 1.22rem; 
	height: 1.22rem;
	margin:0 auto;
}
.question .bd .item .img img{
	width:100%;
}
.question .bd .item dt{
	font-size: 16px;
	color: #21355d;
	font-weight: bold;
	margin-top: .5rem;
	margin-bottom: .2rem;
	line-height:1.2;
}
.question .bd .item dd{
	font-size: 14px;
	color: rgba(33,53,93,.5);
	line-height:20px;
}
/*入驻流程*/
.stationed-process{
	background-image: linear-gradient(to right bottom,#1468fe,#1498fe);
	padding-bottom: .2rem;
}
.stationed-process .home-content-title .h1{
	color: #fff;
}
.stationed-process .home-content-title .h2{
	color: #fff;
	opacity:.6;
}
.stationed-process .bd{
	display:flex;
	flex-wrap:wrap;
	justify-content:space-between;
}
.stationed-process .bd .item{
	flex:0 0 33.33%;
	max-width:33.33%;
	text-align: center;
	margin-bottom: .5rem;
}
.stationed-process .bd .item .img{
	width: .83rem; 
	height: .83rem;
	margin:0 auto;
}
.stationed-process .bd .item .img img{
	width:100%;
}
.stationed-process .bd .item .p{
	color: #fff;
	font-size: 16px;
	margin-top: .3rem;
}
/*购买流程*/
.buy-process{
	position: relative;
}
.buy-process .img{
	 display: none;
	 padding-left: 40px; 
	 padding-top: 50px;
}
.buy-process .step{
	padding-top: .5rem;
	padding-bottom: .5rem;
}
.buy-process .step .item{
	display:flex;
	align-items:center;
	margin-top: .7rem;
}
.buy-process .step .item:first-child{
	margin-top: 0;
}
.buy-process .step .number{
	width:80px;
	height:80px;
	-webkit-border-radius: 40px;
	-moz-border-radius: 40px;
	border-radius: 40px;
	border:10px solid #ebf1ff;
	font-size: 44px; 
	font-family: Impact;
	color: rgba(52,118,254,.7);
	text-align: center;
	line-height:60px;
	margin-right: 25px;
}
.buy-process .step .item dt{
	font-size: 20px;
	color:#21355d;
	font-weight: bold;
	margin-bottom: 12px;
}
.buy-process .step .item dd{
	font-size: 16px;
	color:rgba(33,53,93,.5)
}
.buy-process .bt{
	text-align: center;
	padding-top: .2rem;
	padding-bottom: .5rem;
}
.buy-process .bt .text{
	line-height: 1.4;
	font-size: 16px;
	color: rgba(33,53,93,.5);
}
.buy-process .bt .btn-experience{
	display: inline-block;
	line-height:40px;
	line-height:30px;
	background-color: #3476fe;
	padding:0 .3rem;
	color:#fff;
	font-size: 14px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	border:.1rem #d6e4ff solid;
	margin-top: 20px;
}

/*早发卡平台*/
.platform{
	background-image: linear-gradient(to right bottom,#1468fe,#1498fe);
}
.platform .home-content-title .h1{
	color: #fff;
}
.platform .home-content-title .h2{
	color: #fff;
	opacity:.6;
}
.platform .nav-tabs{
	border-top:1px solid rgba(255,255,255,.2);
	border-bottom: none;
	overflow-x:scroll;
	-ms-flex-wrap:nowrap;
	flex-wrap:nowrap;
}
.platform .nav-tabs::-webkit-scrollbar {
	width:0;
}
.platform .nav-tabs .nav-item{
	white-space: nowrap;
}
.platform .nav-tabs .nav-link{
	padding:0 24px;
	font-size: 18px;
	color: #fff;
	padding-top: 24px; 
	padding-bottom: 15px;
	margin-right: 20px;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	position: relative;
	white-space: nowrap;
	border:none;
}
.platform .nav-tabs .nav-item.show .nav-link,.platform .nav-tabs .nav-link.active,.platform .nav-tabs .nav-link:hover{
	background-color: transparent;
	border:none;
}
.platform .nav-tabs .nav-link.active:before,
.platform .nav-tabs .nav-link:hover:before{
	content:"";
	width:30%;
	max-width:80px;
	height:3px;
	background-color: #fff;
	position: absolute; 
	bottom:0;
	left:50%;
	margin-left: -15%;
}
.platform .bd{
	display:flex;
	flex-wrap:wrap;
	justify-content:space-between;
}
.platform .bd .item{
	width:100%;
	text-align: center;
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	padding: .68rem .4rem;	
	margin-bottom: .4rem;
}
.platform .bd .item:hover{
	box-shadow:0 0 10px rgba(20,106,254,.3);
}
.platform .bd .item .img{
	width:64px;
	height:64px;
	margin:0 auto;
}
.platform .bd .item .img img{
	width:100%;
}
.platform .bd .item dt{
	font-size: 16px;
	color: #21355d;
	font-weight: bold;
	margin-top: .5rem;
	margin-bottom: .2rem;
	line-height:1.2;
}
.platform .bd .item dd{
	font-size: 14px;
	color: rgba(33,53,93,.5);
	line-height:20px;
}
.platform .morebox{
	padding-top: 0;
	padding-bottom: .4rem;
}
.platform .morebox .more{
	border-color: #4399fe;
	background-color: #fff;
	color: #3476fe;
	
}
/*运营数据*/
.data-manager{
	position: relative;
}
.data-manager .bd{
	display:flex;
	justify-content:space-around;
}
.data-manager dl{
	padding-bottom: .5rem;
}
.data-manager dl dt{
	display:flex;
	align-items:center;
	color: #a8adb9;
	font-size: .4rem;
	
}
.data-manager dl dt span{
	position: relative;
	padding-top: .1rem;
}
.data-manager dl dt span .plus{
	position: absolute;
	top:-.3rem;
	right:-.3rem;
	font-size: .38rem;
}
.data-manager dl dt .num{
	font-size: .9rem;
	color: #3476fe;
	margin-right: 5px;
}
.data-manager dl dd{
	font-size: 18px; 
	color: #21355d;
	padding-top: 14px;
}
/*注册商户*/
.reg-store{
	background-image: linear-gradient(to right bottom,#1468fe,#1497fe);
	padding-top: .66rem;
	position: relative;
}
.reg-store .home-content-title .h1{
	color: #fff;
}
.reg-store .home-content-title .h2{
	color: #fff;
	opacity:.6;
}
.reg-store .morebox{
	padding-top: 0;
	padding-bottom: .6rem;
}
.reg-store .morebox .more{
	border-color: #4399fe;
	background-color: #fff;
	color: #3476fe;
	
}
.reg-store .bgimg{
	position: absolute;
	left:0;
	top:0;
	z-index:1;
}
.reg-store .bgimg img{
	width:100%;
}
/*底部*/
.ewm{
	padding-top: 30px;
	padding-bottom: 30px;
}
.ewm-bg{
	background-color: #fafcff;
}
.ewm dl{text-align: center;}
.ewm dl dt{}
.ewm dl dd{
	font-size: 16px;
	color: #fff;
	margin-top: 20px;
}


.footer{
	background-color: #1f2431;
	padding-top: .9rem;
}
.footer .logo-f img{
	width:160px;
}
.footer .h1{
	color: #fff;
	font-size: 16px;
	margin-top: .45rem;
	padding-bottom: .2rem;
}
.footer .qq{
	display:flex;
	display:-webkit-flex;
	justify-content:space-between;
	align-items:center;
	height:40px;
	font-size:14px;
	color: #3476fe;
	margin-top: 14px;
	border:1px solid #3476fe;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	padding:0 20px;
	margin-bottom: .3rem;
}
.footer .qq img{
	width:24px;
}
.footer .info{
	font-size: 14px;
	color: #e6e6e6;
	margin-bottom: .45rem;
}
.footer .info li{
	display:flex;
	align-items:center;
	margin-top: .12rem;	
}
.footer .info li:first-child{
	margin-top: 0;
}
.footer .info img{
	width:28px;
	margin-right: 20px;
}
.footer .links,.footer .links a{
	font-size: 16px;
	color: #ffffff;
	font-weight: bold;
}
.footer .validation{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	justify-content:space-between;
	align-items:center;
}
.footer .validation img{
	margin-top: .45rem;
}
.footer .help{
	display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
	justify-content:flex-end;
	margin-bottom: 40px;
}
.footer .help dl{
	margin-left: 80px;
	opacity:.8;
}
.footer .help a:hover{
	text-decoration: none;
	color: #3476fe;
	font-weight: bold;
}
.footer .help dt{
	padding-bottom: 20px;
} 
.footer .help dt a{
	color: #fff;
	font-size: 16px;	
	font-weight: bold;
}
.footer .help dd a{
	font-size: 16px;
	color:#e0e0e0;
	line-height:36px;
}
.copyright{
	padding:20px 0;
	text-align: center;
	font-size: 14px;
	color: #7e7f84;
	border-top:1px solid #4c505a;
	line-height:1.5;
	margin-top: 30px;
}
/*圆形背景*/
.round-bg-display{
	display: none;
}
.experience-round1{
	position:absolute;
	z-index:2;
	left:0;
	bottom:-80px;
}
.experience-round2{
	position:absolute;
	z-index:2;
	right:0px;
	top:90px;
}
.buy-process-round{
	position:absolute;
	z-index:2;
	right:0px;
	top:85px;
}
.data-manager-round1{
	position:absolute;
	z-index:2;
	left:30px;
	bottom:-40px;
}
.data-manager-round2{
	position:absolute;
	z-index:2;
	right:0px;
	top:85px;
}

/**
关于我们
**/
.about-header{
	height: 5.28rem;
}
.about-header .banner .text-introduce .p{
	height:60px;	
	overflow: hidden;
}
.about-header .banner .img{
	margin-top: 20px; 
	padding:0 100px;
}
/*特色*/
.feature{
	position: relative;
	padding-top: 20px;	
}
.feature .bd{}
.feature .item{
	position: relative;
	padding-top: 25px;
    padding-bottom: .45rem;
    background-color: #fff;	
	border-top-right-radius:54px;
	border-bottom-left-radius:54px;
	box-shadow:0 0 5px rgba(52,118,254,.2);
	margin-bottom: .5rem;
}
.feature .mr15{
	margin-right: 15px;
}
.feature .item .triangle{
	width:50px;
	height:54px;
	position: absolute;
	left:0;
	top:0;
	font-family: Impact;
	font-size: 20px;
	color: #fff;
	padding-top: 5px;
	padding-left: 5px;
	background: url(../image/feature_icon.png) no-repeat;
	-webkit-background-size: cover;
	background-size: cover;
}
.feature .item .img{
	width:92px;
	height:92px;	
	margin:0 auto;
}
.feature .item .img img{
	width:100%;
}
.feature .item dl{
	max-width:280px;
	margin:0 auto;
	text-align: center;
	color: #21355d;
}
.feature .item dl dt{
	padding-top: .4rem; 
	padding-bottom: .2rem;
	font-weight: bold;
	font-size: 18px;
}
.feature .item dl dd{
	width:80%;
	opacity:.5;
	font-size: 14px;
	line-height:20px;
	margin:0 auto;
}
/*联系我们*/
.contact{
	position: relative;
}
.contact .h1{
	font-size: 22px;
	color: #21355d;
	font-weight: bold;
	text-align: center;
}
.contact .bd{
	padding-top: .7rem;
}
.contact .bd .item{
	display:flex;
	padding-bottom: .7rem;
}
.contact .bd .item .img{
	width: 70px;
	height: 70px;
	margin-right: .3rem;
}
.contact .bd .item .img img{
	width:100%;
}
.contact .bd .item dl{
	color: #21355d;
}
.contact .bd .item dl dt{
	font-size: 18px;
	margin-bottom: .2rem;
}
.contact .bd .item dl dd{
	opacity:.5;
	font-size: 14px;
	line-height:20px;
}
/*地图*/
.map{
	padding:.8rem 0;
	background-color: #fafbff;
}
.map .bd{}
.map .bd .item{}
.map .bd .item img{
	max-width:100%;
}
.map .bd dl{}
.map .bd dl dt{
	font-size: 18px;
	color: #21355d;
	margin-bottom: .4rem;
}
.map .bd dl dd{
	font-size: 16px;
	color: #8799bd;
	opacity:.8;
}
.map .bd dl dd p{
	margin-bottom: .4rem;
}
.map .bd .gps{
	text-align: right;
	padding-bottom: .4rem;
}
.map .bd .gps a{
	color:#3476fe;
	opacity:.8;
	font-weight: bold;
	font-size: 16px;
}
/*投诉查询*/
.query-header{
	height: 5.28rem;
}
.query-header .banner{
	position: relative;
	justify-content:center;
}
.query-header .banner .text-introduce{
	text-align: center;
	margin-top: 20px;
}
.query-header .banner .text-introduce .p{
	height:auto;	
	overflow: hidden;
	margin-top: .34rem;
}
.query-header .banner .img{
	position: absolute;
	margin-top: 0; 
	padding:0;
	top:75px;
	left:50px;
}
.query-header .banner .text-introduce .h1{
	font-size: 20px;
}
/*订单查询*/
.query{
	background-color: #f9fbff;
}
.order-search{
	
}
.order-search-box{
	position: relative;
	z-index:2;
	background-color: #fff;
	margin-top: -1.6rem;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	box-shadow:-2px 0 10px rgba(52,135,254,.2);
	padding:0 .6rem;
	margin-bottom: .6rem;
}
.order-search .title{
	padding-top: .4rem;
	padding-bottom: .40rem;
}
.order-search .title span{
	color:#21355d;
	opacity:.6;
	font-size: 16px;
	margin-right: 1.6rem;
}
.order-search .search{
	display:flex;
	flex-wrap:wrap;
	font-size: 16px; 
	color: #21355d;
}
.order-search .search-select{
	display:flex;
	align-items:center;
	height: .66rem; 
	line-height: .66rem;
	background-color: #fafbff;
	margin-right: .24rem;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	padding-left: .24rem;
	padding-right: .24rem;
	margin-bottom: .3rem;	
}
.order-search .search-select .select-icon{
	width:22px;
	height:22px;
	background: url("/static/theme/blue/images/query_select.png") no-repeat;
	-webkit-background-size: contain;
	background-size: contain;
	margin-left: .4rem;	
}
.order-search .search-select .img img{
	width:100%;
}
.order-search .search-text{
	height: .66rem; 
	line-height: .66rem;
	background: url(../image/query_search.png) 0.24rem center no-repeat #fafbff;
	padding-left: .88rem;
	padding-right: .24rem;
	background-size:22px 22px;
	flex:1;
	margin-right: .24rem;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	border: none;
	margin-bottom: .3rem;
}
.order-search .btn-search{
	height: .66rem; 
	line-height: .66rem;
	background-color: #3476fe;
	color:#fff;
	padding-left: .24rem;
	padding-right: .24rem;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	box-shadow:-2px 2px 10px rgba(52,135,254,.1);
	border: none;
	margin-bottom: .3rem;
    cursor:pointer;	
}
.order-search .statement{
	display:flex;
	align-items:flex-start;	
	line-height:22px;
	flex-wrap:wrap;
	font-size: 14px;
	padding-bottom: .4rem;
}
.order-search .statement p{
	color:#21355d;
	opacity:.5;
	margin-top: 10px;
}
.order-search .statement span{	
	color: #3476fe;
	margin-left: .2rem;
}
.order-search-result{
	background-color: #fff;
	box-shadow:-2px 2px 10px rgba(52,135,254,.1);
	margin-top: .44rem;
	margin-bottom: .2rem;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	padding:.4rem;
}
.order-search-result .title{
	display:flex;
	align-items:flex-start;	
	font-size: 14px;
	background-color: #fafbff;
	padding:.2rem .35rem;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}
.order-search-result .title .order-num{
	flex:1;
	display:flex;
	justify-content:space-between;	
	align-items:center;
	flex-wrap:wrap;
	color:#21355d;
	font-size: 16px;
	line-height:20px;
}
.order-search-result img{
	margin-right: .2rem;
}
.order-search-result .date{
	font-size: 14px;
	background-color: #fafbff;
	opacity:.5;
}
.order-search-result .bd{
	border:1px solid #e8eaee;
	margin-top: .3rem;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
}
.order-search-result .bd .leftdata{
	display:flex;
	justify-content:space-around;
	text-align: center;
	padding:30px 0;
}
.order-search-result .bd .leftdata dl{}
.order-search-result .bd .leftdata dt{
	font-size: 20px; 
	font-weight: bold; 
	color:#21355d;
	margin-bottom: .35rem;
}
.order-search-result .bd .leftdata dd{
	font-size: 16px;
	color:#999999;
}
.order-search-result .bd .leftdata .blue{
	color: #3476fe;
}
.order-search-result .bd .btnbox{
	display:flex;
	align-items:center;
	justify-content:center;
	border-top:1px solid #e8eaee;
	padding: 30px 0;
}
.order-search-result .bd .btnbox .btn-card{
	padding:0 .45rem;
	border:.1rem solid #e1ebff;
	background-color: #3476fe;
	height:50px;
	line-height:40px;
	-webkit-border-radius: 25px;
	-moz-border-radius: 25px;
	border-radius: 25px;
	color:#fff;
	font-size: 18px;	
}
/*分页页码*/
.pagination{
	display:flex;
	justify-content:center;
	align-items:center;
	padding:.4rem 0;
}
.pagination a{
	width:28px;
	height:28px;
	line-height:28px;
	font-size: 14px;
	color:#999999;
	text-align: center;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background-color: #ececee;
	margin:0 .1rem;
	font-weight: bold;
}
.pagination a.dot{
	background-color: transparent;
	font-weight: bold;
}
.pagination .arrow{
	font-family: "宋体";
	font-weight: bold;
	font-size: 16px;
}
.pagination a.active,.pagination a:hover{
	background-color: #3476fe;
	color:#fff;
}
/*帮助中心*/
.helpbox{
	background-color: #f9fbff;
}
.help-header{
	height: 5.25rem;
}
.help-header .banner{
	position: relative;
	align-items:flex-start;
}
.help-header .banner .text-introduce{
	width:100%;
	text-align: center;
	margin-top: 30px;
}
.help-header .banner .text-introduce .p{
	margin-top: .35rem;
    line-height: 1.1;
}
.help-header .banner .img{
	position: absolute;
	margin-top:0;
}
.help-header .banner .img1{
	left:25px;
	top:140px;
}
.help-header .banner .img2{
	right:35px;
	top:180px;
}
.help-search{
	display:flex;
	align-items:center;
	width: 7.3rem;
	max-width:100%;
	height: .64rem;
	line-height:.64rem;
	background-color: #fff;
	-webkit-border-radius: .32rem;
	-moz-border-radius: .32rem;
	border-radius: .32rem;
	margin:.6rem auto 0;
	padding-left: .4rem;    	
}
.help-search .icon{
	width:20px;
	height:20px;
}
.help-search .icon img{
	width:100%;
}
.help-search .text{
	flex:1;
	height:.64rem;
	line-height:.64rem;
	padding:0 .2rem;
	border:none;
	font-size: 16px;
	color:#21355d;
}
.help-search .btn-search{
	height:.4rem;
	line-height:.4rem;
	padding-right: .4rem;
	padding-left: .3rem;
	color: #3476fe;
	font-size: 18px;
	border: none;
	border-left:1px solid #e8eaee;
	background-color: transparent;
	cursor:pointer;
	
}
.helpbox .nav-tabs{
	justify-content:space-between;
	border:none;
	padding:.1rem 0 .05rem;
	margin-bottom: .2rem;
}
.helpbox .nav-tabs .nav-link{
	padding:0;
	color:rgba(34,53,93,.5);
	font-size: 18px;
	position: relative;
	line-height: 1.4;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	border:none;
}
.helpbox .nav-tabs .nav-link:hover{
	border:none;
	padding:0;
	margin:0;
}
.helpbox .nav-tabs .nav-link.active,.helpbox .nav-tabs .nav-link:hover{
	border: none;
	color:#3476fe;
	background-color: transparent;
}
.helpbox .nav-tabs .nav-link.active:before,.helpbox .nav-tabs .nav-link:hover:before{
	content:"";
	display: block;
	width:60%;
	height:4px;
	background-color: #3476fe;
	position: absolute;
	left:50%;
	margin-left: -30%;
	bottom:-.16rem;
}
.helpbox .item{
	background-color: #fff;
	box-shadow:-2px 2px 10px rgba(52,135,254,.1);
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	padding:.4rem;
	margin-top: .4rem;
	line-height:25px;
}
.helpbox .item dl{}
.helpbox .item dt{
	display:flex;
	align-items:flex-start;
	font-size: 18px;
	color:#22355d;
	margin-bottom: .3rem;
}
.helpbox .item dd{
	display:flex;
	align-items:flex-start;
	color:#22355d;
	opacity:.5;
	font-size: 14px;
	
}
.helpbox .item dd p{
	flex:1;
}
.helpbox .item .icon{
	width:30px;
	margin-right: .25rem;
}
.helpbox .item .icon img{
	width:100%;
}
/*咨询详情*/
.bread{
	display:flex;
	align-items:center;
	font-size: 18px;
	color:#b1b6c3;
	margin-top: .5rem;
	margin-bottom: 20px;
	overflow:hidden;
}
.bread a.active,.bread a:hover{
	color: #3484fe;
}
.bread a{
	color:#b1b6c3;
    white-space: nowrap;	
}
.bread a:first-child{
	margin-right: 10px;
}
.acticle-page{
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	box-shadow:0 0 10px rgba(70,70,70,.05);
	padding:0 15px;
}
.acticle-page-header{
	border-bottom:1px solid #e5e5e5;
	padding-top: .65rem; 
	padding-bottom: .35rem;
	color: #22355d;
	font-weight: bold;
	position: relative;
	text-align: center;
}
.acticle-page-header .h1{
	font-size: 20px;
	padding-bottom: .45rem;
}
.acticle-page-header .h2{
	opacity:.5;
	font-size: 14px;
}
.acticle-page-header .share{
	display:flex;
	align-items:center;
    justify-content:center;	
	font-size: 12px;
	color:#333;
	margin-top: 10px;
}
.acticle-page-header .share span{
	padding-right: .1rem;
}
.acticle-page-header .share a{
	width:20px;
	display:block;
	margin-left: .1rem;
    cursor:pointer;	
}
.acticle-page-header .share a img{
	width:100%;
}
.acticle-page-content{
	font-size: 14px;
	line-height:2;
	padding-top: .35rem;
}
.acticle-page-content dl{
	padding-bottom: .6rem;
}
.acticle-page-content dt{
	font-weight: normal;
	color:#3476fe;
	opacity:.6;
}
.acticle-page-content dd{
	color:#22355d;
	opacity:.6;
}
.acticle-other{
	margin-top: .6rem;
	padding-bottom: .6rem;
}
.acticle-other .title{
	display:flex;
	align-items:center;
	font-size: 14px;
	color:#22355d;
	padding-bottom: .2rem;
}
.acticle-other span{
	margin-right: .24rem;
}
.acticle-other .bder{
	flex:1;
	height:1px;
	background-color: #cfd3df;
}
.acticle-other .item{
	align-items:center;
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	box-shadow:0 0 10px rgba(70,70,70,.05);
	padding:.3rem 15px;
	margin-top: .2rem;	
}
.acticle-other .item .date{
	background-color: #fafbff;
	text-align: center;
	padding:.3rem .2rem;	
}
.acticle-other .item .date .h1{
	font-size: .5rem;
	font-family: Impact;
	color:#347afd;
	padding:.18rem .1rem;
	display: inline-block;
	border-bottom:1px solid #cfd3df;
}
.acticle-other .item .date .h2{
	color:#22355d;
	padding:.18rem 0;
	opacity:.5;
	font-size: 16px;
}
.acticle-other .item dl{
	flex:1;
	max-width:950px;
	margin:0 10px;
	line-height:2;
}
.acticle-other .item dt{
	max-width:100%;
	margin-bottom: .4rem;
	line-height:1.2;
}
.acticle-other .item dd{
	max-height:84px;
	overflow:hidden;
	cursor: pointer;
}
.acticle-other .item .more{
	display:block;
	cursor:pointer;
	width:24px;
}
.acticle-other .item .more img{
	width:100%;
}
/*产品服务*/
.feature-product .item .img{
	width: 116px; 
	height: 116px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
}
.feature-product .item dl{
	max-width:none;
}
.feature-product .item dl dt{
	line-height:1.2;
	padding:0 30px;
	margin-bottom: .2rem;
	margin-top: .3rem;
}
.feature-product .item-arrow{
	display:flex;
	justify-content:center;
	align-items:center;
	margin-bottom: .5rem;
}
.feature-product .item-arrow img{
	 transform: rotate(90deg);	
}
.feature-product .item dl dd{
	width:100%;
	padding:0 30px;
	text-align:left;
}
.product-img{
	text-align: center;
	padding-top: .5rem;
	padding-bottom: .7rem;
}
.product-img img{
	max-width:100%;
}
.product-info{
	color: #333333;
	padding-top: .75rem;
	padding-bottom: .7rem;
}
.product-info .h1{
	font-size: 18px;
	font-weight: bold;
	line-height:28px;
}
.product-info .p{
	color: #666666;
	font-size: 14px; 
	line-height:20px;
	margin-top: .2rem;
}
.product2{
	background-color: #fafbff;
}
.column-reverse .row{
	flex-direction: column-reverse;
}
.product3{
	position: relative; 
	z-index:4;
}
.product3 .experience-round1{
	bottom:-220px;
} 
.product-info .text-item{
	display:flex;
	align-items:center;
	color: #333333;
	font-weight: bold;
	font-size: 20px;
}
.product-info .text-item img{
	width:30px;
	margin-right: .3rem;
}
.product-info .text-item span{
	color: #3476fd;
	margin-left: .3rem;
}
.product-info ul li{
	margin-bottom: .54rem;
}
.product-info .wx{
	display:flex;
	justify-content:flex-end;
	align-items:flex-end;
}
.product-info .wx dl{
	color: #666666;
	line-height:1.3;
	font-size: 14px;
	margin-right: .4rem;
}
.product-info .wx dl dt{
	font-weight: normal;
}
.product-info .wx dl span{
	color: #3476fd;
}
.product-info .wx img{
	border:.1rem solid #eee;
}
.product-round1{
	position: absolute;
	left:0;
	bottom:0;
	z-index:3;
}
.product-round2{
	position: absolute;
	top:120px;
	right:120px;
}
