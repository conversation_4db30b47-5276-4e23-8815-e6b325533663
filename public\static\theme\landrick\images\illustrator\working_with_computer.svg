<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="726.63" height="598.15" viewBox="0 0 726.63 598.15">
  <defs>
    <linearGradient id="linear-gradient" x1="362.32" y1="84.46" x2="379.08" y2="84.46" gradientTransform="matrix(1, 0, 0, -1, -55.95, 599.59)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#183866"/>
      <stop offset="1" stop-color="#1a7fc1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="402.86" y1="82.34" x2="410.55" y2="82.34" gradientTransform="matrix(1, 0, 0, -1, -55.95, 599.59)" gradientUnits="userSpaceOnUse">
      <stop offset="0"/>
      <stop offset="0.99" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="420.02" y1="95.05" x2="465.77" y2="115.48" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="419.3" y1="154.46" x2="363.69" y2="135.81" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-5" x1="450.15" y1="94.12" x2="307.62" y2="46.31" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="454.97" y1="79.65" x2="450.03" y2="77.99" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-7" x1="400.43" y1="93.82" x2="357.12" y2="105.59" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-8" x1="399.81" y1="101.15" x2="399.81" y2="101.15" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-11" x1="752.82" y1="149.25" x2="-22.22" y2="111.97" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-12" x1="319.83" y1="269.82" x2="714.72" y2="269.82" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-13" x1="456.76" y1="268.17" x2="498.43" y2="435.22" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-14" x1="388.4" y1="294.66" x2="528.43" y2="341.59" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-15" x1="387.95" y1="329.7" x2="384.29" y2="356.31" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-16" x1="189.55" y1="23.69" x2="121.95" y2="21.11" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-17" x1="199.83" y1="54.7" x2="143.82" y2="52.77" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-18" x1="90.26" y1="102.87" x2="61.93" y2="-33.61" gradientTransform="matrix(1, 0, 0, -1, -0.01, 600)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-19" x1="133.44" y1="70.6" x2="76.41" y2="330.72" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-20" x1="136.63" y1="65.37" x2="129.97" y2="95.76" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-21" x1="128.63" y1="51.78" x2="192.61" y2="270.81" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-22" x1="391.94" y1="480.61" x2="452.67" y2="480.61" gradientTransform="matrix(1, 0, 0, -1, -55.95, 599.59)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ecc4d7"/>
      <stop offset="0.42" stop-color="#efd4d1"/>
      <stop offset="1" stop-color="#f2eac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="404.28" y1="269.89" x2="460.78" y2="269.89" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-24" x1="487.03" y1="288.1" x2="532.04" y2="288.1" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-25" x1="786.69" y1="334.77" x2="536.94" y2="500.11" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-26" x1="743.88" y1="245.64" x2="570.27" y2="407.53" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-27" x1="452.25" y1="77.42" x2="450.57" y2="76.86" xlink:href="#linear-gradient-2"/>
  </defs>
  <title>29. Working in front Computer</title>
  <path d="M537.74,103.94C515.56,78.71,498.39,48.59,471.88,28,430.24-4.41,370.31-7.57,321.62,12.79S235,74.19,210,120.66c-13.15,24.45-23.48,51.12-42.75,71.1-16.29,16.88-37.7,27.65-58,39.38C76.52,250,45,272.79,23.82,304.06S-6.83,376.4,6.71,411.64c9.13,23.78,27.52,42.7,46.36,59.83,22.17,20.14,46,39,73.5,51C161,537.4,199.22,540.61,236.7,542.13a1414.92,1414.92,0,0,0,199.16-6c54.18-5.46,110-14.78,155.33-44.95,63.5-42.25,96.24-119.66,103.61-195.57,3-30.36,2.24-61.91-9.48-90.08C657.29,138.15,581.66,153.91,537.74,103.94Z" transform="translate(-0.01 0)" fill="#2f55d4" opacity="0.18" style="isolation: isolate"/>
  <path d="M268.86,239.22s-25.13-4.42-32.17,13,0,77.58,0,77.58l93.76,66.38Z" transform="translate(-0.01 0)" fill="#3f3d56"/>
  <path d="M387,507.15l-36.37,10.77a10,10,0,0,1-12.4-6.74h0a10,10,0,0,1,6.74-12.4L381.29,488a10,10,0,0,1,12.4,6.73h0A10,10,0,0,1,387,507.15Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <circle cx="384.94" cy="530.18" r="16.76" fill="#25233a"/>
  <circle cx="381.92" cy="531.71" r="8.38" fill="#3f3d56"/>
  <circle cx="317.75" cy="513.62" r="16.76" fill="#25233a"/>
  <path d="M323.13,515.13a8.38,8.38,0,1,1-8.38-8.38A8.38,8.38,0,0,1,323.13,515.13Z" transform="translate(-0.01 0)" fill="url(#linear-gradient)"/>
  <circle cx="312.9" cy="536.54" r="16.76" fill="#25233a"/>
  <path d="M326.15,537.1a8.38,8.38,0,1,1-8.38-8.38,8.38,8.38,0,0,1,8.38,8.38Z" transform="translate(-0.01 0)" fill="#3f3d56"/>
  <path d="M388.32,523.59l-76.65-15.47a10,10,0,0,1-7.8-11.76h0a10,10,0,0,1,11.75-7.8L392.27,504a10,10,0,0,1,7.8,11.76h0A10,10,0,0,1,388.32,523.59Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M341.07,403.17H362v88.15a10.46,10.46,0,0,1-10.46,10.46h0a10.46,10.46,0,0,1-10.46-10.46V403.17Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M348.58,517.8l-36.36,10.79a10,10,0,0,1-12.41-6.74h0a10,10,0,0,1,6.74-12.4l36.36-10.76a10,10,0,0,1,12.4,6.73h0A10,10,0,0,1,348.58,517.8Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M354.6,516.73l-4,1.19a9.94,9.94,0,0,1-3.68.37l1.67-.49a9.74,9.74,0,0,0,3.21-1.63Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-2)"/>
  <path d="M394.11,497.59a9.93,9.93,0,0,1-2.26,6.3l-31.8-6.43a10.29,10.29,0,0,0,1.7-3.64l19.59-5.8a10,10,0,0,1,12.38,6.84A10.12,10.12,0,0,1,394.11,497.59Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-3)"/>
  <path d="M341.34,493.7a10.59,10.59,0,0,1-.27-2.38V403.17h7.27v95a3.48,3.48,0,0,0,.07,1.4,9.47,9.47,0,0,0-3.48-.77,17.94,17.94,0,0,0-2.54,0l.52-.15C343.23,498.59,341.76,495.5,341.34,493.7Z" transform="translate(-0.01 0)" opacity="0.31" fill="url(#linear-gradient-4)" style="isolation: isolate"/>
  <path d="M395.05,522.51a9.82,9.82,0,0,1-4.77,1.23,10.36,10.36,0,0,1-2-.2l-33.72-6.81-2.81-.56a9.74,9.74,0,0,1-3.21,1.63l-1.67.49-34.65,10.3a10,10,0,0,1-12.41-6.74,8.54,8.54,0,0,0,10.87.62c4.23-2.93,21.07-7.33,30.61-9.67a11.72,11.72,0,0,0,8.8-9.27,6.69,6.69,0,0,1,.12-.8c1.59,1.72,4.51,3.65,9.79,5.29,13,4,34.11,6.65,34.11,6.65S397.92,518.41,395.05,522.51Z" transform="translate(-0.01 0)" opacity="0.31" fill="url(#linear-gradient-5)" style="isolation: isolate"/>
  <path d="M398.67,519.15a9.82,9.82,0,0,1-2.49,2.65C396.85,521.11,397.71,520.19,398.67,519.15Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-6)"/>
  <path d="M341.34,493.7a10.4,10.4,0,0,0,2.52,4.74c-.31.06-.63.14-.95.23L311.39,508a10,10,0,0,1,2.24-19.69,10.36,10.36,0,0,1,2,.2Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-7)"/>
  <path d="M343.86,498.44" transform="translate(-0.01 0)" fill="url(#linear-gradient-8)"/>
  <path d="M343.86,498.44" transform="translate(-0.01 0)" fill="url(#linear-gradient-8)"/>
  <path d="M343.86,498.44" transform="translate(-0.01 0)" fill="url(#linear-gradient-8)"/>
  <path d="M389.9,69.09s-.41-31.08-25.32-28.4-23-7.61-33.64-6.48-23,11.63-19.45,24.6S302.1,74.68,293.6,84.52s2,17.89,16.32,27.51-.89,7.13,7.6,22.34,37.23-4.22,37.23-4.22Z" transform="translate(-0.01 0)" fill="#3f3d56"/>
  <path d="M186.71,589.1l17.67.6V448.59s.89-36.49,35.33-40.15,346.78-48.87,346.78-48.87S598.87,358.26,606,382s14.07,117.09,14.07,117.09L715.94,512S718.47,347.1,683.85,338s-487.37,37.27-487.37,37.27S162.9,550.41,186.71,589.1Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M186.71,589.1l17.67.6V448.59s.89-36.49,35.33-40.15,346.78-48.87,346.78-48.87S598.87,358.26,606,382s14.07,117.09,14.07,117.09L715.94,512S718.47,347.1,683.85,338s-487.37,37.27-487.37,37.27S162.9,550.41,186.71,589.1Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-11)"/>
  <path d="M93.47,392.59s8.66-47.92,69.57-60.48L593,282.85s42.21-1.75,61.52,9.94,46.87,21.38,58.17,74.28,14,143.27,14,143.27L716,512S700.4,348.36,683.87,338s-463.46,53.2-463.46,53.2-31,6.17-33.7,52.11,0,145.78,0,145.78L93.47,551.45Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M535.82,301.29c-22.25-2.9-44.68-5-67.1-4.15-16.85.66-33.59,3-50.3,5.33L271.05,323.08c-2.5.35-5.35.92-6.6,3.12-2.4,4.2,3.24,8.34,7.81,9.94,49,17.18,101.35,34.58,151.79,22.33,23.06-5.6,44.48-17.23,67.87-21.27s47.4-.32,70.74-4.13c21.94-3.58,42.83-13.75,65.07-13.85,6.82,0,13.84.88,20.31-1.28s12.19-8.76,10.4-15.34c-20.88-.73-44.32-4.59-64.8-.4C573.37,306.34,556.12,301.29,535.82,301.29Z" transform="translate(-0.01 0)" opacity="0.19" fill="url(#linear-gradient-12)" style="isolation: isolate"/>
  <path d="M402.55,326.41l.86,3.29s25.46,15.83,25.3,16.34,105.61-25.27,105.61-25.27l2.46-7.4Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M402.55,326.41l.86,3.29s25.46,15.83,25.3,16.34,105.61-25.27,105.61-25.27l2.46-7.4Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-13)"/>
  <polygon points="404.92 326.41 497.96 305.44 536.75 313.37 431.05 339.96 404.92 326.41" fill="#2f55d4"/>
  <polygon points="404.92 326.41 497.96 305.44 536.75 313.37 431.05 339.96 404.92 326.41" fill="url(#linear-gradient-14)"/>
  <path d="M375.64,343l22.09.74s5.61.33,7.06-3.93-9-13.25-10.12-13.42-30.19,7.55-26,15.27Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M375.64,343l22.09.74s5.61.33,7.06-3.93-9-13.25-10.12-13.42-30.19,7.55-26,15.27Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-15)"/>
  <path d="M120.31,560.59l-10.75,32.64c-31.18,11.07-57.69,0-57.69,0L44.16,559S83.05,571.89,120.31,560.59Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M120.31,560.59l-10.75,32.64c-31.18,11.07-57.69,0-57.69,0L44.16,559S83.05,571.89,120.31,560.59Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-16)"/>
  <path d="M132.5,530.2,129.05,556l-8.74,4.58C83.08,571.88,44.16,559,44.16,559l-6.71-5L34.32,530.2Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M132.5,530.2,129.05,556l-8.74,4.58C83.08,571.88,44.16,559,44.16,559l-6.71-5L34.32,530.2Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-17)"/>
  <ellipse cx="83.39" cy="530.2" rx="49.08" ry="8.83" fill="#2f55d4"/>
  <ellipse cx="83.39" cy="530.2" rx="49.08" ry="8.83" fill="url(#linear-gradient-18)"/>
  <path d="M60.05,333.18c-4.82-2.28-10-4-14.17-7.33-8.4-6.77-10.53-18.43-13.16-28.89S25,275,14.63,272c-3.88-1.11-8.43-.67-11.38,2.09C.14,277-.49,281.78.36,286s2.92,8,4.52,12a55.17,55.17,0,0,1,3.29,29.31c-1.31,8.11-4.44,16.33-2.43,24.28,3.41,13.49,20.4,21.4,20.87,35.3.32,9.38-7.4,18.06-5.65,27.29,1.29,6.83,7.58,12.16,7.92,19.1.38,7.87-7.06,14.53-6.7,22.4S30.4,468.81,35.53,475a32.17,32.17,0,0,1,7.31,18.35c.27,4.2-.27,8.55,1.1,12.53,1,2.91,3,5.38,4.55,8,1.71,2.83,3,5.89,5.06,8.51a20.82,20.82,0,0,0,22.3,7c1.59-10.46.2-21.13.64-31.7.87-20.48,8.62-41.12,3.66-61-1.82-7.29-5.32-14.21-5.94-21.69-.55-6.68,1.23-13.3,2.47-19.89,2.56-13.61,2.52-27.68,1.47-41.44C77.32,342.75,69.64,337.72,60.05,333.18Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M90.7,386.82c-.08,9.13,2,18.57-.92,27.24-2.21,6.57-7.07,11.95-9.58,18.41-5.79,14.91,2,31.63.41,47.55-.66,6.56-2.91,12.86-4.33,19.3A77.3,77.3,0,0,0,75,524.15a6.28,6.28,0,0,0,5,6.54,78,78,0,0,0,15.15,4.09,40.18,40.18,0,0,1,5.18-28c7.93-13,23.34-21.76,26.21-36.69,1.94-10.11-2.58-20.37-2.26-30.66a33,33,0,0,1,6.74-18.9c3.73-4.86,8.78-8.62,12.25-13.66,8.34-12.15,5.74-28.39,3.26-42.92s-3.92-31.25,5.88-42.26c3.1-3.48,7.14-6.09,9.95-9.81,6.26-8.29,4.68-21.06-2.6-28.47s-19-9.46-28.9-6.2a20.84,20.84,0,0,0-10.54,7.17c-5,7.1-3,16.8-.88,25.24,1.85,7.33,3.34,15.79.81,23.18-1.89,5.5-6.68,8.48-10.75,12.31A58.64,58.64,0,0,0,90.7,386.82Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M80.37,467.72c-1.42,9.92-3.47,19.92-3.89,29.87v1a2.63,2.63,0,0,1-.17.72A77.3,77.3,0,0,0,75,524.14,9.41,9.41,0,0,0,76,528c-.05.42-.11.83-.17,1.25a20.82,20.82,0,0,1-22.3-7c-2-2.62-3.35-5.68-5.06-8.51-1.6-2.63-3.55-5.1-4.55-8-1.37-4-.83-8.33-1.1-12.53a32.17,32.17,0,0,0-7.31-18.35c-5.13-6.15-13-11.31-13.35-19.31s7.08-14.53,6.7-22.4c-.34-6.94-6.63-12.27-7.92-19.1-1.75-9.23,6-17.91,5.65-27.29-.47-13.9-17.46-21.81-20.87-35.3-2-7.95,1.12-16.17,2.43-24.28A55.17,55.17,0,0,0,4.9,297.9c-1.6-4-3.67-7.77-4.52-12s-.22-9,2.89-11.92c3-2.76,7.5-3.2,11.38-2.09,10.38,3,15.45,14.54,18.09,25S37.5,319,45.9,325.78c4.15,3.35,9.35,5.05,14.17,7.33,9.6,4.54,17.28,9.57,18.1,20.39,1,13.76,1.09,27.83-1.47,41.44-1.24,6.59-3,13.21-2.47,19.89s3.44,13,5.34,19.48c.21.73.42,1.47.6,2.21C82.69,446.81,81.88,457.22,80.37,467.72Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-19)"/>
  <path d="M76.05,528A9.41,9.41,0,0,1,75,524.15a77.3,77.3,0,0,1,1.33-24.83c.05-.24.11-.48.17-.72C76.14,508.42,77.28,518.32,76.05,528Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-20)"/>
  <path d="M162.22,311.83c-2.81,3.72-6.85,6.33-9.95,9.81-9.8,11-8.36,27.73-5.88,42.26s5.08,30.77-3.26,42.92c-3.47,5-8.52,8.8-12.25,13.66a33,33,0,0,0-6.74,18.9c-.32,10.29,4.2,20.55,2.26,30.66-2.87,14.93-18.28,23.71-26.21,36.69a40.24,40.24,0,0,0-5.14,28,77.61,77.61,0,0,1-15.11-4.09,8.14,8.14,0,0,1-3.52-2.13,3.77,3.77,0,0,1-.37-.52A9.41,9.41,0,0,1,75,524.15a77.3,77.3,0,0,1,1.33-24.83c.05-.24.11-.48.17-.72,1.42-6.19,3.52-12.26,4.16-18.58a60.2,60.2,0,0,0-.24-12.3c1.51-10.5,2.32-20.91-.23-31.13-.18-.74-.39-1.48-.6-2.21.19-.64.41-1.28.66-1.91,2.51-6.46,7.37-11.84,9.58-18.41,2.91-8.67.84-18.11.92-27.24.13-15.77,7.24-31,18.66-41.76,4.07-3.83,8.86-6.81,10.75-12.31,2.53-7.39,1-15.85-.81-23.18-2.13-8.44-4.15-18.14.88-25.24,2.5-3.52,6.44-5.81,10.54-7.17,9.86-3.26,21.63-1.21,28.9,6.2S168.48,303.59,162.22,311.83Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-21)"/>
  <path d="M449.16-16.85" transform="translate(-0.01 0)" fill="none" stroke="#6c62ff" stroke-miterlimit="10" stroke-width="1.86"/>
  <path d="M339.47,155s-42.07,6.43-61.53,52.3S268.6,310,268.94,315s59.63,26.69,76.58,26.69S366,324.9,366,324.9l-29.95-12.61,91-10.39h16.34l-9,15s17.76,1.84,29.63,2.26,19.93-22.79,21.26-34.33S432.85,172,424.82,166,395,145.34,339.47,155Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M389.9,69.09s7.45,4.25,6.78,18-4.87,34.44-9.11,39.8-19.07,7.46-19.07,7.46,8.09,19.49,14.11,24.52-6.05,23.56-17.79,17.48S339.47,155,339.47,155s9.79-18.39,6.48-28.07S330,83.28,339.47,74,369.36,51.42,389.9,69.09Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-22)"/>
  <polygon points="322.43 221.03 315.83 301.9 335.99 312.29 352.38 309.48 331.93 284.79 322.43 221.03" fill="#fff" opacity="0.25" style="isolation: isolate"/>
  <path d="M428.71,217s26.35,62.71,14.67,84.95l-9-1.73Z" transform="translate(-0.01 0)" fill="#fff" opacity="0.25" style="isolation: isolate"/>
  <path d="M355.05,322s-10.06,9-5.57,16,9.85,6.8,14.32,3.67,13.25-10.56,23.71-7.15,13,5.58,13,5.58,5.51-3.61,4.08-7.23S392.22,317.52,389.05,316s-16.33,3.15-16.33,3.15S363.05,316.41,355.05,322Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-23)"/>
  <path d="M446.37,305.11s-9.58,1.1-12,7.18-3.29,6.82-3.29,6.82,5.41,1,7.66-1.83,4.59-1.64,4.59-1.64l-.67,3.63s14.7,2.14,21.34-.16,9.49-5,9.49-5l2.6-6.14s-6.27-4.39-11-5.07S446.37,305.11,446.37,305.11Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-24)"/>
  <path d="M495,372.72s32.63,24.84,29.73,58.44-7.16,83.49-7.16,83.49-13.16,5.54-21.88,0-14-81.35-18.32-88.59-30.31-39.88-58.08-42.69Z" transform="translate(-0.01 0)" fill="#25233a"/>
  <path d="M330.45,396.14s15.73,15,43.77,19.71,41.11,8.64,49.68,32.75,16.79,76,19.48,81.8,14.13,2.84,14.13,2.84,6.47-7.42,6.47-48.68c0-57.52,4-69.31-10.41-83.76s-27.52-18.39-27.52-18.39Z" transform="translate(-0.01 0)" fill="#3f3d56"/>
  <path d="M443.38,530.4l2.67,21.05h5s32.2,1.76,33.21,1-1.55-16.67-13.75-16.22-13-3-13-3Z" transform="translate(-0.01 0)" fill="#25233a"/>
  <path d="M499.62,516.38l5.2,20.72,56.82-7.8s2-13.61-16-12.26c-4.75.35-8.29,3.69-14,3.72s-11.18-2.31-14.06-6.11h0Z" transform="translate(-0.01 0)" fill="#25233a"/>
  <path d="M697.75,255.72,482.68,267.24a9.44,9.44,0,0,1-9.92-8.92c0-.16,0-.33,0-.49V105.42a11.35,11.35,0,0,1,11.11-11.34l214.19-4.42a10.36,10.36,0,0,1,10.57,10.15V244.25A11.5,11.5,0,0,1,697.75,255.72Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M707.67,255.72,492.6,267.24a9.44,9.44,0,0,1-9.91-8.93c0-.16,0-.32,0-.48V105.42a11.33,11.33,0,0,1,11.1-11.34L708,89.66A10.36,10.36,0,0,1,718.55,99.8V244.25A11.5,11.5,0,0,1,707.67,255.72Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M707.67,255.72,492.6,267.24a9.44,9.44,0,0,1-9.91-8.93c0-.16,0-.32,0-.48V105.42a11.33,11.33,0,0,1,11.1-11.34L708,89.66A10.36,10.36,0,0,1,718.55,99.8V244.25A11.5,11.5,0,0,1,707.67,255.72Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-25)"/>
  <path d="M645.05,313.29l-54.61,5.82s-16.63-3.36-15.73-5.82S593,308.46,593,308.46V209.3h0c-.15-.22-2.44-3.14-8.71-5h0a31.49,31.49,0,0,0-3.7-.86,10.9,10.9,0,0,1-3.1-.94c-3.73-1.93.39-5.29,6.18-5.44a14.46,14.46,0,0,1,2.71.16h42s15.9,4.48,16.67,26.84S645.05,313.29,645.05,313.29Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M600.61,313.5l-10.2,5.61s-16.63-3.36-15.73-5.82S593,308.46,593,308.46V209.3h0c-.21-.23-2.76-2.86-8.71-5h0a44.22,44.22,0,0,0-6.8-1.8c-3.73-1.93.39-5.29,6.18-5.44,4.93.67,14.64,6.07,15.27,9.15S600.61,313.5,600.61,313.5Z" transform="translate(-0.01 0)" fill="#2f55d4"/>
  <path d="M600.61,313.5l-10.2,5.61s-16.63-3.36-15.73-5.82S593,308.46,593,308.46V209.3h0c-.21-.23-2.76-2.86-8.71-5h0a44.22,44.22,0,0,0-6.8-1.8c-3.73-1.93.39-5.29,6.18-5.44,4.93.67,14.64,6.07,15.27,9.15S600.61,313.5,600.61,313.5Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-26)"/>
  <path d="M396.18,521.8c-1.27,1.3-1.86,1.78-1.11.71A8,8,0,0,0,396.18,521.8Z" transform="translate(-0.01 0)" fill="url(#linear-gradient-27)"/>
</svg>
