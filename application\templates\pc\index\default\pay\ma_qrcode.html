<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <link rel="stylesheet" href="__RES__/app/theme/default/css/bootstrap-reboot.min.css">
        <link rel="stylesheet" href="__RES__/app/theme/default/css/bootstrap-grid.css">
        <link rel="stylesheet" href="__RES__/app/theme/default/css/pc_qrcode.css">
        <title>收银台 - {:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
    </head>
    <body>

        <header class="header">
            <div class="header__wrap">
                <div class="container">
                    <div class="row ">
                        <div class="col-12">
                            <div class="header__content d-flex justify-content-between">
                                <div class='header__logo d-flex align-items-center'>         
                                    <svg t="1610806307396" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6171" width="26" height="26"><path d="M1024 199.18v410.38c0 110-89.54 199.18-200 199.18H200c-110.46 0-200-89.18-200-199.18V199.18C0 89.17 89.54 0 200 0h624c110.46 0 200 89.17 200 199.18z m-553.95 317v46.72q0.9 19.32 12 28.75t30.9 9.43q40.14 0 41.95-38.18v-47.58l86.6 0.45q11.73-0.9 18.49-8.76t7.67-19.54a33.48 33.48 0 0 0-7.67-19.32q-6.77-8.09-18.49-9h-86.6v-27.4l86.15-0.45q11.73-0.9 18.72-9a33.26 33.26 0 0 0 7.89-19.76q-0.9-11.23-7.67-18.42t-18.49-8.09h-66.3l69.91-113.2q9-11.68 9-24.71a50.37 50.37 0 0 0-4.28-15.27 24.48 24.48 0 0 0-7.22-9 27.29 27.29 0 0 0-9.92-4.49 74.75 74.75 0 0 0-12.4-1.8 43.43 43.43 0 0 0-19.4 7.19 54.51 54.51 0 0 0-14 13.48l-75.34 125.83L443 229.18A65.48 65.48 0 0 0 429 215a36.39 36.39 0 0 0-19.4-7.41q-18.49 2.25-25.26 10.11t-9 20.44a36.94 36.94 0 0 0 3.61 18.19 67.53 67.53 0 0 0 8.57 13.7l60.44 106H383q-12.18 0.9-18.72 8.09t-7.89 18.42q1.35 11.68 7.89 19.32t18.72 8.56l87.05 0.45v28.3H383q-12.18 0.9-18.72 8.09t-7.89 18.42a43.81 43.81 0 0 0 7.89 20.44q6.54 9.21 18.72 10.11h87.05z" fill="#4375ff" p-id="6172"></path><path d="M264.96 903.6m60.2 0l373.67 0q60.2 0 60.2 60.2l0 0q0 60.2-60.2 60.2l-373.67 0q-60.2 0-60.2-60.2l0 0q0-60.2 60.2-60.2Z" fill="#4375ff" p-id="6173"></path></svg>

                                    <span class="ml-2">收银台</span>
                                </div>
                                <a href="/orderquery" class="header__order">
                                    <svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="18" height="18"><path d="M455.253333 657.92c117.76 0 213.333333-95.573333 213.333334-213.333333s-95.573333-213.333333-213.333334-213.333334-213.333333 95.573333-213.333333 213.333334 95.573333 213.333333 213.333333 213.333333z m229.76-22.4l169.813334 169.813333c16.64 16.64 16.64 43.733333 0 60.373334-16.64 16.64-43.733333 16.64-60.373334 0l-172.8-172.8c-47.573333 32-104.746667 50.56-166.4 50.56-164.906667 0-298.666667-133.76-298.666666-298.666667s133.76-298.666667 298.666666-298.666667 298.666667 133.76 298.666667 298.666667c0 72.32-25.813333 138.88-68.906667 190.72z"  fill="#ffffff"></path></svg>
                                    <span>订单查询</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <section class='section details__section section--first  section--last'>
            <div class='container'>

                <div class="row">
                    <div class="col-12">
                        <div class="time">
                            <span>请扫描二维码完成支付 </span> 
                        </div>

                        <div class="order">
                            <span>订单号：{$order.trade_no}</span> 
                            <span data-clipboard-text="{$order.trade_no}" class="copy" id="clipboard_order">复制</span>
                        </div>

                        <div class="goods_name">
                            <span>{$order.goods_name}</span> 
                            {if isset($order.quantity)}<span>x{$order.quantity}</span>{/if}
                        </div>

                        <div class="pay_type mt-4">
                            <img src="{$order.channel.paytypes.ico}" /> 
                            <span>{$order.channel.show_name}</span>
                        </div>

                        <div class="code_cs" style="display:none"><img src="data:image/png;base64,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"></div>

                        <img src="{:url('resource/generateQrcode',['str'=>urlencode($pay_url)])}" class="code" /> 

                        <div class="price mt-4" style="letter-spacing: 1.5px;">
                            请付款 <span>{$price}</span> <span>元</span>，注意不能多付或少付
                        </div>
                        <div class="price mt-2"  style="letter-spacing: 1.5px;">
                            请在规定时间内及时付款，失效请勿付款，如支付完成请您耐心等待15秒。
                        </div>

                        <style>
                            .time-item strong {
                                background: #3ec742;
                                color: #fff;
                                line-height: 25px;
                                font-size: 17px;
                                font-family: Arial;
                                padding: 5px 10px;
                                margin-right: 10px;
                                border-radius: 5px;
                                box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
                            }
                        </style>

                        <div class="time-item mt-4">
                            <strong id="hour_show"><s id="h"></s>0时</strong>
                            <strong id="minute_show"><s></s>00分</strong>
                            <strong id="second_show"><s></s>00秒</strong>
                        </div>

                        <div class="shanxinzha">
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAACiElEQVR4nO2a0XHDIAyGGcEjZISMkBE6QkfICNmgI3QEj+ARMoIegJ9Hj9A+WNy1vcbItrCcHN8drwYJ6UcWONdoNBqNRqNRESLqvPeXGOMNwAeAAcAdwMjjDqBPKX3GGG/e+4v1mjeTjU4pfbKRXytGH0J4t7ZlEUTUAbgCoJVGPxpXa9uKeO8vHM6ahv8cdMiI4F3vKxr+d9yJqLO22znnXAjhXCHcJWMMIZxNjeeQXytwWuPNxPgQwvsBjLdxAof9UuNHAEM+50MIZyLqiKjj771hOj2GNU7YLR1Y8JYo/QjgulS02BmLHLyLMEKu9mOM8bZ1UVw9Sp0waNn5Lxym0l1Xy8slKVetTlgQ+veU0kl7/pTSSTj/qD23c06ck1XPZt6EYiTEGG8WE6uG/SO49ihuhGoUCnP/Q23CAhJhVNUCgfLvcwQxHJFUWFO/22TqOSeAK9H6YigJ/xqqX4I3pn4aCNS/bvExv7bZ1FSJTG5pHSr8M4I02K4DAgG0+R11oiNxe3SiUH1ZNiUEOrBdCEsngIUAZvZywGwFaN2bK2yQigMOmwK8vjmNUtGAYc4B1rc4/Jf47+6rrA3TVdZcnplfWHC/IEfCCGBQi0yBA3Rq7qMCQSlsLYRVkfQCDnldpUmpHFY5bo6MoOY2/SeojrQXZ30kVgXCpqhlaVydQtmZB72sEyRaoFqFHZHSv8GfsVuneDeEHdlf0fBydQLX3lIH/CqbAVz59djJ2o5NCG9oJIMwvRE8Wdu0GEUn5JLa9s3PGpQfST3nn+WKVyMPBdPalk1g+wvR53ZABsvf9+RhdstUBXaE+E3Ry1aPzk2dJb7X7zE1WnOq6PbwGo1Go9FoNIR8A0fjmlx859ePAAAAAElFTkSuQmCC"> 
                            <span>正在检测付款状态 <p>1</p> 次</span>
                        </div>
                    </div>

                </div>

            </div>
        </section>


        <script src="__RES__/app/theme/default/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/app/js/clipboard.js"></script>
        <script src="__RES__/app/layer/layer.js"></script>

        <script>
            var maxtime = {$time} - (parseInt('{:time()-$order.create_at}'));
            timer(maxtime);
            var check = setInterval("CheckStatus()", 2500);
            var myTimer;
            function timer(intDiff) {
                var i = 0;
                i++;
                var day = 0,
                        hour = 0,
                        minute = 0,
                        second = 0;//时间默认值
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9)
                    minute = '0' + minute;
                if (second <= 9)
                    second = '0' + second;
                $('#hour_show').html('<s id="h"></s>' + hour + '时');
                $('#minute_show').html('<s></s>' + minute + '分');
                $('#second_show').html('<s></s>' + second + '秒');
                if (hour <= 0 && minute <= 0 && second <= 0) {
                    qrcode_timeout()
                    clearInterval(myTimer);

                }
                intDiff--;

                myTimer = window.setInterval(function () {
                    i++;
                    var day = 0,
                            hour = 0,
                            minute = 0,
                            second = 0;//时间默认值
                    if (intDiff > 0) {
                        day = Math.floor(intDiff / (60 * 60 * 24));
                        hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                        minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                        second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                    }
                    if (minute <= 9)
                        minute = '0' + minute;
                    if (second <= 9)
                        second = '0' + second;
                    $('#hour_show').html('<s id="h"></s>' + hour + '时');
                    $('#minute_show').html('<s></s>' + minute + '分');
                    $('#second_show').html('<s></s>' + second + '秒');
                    if (hour <= 0 && minute <= 0 && second <= 0) {
                        qrcode_timeout()
                        clearInterval(myTimer);
                        clearInterval(check);
                    }
                    intDiff--;
                }, 1000);
            }


            function qrcode_timeout() {
                $('.time span').text("支付超时，请重新选择商品支付 ");
                $('.code').hide();
                $('.code_cs').show();
                $('.shanxinzha').hide();
                $('.price').hide();
            }


            var i = 0;
            function CheckStatus()
            {
                i++;
                $('.shanxinzha p').text(i);
                $.ajax({
                    url: "{:url('pay/check_order_status')}",
                    type: 'post',
                    data: {out_trade_no: '{$order.trade_no}'},
                    success: function (res) {
                        if (res.code == 1) {
                            clearInterval(check);
                            layer.msg('恭喜您，付款成功。');
                            setTimeout(function () {
                                window.location.href = res.url;
                            }, 500);
                        }

                    }
                });

            }

            function PrefixZero(num, n) {
                return (Array(n).join(0) + num).slice(-n);
            }


            $('#clipboard_order').click(function () {
                var clipboard = new ClipboardJS('#clipboard_order');
                clipboard.on('success', function (e) {
                    layer.msg("复制成功！");
                });
                clipboard.on('error', function (e) {
                    layer.msg("复制失败！");
                });
            });



        </script>

        {if plugconf('codepay','audio')=='1'}
        <script>
            var url = "http://tts.baidu.com/text2audio?lan=zh&ie=UTF-8&text=" + encodeURI("温馨提醒：请您在规定时间内付款，注意不能多付或少付。");
            var audio = new Audio(url);
            audio.src = url;
            audio.play();
        </script>
        {/if}
    </body>
</html>