{extend name="simple_base"}

{block name="css"}
<link rel="stylesheet" href="__RES__/merchant/lottery/css/lottery.css">
<script src="__RES__/merchant/lottery/js/vue.min.js"></script>
<style>
    .lottery{
        width: 100%;
    }
    .lottery .lottery-item ul li .box p{
        font-size: 12px;
    }
    .box p{
        color: #fff;
    }
    .lottery-item ul{
        padding:0px;
    }
</style>
{/block}

{block name="content"}

<div class="container">


    <div class="col-xl-12">
        <div class="card">
            <div class="card-body p-3">

                <div>
                    <h4 class="card-title mb-4">{$_title}</h4>
                    <div class="row mb-2">

                        <div class="col-lg-6">
                            <div class="lottery-box" id="app">
                                <div class="lottery">
                                    <div class="lottery-item">
                                        <div class="lottery-start">
                                            <div class="box gray" v-if="isStart===0">
                                                <p>活动未开始</p>
                                            </div>
                                            <div class="box" @click="startLottery" v-if="isStart===1">
                                                <p><b>抽奖</b></p>
                                                <p>剩余{$count-$use_count}抽奖机会</p>
                                            </div>
                                            <div class="box gray" v-if="isStart===2">
                                                <p>活动已过期</p>
                                            </div>
                                        </div>
                                        <ul>
                                            <li v-for="(item,i) in list" :class="i==index?'on':''">
                                                <div class="box">
                                                    <p><img :src="item.image" alt=""></p>
                                                    <p>{{item.name}}</p>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- 中奖弹窗 -->
                                <div class="mask" v-if="showToast"></div>
                                <div class="lottery-alert" v-if="showToast">
                                    <h1>恭喜您</h1>
                                    <p><img :src="list[index].image" alt=""></p>
                                    <h2>获得{{list[index].name}} <span  v-if="list[index].price>0">价值：{{list[index].price}}元</span></h2>
                                    <div class="btnsave" @click="btnsave">确定</div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="alert alert-success" role="alert">
                                流水到达￥{$stream}元次日即可获得一次抽奖机会（可多次叠加获得），抽奖机会次日及时使用过期不保留！中奖后联系平台客服，本平台拥有活动最高解释权。
                            </div>
                            <div class="alert alert-warning" role="alert">
                                昨日流水 <b>{$user_stream}</b> 元，获得抽奖次数 <b>{$count}</b> 次
                            </div>

                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title">最新中奖用户</h4>
                                    <table class="table m-0">
                                        <tbody>
                                            {foreach $list as $k=> $v}
                                            <tr>
                                                <th scope="row">
                                                    {$v->user->username}
                                                </th>
                                                <td>
                                                    {$v->name}
                                                </td>
                                                <td>
                                                    {$v.create_at|date="Y-m-d H:i:s",###}
                                                </td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title">我的中奖列表</h4>
                                    <table class="table m-0">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th >奖品</th>
                                                <th >状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {foreach $list as $k=> $v}
                                            <tr>
                                                <th scope="row">
                                                    <div class="d-flex align-items-center">
                                                        <span>{$v.create_at|date="Y-m-d H:i:s",###}</span>
                                                    </div>
                                                </th>
                                                <td>
                                                    <b>{$v.name}</b>
                                                </td>
                                                <td>
                                                    {if $v.status==0}
                                                    <span class="badge badge-pill badge-soft-warning font-size-12 font-weight-bold">待发放</span>
                                                    {elseif $v.status==1/}
                                                    <span class="badge badge-pill badge-soft-success font-size-12 font-weight-bold">已发放</span>
                                                    {/if}
                                                </td>
                                            </tr>
                                            {/foreach}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <nav aria-label="...">
                        {$page}
                    </nav>
                </div>
            </div>
        </div>
    </div>

</div>

{/block}
{block name="js"}
<script>

new Vue({
    el: "#app",
    data: {
        isStart: 1,
        score: 10, //消耗积分
        list: {$gift | json_encode}, //奖品1-9     
        index: -1, // 当前转动到哪个位置，起点位置
        count: 8, // 总共有多少个位置
        timer: 0, // 每次转动定时器
        speed: 200, // 初始转动速度
        times: 0, // 转动次数
        cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
        prize: -1, // 中奖位置
        click: true,
        showToast: false, //显示中奖弹窗        
    },
    mounted() {},
    methods: {
        startLottery() {
            if (!this.click) {
                return
            }

            var c = '{$count - $use_count}';
            var cc = parseInt(c);
            if (cc <= 0)
            {
                $.alert("无抽奖机会");
            } else {
                this.startRoll();
            }
        },
        // 开始转动
        startRoll() {
            this.times += 1 // 转动次数
            this.oneRoll() // 转动过程调用的每一次转动方法，这里是第一次调用初始化 
            // 如果当前转动次数达到要求 && 目前转到的位置是中奖位置
            if (this.times > this.cycle + 10 && this.prize === this.index) {
                clearTimeout(this.timer)  // 清除转动定时器，停止转动
                this.prize = -1
                this.times = 0
                this.speed = 200
                this.click = true;
                var that = this;
                setTimeout(res => {
                    that.showToast = true;
                }, 500)
            } else {
                if (this.times < this.cycle) {
                    this.speed -= 10  // 加快转动速度
                } else if (this.times === this.cycle) {

                    var that = this;
                    $.ajax({
                        type: "POST",
                        url: "{:url('Plugin/lotteryPlay')}", //上传路径
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            if (data.code == 1)
                            {
                                that.prize = data.data.index;
                                if (that.prize > 7) {
                                    that.prize = 7
                                }
                            } else {
                                $.alert(data.msg);
                            }

                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            alert("抽奖失败");
                        }
                    });
//                    const index = parseInt(Math.random() * 10, 0) || 0;  // 随机获得一个中奖位置
//                    this.prize = index; //中奖位置,可由后台返回 
//                 
                } else if (this.times > this.cycle + 10 && ((this.prize === 0 && this.index === 7) || this.prize === this.index + 1)) {
                    this.speed += 110
                } else {
                    this.speed += 20
                }
                if (this.speed < 40) {
                    this.speed = 40
                }
                this.timer = setTimeout(this.startRoll, this.speed)
            }
        },
        // 每一次转动
        oneRoll() {
            let index = this.index // 当前转动到哪个位置
            const count = this.count // 总共有多少个位置
            index += 1
            if (index > count - 1) {
                index = 0
            }
            this.index = index
        },
        btnsave() {
            this.showToast = false;
            window.location.reload();
        }
    }

})


</script>
{/block}


