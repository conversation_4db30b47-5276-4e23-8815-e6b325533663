<?php

namespace app\pay\controller;

use service\PayService;
use think\Request;

class JinRuiRsa extends PayService {

    public function pay($trade_no, $subject, $totalAmount) {
        $order = $this->loadOrder($trade_no);

        $sub_mch_id = $order->channelAccount->params->sub_mch_id;
        $channel_type = $order->channelAccount->params->channel_type;


        $open_userid = $order->channelAccount->params->open_userid;
        $open_userkey = $order->channelAccount->params->open_userkey;

        $notifyurl = url("pay/JinRuiRsa/notify");

        $scheme = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';


        $param = [
            'open_userid' => $open_userid,
            'res_body' => [
                'channel_type' => $channel_type,
                'total_fee' => $totalAmount,
                'pay_name' => $subject,
                'pay_body' => $subject,
                'notify_url' => $notifyurl,
                'out_trade_no' => $trade_no,
                'user_ip' => Request::instance()->ip(),
                'server_url' => $scheme . $_SERVER['HTTP_HOST'],
            ],
            'service' => 'gateway.unified.pay',
            'sign_type' => 'RSA2',
            'version' => '2.0'
        ];

        if ($sub_mch_id == "") {
            $param['res_body']['sub_type'] = "SYSTEM";
        } else {
            $param['res_body']['sub_mch_id'] = $sub_mch_id;
        }

        ksort($param['res_body']);
        $param['res_body'] = json_encode($param['res_body'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $param['sign'] = $this->make_sign($param, $open_userkey);
        $res = $this->curl('/', $param);

        $res = json_decode($res, true);
        if ($res['rsp_code'] == "0000") {
            if ($channel_type == "ALIPAY") {
                $pay_url = $res['request_array']['pay_url'];
                return $this->qrcode($pay_url);
            } elseif ($channel_type == "WECHAT_MP") {
                $pay_url = $res['request_array']['pay_url'];
                return $this->qrcode($pay_url);
            } elseif ($channel_type == "WECHAT_H5") {

                $pay_url = $res['request_array']['wechat_redirect'];
                header("location: $pay_url");
            } elseif ($channel_type == "QQPAY") {
                $pay_url = $res['request_array']['pay_url'];

                if (strpos($_SERVER['HTTP_USER_AGENT'], 'QQ/') !== false) {
                    header("location: $pay_url");
                } else {
                    return $this->qrcode($pay_url);
                }
            }
        } else {
            echo $res['rsp_msg'];
        }
    }

    private function curl($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://openapi.minda-group.com' . $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); //单位 秒，也可以使用
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    private function make_sign($param, $priKey) {
        ksort($param);
        $json_data = json_encode($param, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $res = "-----BEGIN RSA PRIVATE KEY-----\n" .
                wordwrap($priKey, 64, "\n", true) .
                "\n-----END RSA PRIVATE KEY-----";
        ($res) or die('您使用的私钥格式错误，请检查RSA私钥配置');

        openssl_sign($json_data, $sign, $res, OPENSSL_ALGO_SHA256);
        $sign = base64_encode($sign);
        return $sign;
    }

    /**
     * 页面回调
     */
    public function callback() {
        $out_trade_no = input("out_trade_no");
        header("Location:" . url('index/Pay/pay_result', ['orderid' => $out_trade_no]));
        die;
    }

    /**
     * 服务器回调
     */
    public function notify() {
        $out_trade_no = input("out_trade_no");
        $order = $this->loadOrder($out_trade_no);

        $res = "-----BEGIN PUBLIC KEY-----\n" .
                wordwrap('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCHZHdWwJoBhi5w8c37gKotBf8Ay/aMjx2uFtsSm82Dyf0fQWmdFojSd6bbpU90IohueabOj/mbb73Zce3JWiwOTVYRJGmrXBAIzZmYusBxUbqsSPGgIPCSR6DuYyzRVOx3JxVqydq1M6hp52/swOEwNic3ciFgkmhHae/zEj+yFiuEhOkwLSSltksEeThVaa6aYrVyP9ka8neAvUHfTyiegVv+vhEc1MOh/O6WRkeJ/g4VR21c1pnh9ymIJhGAI3H8yemKHEw5PStqiBzlUKx2m2p8koxZs9Ifu9ZBKLKcLNtifN9DqYeXRVwP8VHuN2Jb589KnLg1+vFKpc5Z9jwIDAQAB', 64, "\n", true) .
                "\n-----END PUBLIC KEY-----";
        ($res) or die('您使用的公钥格式错误，请检查RSA公钥配置');


        $param = [
            'open_userid' => $order->channelAccount->params->open_userid,
            'out_trade_no' => input("out_trade_no"),
            'system_order_id' => input("system_order_id"),
            'pay_external_id' => input("pay_external_id"),
            'total_fee' => input("total_fee")
        ];

        ksort($param);

        $json_data = json_encode($param, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $result = FALSE;
        $result = (@openssl_verify($json_data, base64_decode($_POST['sign']), $res, OPENSSL_ALGO_SHA256) === 1);

        if ($result) { // 建议此处还是判断下sign是否为空值
            # SUCCESS 校验成功了,再此处实现业务逻辑
            $order->transaction_id = input("system_order_id");
            $this->completeOrder($order);
            echo 'SUCCESS'; // 输出SUCCESS则判断为通知成功
        } else {
            echo 'NO';
        }
    }

}
