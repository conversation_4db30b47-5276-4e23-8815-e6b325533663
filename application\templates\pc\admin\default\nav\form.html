<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">导航名称</label>
        <div class="layui-input-block">
            <input type="text" name="title" value='{$vo.title|default=""}' required="required" title="请输入菜单名称" placeholder="请输入菜单名称" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">导航链接</label>
        <div class="layui-input-block">
            <input type="text" onblur="(this.value === '') && (this.value = '')" name="url" autocomplete="off" required="required" title="请输入菜单链接" placeholder="请输入菜单链接" value="{$vo.url|default='#'}" class="layui-input typeahead">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">打开新窗口</label>
        <div class="layui-input-block">
            <select name="target" class="layui-input" style="display:block;" required>
                <option value="0" {if isset($vo.target) && $vo.target == 0}selected{/if}>否</option>
                <option value="1" {if isset($vo.target) && $vo.target == 1}selected{/if}>是</option>
            </select>
        </div>
    </div>

    <div class="hr-line-dashed"></div>

    <div class="layui-form-item text-center">
        {if isset($vo['id'])}<input type='hidden' value='{$vo.id}' name='id'/>{/if}
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>

    <script>
        require(['bootstrap.typeahead'], function () {
            var subjects = JSON.parse('{$nodes|json_encode}');
            $('.typeahead').typeahead({source: subjects, items: 5});
        });
    </script>

</form>
