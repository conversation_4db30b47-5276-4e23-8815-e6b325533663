{extend name="lite_base" /}

{block name="content"}
<div class="card mb-0">

    <div class="card-content">
        <div class="card-body p-3">

            <p class="mb-2" style="word-wrap: break-word;white-space: normal;word-break: keep-all;">卡号:{$card.number}<a href="javascript:;" class="btn btn-primary btn-sm waves-effect waves-light clipboard" data-clipboard-text="{$card.number}">复制</a>
            </p>
            <p class="mb-2" style="word-wrap: break-word;white-space: normal;word-break: keep-all;">密码:{$card.secret}<a href="javascript:;" class="btn btn-primary btn-sm waves-effect waves-light clipboard" data-clipboard-text="{$card.secret}">复制</a>
            </p>
        </div>
    </div>

</div>
{/block}

{block name="js"}
<!-- BEGIN: Page JS-->
<script src="__RES__/app/js/clipboard.js"></script>
<script>

    var clipboard = new ClipboardJS('.btn');
    clipboard.on('success', function (e) {
        layer.msg('复制成功！', {
            icon: 1
        });
    });

    clipboard.on('error', function (e) {
        layer.msg('复制失败，请手动复制！', {
            icon: 2
        });
    });
</script>

<!-- END: Page JS-->
{/block}
