﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title>{if condition="$shop['shop_name']"}{$shop.shop_name} - {/if}{:sysconf('site_name')}</title>
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />

        <link rel="stylesheet" href="__RES__/app/theme/default/css/mobile_main.css">

        <link href="__RES__/app/css/nyro.css" rel="stylesheet" type="text/css">
        <script src="__RES__/app/js/jquery.min.js"></script>
        <script src="__RES__/app/js/nyro.js"></script>
        <script src="__RES__/app/theme/default/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/app/layer/layer.js"></script>
        <script src="__RES__/app/theme/default/js/woodyapp_mobile.js?_v={:date('YmdHi')}"></script>
        <script>
            var user_popup_message = '';
            var is_display = '0';
            var userid = "{$shop.id}";
            var cateid = 0;
            var static_url = '__PUBLIC__';
            var dis_pwd_content = '<div style="padding:10px;color:#cc3333;line-height:24px"><p style="float:left;font-size:14px;font-weight:bold;color:blue;">访问密码：</p><div style="float:right; font-size:14px; padding-left:20px;"><a href="#" style="color:#0064ff;text-decoration:none;display:inline-block;{if isset($goods)}display:none;{/if}background:url(__RES__/app/images/x.png) left no-repeat;padding-left:15px;" class="nyroModalClose" onclick="closeNyro()">关闭</a></div><p style="clear:both;font-size:12px;font-weight:bold;color:red;"><input type="password" name="pwdforbuy" class="input" maxlength="20"> <input type="submit"  onclick="verify_pwdforbuy()" id="verify_pwdforbuy" style="font-size:12px;padding:3px 3px" value="验证密码"> <span id="verify_pwdforbuy_msg" style="display:none"><img src="__RES__/app/images/load.gif"> 正在验证...</span><ul><li>1.本商品购买设置了安全密码</li><li>2.只有成功验证密码后才能继续购买</li></ul></p></div>';
            var goodid = "{$goods.id|default=0}";
            var is_contact_limit = '{$goods.contact_limit|default=""}';
            var is_contact_limit_default = '{$goods.contact_limit|default=""}';
            var limit_quantity_tip = '无法修改购买数量，是因为本商品限制了购买数量。';
            var notice = "{$shop['shop_notice']}";
            /* {if !isset($goods)} */

            function closeNyro() {
                $('[name="goodid"]').val('');
                $('[name="goodid"]').change();
            }

            /* {/if} */

        </script>


    </head>
    <body>
        <style>
            .bangz_box {
                position: fixed;
                right: 0;
                top: 100px;
                z-index: 45;
            }
            .bangz_box .item {
                padding: 7px;
                background: linear-gradient(45deg,#3798f7,#3369ff);
                box-shadow: 0 0.093333rem 0.133333rem 0 rgb(54 144 248 / 23%);
                border-radius: 7px 0 0 7px;
                margin-top: 15px;
            }
            .bangz_box .item:first-child {
                background: linear-gradient(45deg,#fd0b27,#ff4a4a);
                box-shadow: 0 7px 10px 0 rgb(255 113 19 / 23%);
            }
            .bangz_box .item:nth-child(2) {
                background: linear-gradient(45deg,#f737e8,#3369ff);
                box-shadow: 0 0.093333rem 0.133333rem 0 rgb(54 144 248 / 23%);
            }
            .bangz_box .item a{
                text-decoration: none;
                text-align: center;
                color:#FFF
            }
            .bangz_box span{
                margin-left: 5px;
                font-weight: 600;
                font-size: 10px;
                color: #fff;
            }
        </style>
        <div class="bangz_box">
            <div class="item">
                <a href="/complaint">
                    <span>投诉商家</span>
                </a>
            </div>
            <div class="item">
                <a href="//wpa.qq.com/msgrd?v=1&uin={$shop.qq}&site=&menu=yes" target="_blank"> 
                    <span>卖家客服</span>
                </a>
            </div>

            {if $shop.qqqun!=""}
            <div class="item">
                <a href="{$shop.qqqun|htmlspecialchars_decode|removeXSS}" target="_blank"> 
                    <span>商家Q群</span>
                </a>
            </div>
            {/if}
        </div> 
        <!-- header -->
        <header class="header">
            <div class="header_box">
                <img src="__RES__/app/theme/default/img/shop_img_w.png" class="avatar"  alt="LOGO">
                <style>
                    .header_title   .title{
                        font-size: .426667rem;
                        font-weight: 700;
                        color: #fff;
                        max-width: 7.52rem;
                        display: block;
                    }

                    .header_title .subtext{
                        color: #e5ecff;
                        font-weight: 500;
                        font-size: .26666rem;
                        margin-top: .15rem;
                        max-width: 7.5rem;
                        display:flex;align-items: center
                    }
                    .header_title .subtext .icon{
                        margin-right: 1px;
                    }
                </style>
                <div class="header_title">

                    <div class="title">{$shop.shop_name}</div>
                    <div class="subtext">客服QQ：{$shop.qq}</div>

                    {if plugconf('usercard', 'status')==1}
                    <div class="subtext">
                        {if plugconf('usercard', 'auth_status')==1}
                        {if $shop->auth}
                        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="12" height="12"><path d="M353.620719 131.397105l112.646916-112.650471c24.993734-24.993734 65.516331-24.993734 90.510065 0L669.424616 131.397105h157.398837c35.347494 0 63.999889 28.654172 63.999889 63.999889v157.400616l113.471803 113.470025c24.993734 24.993734 24.993734 65.516331 0 90.510065l-113.471803 113.470025v158.037059c0 35.345716-28.652395 63.999889-63.999889 63.999889h-158.035281l-112.01225 112.01225c-24.993734 24.993734-65.516331 24.993734-90.510065 0l-112.01225-112.01225h-160.317944c-35.345716 0-63.999889-28.654172-63.999889-63.999889V667.965063l-111.18914-111.189141c-24.993734-24.993734-24.993734-65.516331 0-90.510065l111.18914-111.18914v-159.679723c0-35.347494 28.654172-63.999889 63.999889-63.999889h159.681501z m97.230054 509.328894l-138.278871-138.278871c-10.414204-10.414204-27.299508-10.414204-37.711935 0-10.414204 10.414204-10.414204 27.29773 0 37.711934l157.13395 157.13395c10.414204 10.414204 27.29773 10.414204 37.711934 0l321.812775-321.810997c10.412426-10.414204 10.412426-27.29773 0-37.711935-10.414204-10.414204-27.299508-10.414204-37.713712 0L450.850773 640.725999z" fill="#ffffff" p-id="1493"></path></svg>
                        商家已认证
                        {else/}
                        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="12" height="12"><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0zM593.066667 145.066667l-21.333333 486.4c0 29.866667-25.6 51.2-55.466667 51.2l-12.8 0c-29.866667 0-51.2-21.333333-55.466667-51.2L426.666667 145.066667c0-29.866667 21.333333-51.2 46.933333-51.2l68.266667 0C571.733333 93.866667 597.333333 115.2 593.066667 145.066667zM571.733333 913.066667C554.666667 930.133333 533.333333 938.666667 512 938.666667c-25.6 0-42.666667-8.533333-59.733333-25.6C435.2 896 426.666667 878.933333 426.666667 853.333333c0-25.6 8.533333-42.666667 25.6-59.733333C469.333333 776.533333 486.4 768 512 768c25.6 0 46.933333 8.533333 64 25.6 17.066667 17.066667 25.6 34.133333 25.6 59.733333C597.333333 878.933333 588.8 900.266667 571.733333 913.066667z" fill="#f69a74" p-id="16662"></path></svg>
                        商家未认证
                        {/if}
                        <span style="margin-right:4px"></span>
                        {/if}

                        {if plugconf('usercard', 'deposit_status')==1}
                        <svg  class="icon" viewBox="0 0 1080 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="M872.732444 194.56L524.686222 64.967111a34.474667 34.474667 0 0 0-21.788444 0L163.612444 194.56a31.232 31.232 0 0 0-20.024888 29.411556v295.708444c0 137.784889 62.464 258.56 182.044444 346.851556 88.291556 65.991111 175.502222 91.306667 179.029333 92.501333 2.958222 0.568889 5.290667 1.137778 8.248889 1.137778 2.332444 0 5.290667 0 8.817778-0.568889 4.152889-1.137778 93.639111-26.510222 184.32-92.444445 121.969778-89.543111 186.709333-209.066667 186.709333-347.477333V223.971556a32.085333 32.085333 0 0 0-20.024889-29.411556z m-210.830222 139.548444l-87.779555 132.551112h72.476444c6.485333 6.485333 11.207111 13.539556 11.207111 22.357333a27.875556 27.875556 0 0 1-11.207111 22.414222H552.334222v38.286222h94.264889c6.485333 6.428444 11.207111 13.539556 11.207111 22.357334a27.875556 27.875556 0 0 1-11.207111 22.357333H552.334222v96.597333c2.389333 27.136-8.817778 43.008-33.564444 43.008-24.746667 0-38.286222-13.539556-35.896889-43.008V594.488889H387.413333a25.315556 25.315556 0 0 1-13.596444-22.357333c0-11.207111 4.721778-18.204444 13.596444-22.357334h94.208V512H389.802667c-8.817778-6.485333-13.539556-13.539556-13.539556-22.357333 0-8.874667 4.721778-18.261333 13.539556-22.414223h71.850666l-87.779555-133.12c-6.428444-15.928889-2.275556-29.411556 15.928889-40.618666 18.261333-8.817778 31.232-6.485333 43.008 11.207111l85.390222 134.826667 87.779555-134.826667c11.150222-15.928889 24.689778-20.024889 40.618667-13.539556 15.928889 6.485333 22.357333 22.926222 15.36 42.951111z" fill="#ffffff" p-id="10535"></path></svg>
                        信誉保证金: {$shop.agentdeposit_money|round=###,2}元
                        {/if}

                    </div>

                    {if plugconf('usercard', 'time_status')==1}
                    <div class="subtext">
                        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"  width="12" height="12"><path d="M829.3 195C744.5 110.5 631.9 63.9 512.2 63.9c-119.7 0-232.3 46.6-317 131.1-84.7 84.6-131.4 197-131.4 316.7 0 119.6 46.6 232.1 131.3 316.7 84.7 84.5 197.2 131.1 317 131.1 119.8 0 232.4-46.6 317.2-131.2 174.8-174.5 174.8-458.6 0-633.3zM725 673.1c-5.6 9.7-13.9 16.6-23.5 19.4-3.3 1-6.7 1.4-10.1 1.4-6.8 0-13.6-1.9-20-5.5l-155-93.7c-5.2-2.9-11.6-6.4-15-10.9-19.7-11.4-29.6-23.2-29.6-35.1V290.2c0-23 18.7-41.6 41.7-41.6 23 0 41.7 18.7 41.7 41.6V529l151 87.2c21.4 12.4 29.8 37.9 18.8 56.9z" fill="#ffffff" p-id="13985"></path></svg>
                        开店时间：{$shop.create_at|date='Y-m-d',###}
                    </div>
                    {/if}

                    {/if}
                </div>
                <!--                <div class="header_title">
                                    <span>{$shop.shop_name}</span> 
                                    <span>客服QQ：{$shop.qq}</span>
                                </div>-->
                <div class="header_right">
                    {if $shop.website!=""}
                    <a class="website" href="{$shop.website}">进入官网</a>
                    {/if}
                    <a class="queryorder" href="/orderquery">查询订单</a>
                </div>
            </div>
        </header>
        <!-- end header -->
        <form name="p" id="payform" method="post" action="/pay/order" target="_self"  autocomplete="off">

            {block name="content"}{/block}

            <section class="desc">

                <div class="sale_message" id='isdiscount_span'>
                    <svg  class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path d="M988.908308 614.006154c-58.525538-29.538462-76.091077-53.208615-76.091077-100.509539 0-44.386462 17.565538-68.017231 76.091077-100.548923 32.177231-20.716308 35.091692-38.439385 35.091692-68.01723v-168.566154C1024 123.116308 980.125538 78.769231 927.468308 78.769231H96.492308C43.874462 78.769231 0 123.116308 0 176.364308v168.566154c0 26.584615 2.914462 50.254769 35.091692 68.01723 23.433846 11.815385 76.091077 41.353846 76.091077 100.548923 0 65.024-38.045538 85.740308-73.137231 97.555693H35.052308C2.914462 631.768615 0 664.300308 0 679.069538v168.566154C0 900.883692 43.874462 945.230769 96.531692 945.230769H927.507692C980.125538 945.230769 1024 900.883692 1024 847.635692v-168.566154c0-32.531692-11.697231-44.347077-35.091692-65.063384z" fill="#7FBCFF" p-id="61311"></path><path d="M670.444308 530.116923c17.723077 0 32.571077 14.572308 32.571077 32.019692a32.571077 32.571077 0 0 1-32.571077 31.980308h-124.376616v122.171077a32.571077 32.571077 0 0 1-65.142154 0v-122.171077H356.548923a32.571077 32.571077 0 0 1-32.571077-31.980308c0-17.447385 14.808615-32.019692 32.571077-32.019692h124.376615v-75.618461H347.648A32.571077 32.571077 0 0 1 315.076923 422.478769c0-17.447385 14.808615-31.980308 32.571077-31.980307h97.713231L341.740308 288.689231a31.232 31.232 0 0 1 0-43.638154 32.610462 32.610462 0 0 1 44.425846 0l127.330461 125.085538 127.330462-125.085538a32.610462 32.610462 0 0 1 44.386461 0c11.854769 11.618462 11.854769 31.980308 0 43.638154l-103.620923 101.809231h94.759385c17.762462 0 32.571077 14.572308 32.571077 31.980307a32.571077 32.571077 0 0 1-32.571077 32.019693h-133.277538v75.618461h127.369846z" fill="#007AFF" p-id="61312"></path></svg>
                    <div class="content sale_message">
                        <span></span>
                    </div>
                </div>
                <div class="desc_content">
                    <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path d="M392.7 958.9c22.5 0 40.7-18.2 40.7-40.7V630.9c0-22.5-18.2-40.7-40.7-40.7H105.4c-22.5 0-40.7 18.2-40.7 40.7v287.4c0 22.5 18.2 40.7 40.7 40.7h287.3zM860 958.9c22.5 0 40.7-18.2 40.7-40.7V630.9c0-22.5-18.2-40.7-40.7-40.7H572.7c-22.5 0-40.7 18.2-40.7 40.7v287.4c0 22.5 18.2 40.7 40.7 40.7H860zM392.7 492c22.5 0 40.7-18.2 40.7-40.7V164c0-22.5-18.2-40.7-40.7-40.7H105.4c-22.5 0-40.7 18.2-40.7 40.7v287.4c0 22.5 18.2 40.7 40.7 40.7h287.3z" fill="#1E94EE" p-id="42427"></path><path d="M948.3 336.4c15.9-15.9 15.9-41.6 0-57.5L745.1 75.7c-15.9-15.9-41.6-15.9-57.5 0L484.4 278.9c-15.9 15.9-15.9 41.6 0 57.5l203.2 203.2c15.9 15.9 41.6 15.9 57.5 0l203.2-203.2z" fill="#B4DCF5" p-id="42428"></path></svg>
                    <div class="content"  id="remark">
                    </div>
                </div>
            </section>


            <section class="order_info">

                <div class="info_box">
                    <div class="info_left">
                        <span>*</span> 
                        <span>购买数量</span> 
                    </div> 
                    <div class="count_right">
                        <span style="cursor:pointer"><p></p></span> 
                        <input type="text" name="quantity" value="1" onkeyup="changequantity()"> 
                        <span style="cursor:pointer"><p></p> <p></p></span>
                    </div>
                </div>


                <div class="info_box">
                    <div class="info_left">
                        <span>*</span> 
                        <span>联系方式</span> 
                    </div> 
                    <div class="input_right">
                        <input name="contact" class='phone_num' type="text" placeholder="[必填]推荐填写QQ号或手机号!" required="">
                    </div>
                    <div class="info_box_msg" >
                        联系方式特别重要,可用来查询订单
                    </div>
                </div>

                <div class="info_box coupon">
                    <div class="info_left">
                        <span></span> 
                        <span>短信提醒</span> 
                    </div> 
                    <div class="input_right" style="text-align:right">
                        <input type="checkbox" name="is_rev_sms" value="1" id="is_rev_sms" data-cost="0"><label class="checkbox_label" for="is_rev_sms"></label>
                    </div>
                </div>


                <div class="info_box coupon">
                    <div class="info_left">
                        <span></span> 
                        <span>邮箱提醒</span> 
                    </div> 
                    <div class="input_right" style="text-align:right">
                        <input type="checkbox" name="isemail" value="1" id="isemail"><label class="checkbox_label" for="isemail"></label>
                    </div>

                    <div class="input_coupon email_show" style='display:none'>
                        <input type="text" name="email" placeholder="填写你常用的邮箱地址">
                    </div>
                </div>

                <div class="info_box coupon realcoupon">
                    <div class="info_left">
                        <span></span> 
                        <span>使用优惠券</span> 
                    </div> 
                    <div class="input_right" style="text-align:right">
                        <input id="youhui" type="checkbox"  name="youhui">
                        <label class="checkbox_label" for="youhui"></label>
                    </div>

                    <div class="input_coupon youhui_show" style='display:none'>
                        <input type="text" name="couponcode" placeholder="请填写你的优惠券" onchange="checkCoupon2()">
                    </div>
                </div>


                <div class="info_box"  id="pwdforsearch1" style="display:none">
                    <div class="info_left">
                        <span>*</span> 
                        <span>取卡密码</span> 
                    </div> 
                    <div class="input_right">
                        <input type="text"  name="pwdforsearch1" placeholder="[必填]请输入取卡密码（6-20位）">
                    </div>
                </div>

                <div class="info_box"  id="pwdforsearch2" style="display:none">
                    <div class="info_left">
                        <span></span> 
                        <span>取卡密码</span> 
                    </div> 
                    <div class="input_right">
                        <input type="text"  onblur="is_pwd_not_need()" name="pwdforsearch2" placeholder="[选填]请输入取卡密码（6-20位）">
                    </div>
                </div>
            </section>

            <section class="xh_box" style="display:none">
                <div style="color: #545454;margin-bottom: .2rem;font-size: .373333rem;font-weight: 700;">
                    已选号码<a style="font-size: 12px;float: right;border: 1px solid rgb(51, 105, 255);border-radius: 5px;padding: 2px 6px;color: rgb(51, 105, 255);" href='javascript:void(0)' onclick='selectForm()'>重新选择</a>
                </div>
                <div class="xh_box_content">
                    <p style='color: #74788d;padding:10px'>未指定号码，系统将随机发货！</p>
                </div>
            </section>

            <div class="border"></div>

            <section class="pay">
                <div class="pay_title">
                    支付方式
                </div>

                <div class="pay_list">
                    {foreach $userChannels as $k=>$v}
                    {if $v.status==1}
                    <div style="cursor:pointer" class="pay_box {if $k==1}active{/if}" data-pid='{$v.channel_id}' data-rate="{$v.rate}">
                        <div>
                            <img src="{:get_paytype_info($v.paytype)['ico']}" class="buy_type" /> 
                            <span>{$v.show_name}</span> 
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAwCAMAAABdTmjTAAABU1BMVEUzaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf8zaf////9hi//b5f/d5v/z9v81av84bf9Acv9Bc/9Edf9Jef9Me/9NfP9Wg/9Xg/81a/9rkv9tlP9ulf92mv99oP9/of+Gpv+Nq/+Qrf+UsP+fuP+huv+uxP+vxP+yx//B0f/C0v/J2P/M2f83bP8+cf/i6v/t8v/u8//x9P88b//5+v/6+//8/f/9/f/+/v89cf+QnoDYAAAAQHRSTlMAAQIDBgoMDxMVFhwfIiQsLS83PkJDTlFUW2Nma3Z4f4KHiJSWmZykq66xtLy9wcPEyNDS09vg4+rs8PP1+fz+e7c8kwAAATpJREFUeF6V0ldPAlEYhGGRRcQu9t7FrqgIImUO1d672Hv3/1cP0Q3Z8EGG9/q5mkxZCblKsJWzvHV4g7Q1pkBj+xhobBsBjwfA4x7wuDPK47YIaNwcBo3dK6BxfQA0rvGDxi4faOycB40dM6CxMQEa2zzg8RB43Aced4PHHVEet4ZB46YQaNwYBI3rAhaTXi2Cq5dg6TKxVRC7Fq32VKnnWAFc4bXa/S+l1LGMjWmr3U5qm4GI7eOmekS29Sdtb3ZFbBs17cHrHoD0vbbxH4h40LQ7SfV5Alxp+7IJEffC7FbpMmfafsQg4q7cedbu1H9HEHF7BLkerv/sBUTcEoal8+/sECkRu/OOdviu4m+QcMMy8oolNiDhWj+EUpBwlQ9iEnYugCmUtY45cGlrTILG5R7weBg87gffL8pDCUuj0TqxAAAAAElFTkSuQmCC" class="icon">
                        </div>
                    </div>
                    {/if}
                    {/foreach}
                </div>
            </section>

            <section class="copyright">
                <p>{:sysconf('site_info_copyright')}</p>
            </section>

            <footer class="footer">
                <div class="to_pay" id="check_pay">
                    <span><span></span> 
                        <p>手续费
                        </p>
                    </span> 
                </div>
            </footer><!--
             end footer -->

            <input type="hidden" name="is_contact_limit" value="{$goods.contact_limit|default=''}">
            <input type="hidden" name="limit_quantity" value="{$goods.limit_quantity|default=1}">
            <input type="hidden" name="userid" value="{$shop.id}">
            <input type="hidden" name="token" value="{$Request.token}">
            <input type="hidden" name="cardNoLength" value="0">
            <input type="hidden" name="cardPwdLength" value="0">
            <input type="hidden" name="is_discount" id="is_discount" value="0">
            <input type="hidden" name="coupon_ctype" value="0">
            <input type="hidden" name="coupon_value" value="0">
            <input type="hidden" name="sms_price" value="0">
            <input type="hidden" name="paymoney" value="">
            <input type="hidden" name="danjia" value="">
            <input type="hidden" name="is_pwdforsearch" id="is_pwdforsearch">
            <input type="hidden" name="is_coupon" value="">
            <input type="hidden" name="price" value="0">
            <input type="hidden" name="kucun" value="0">
            <input type="hidden" name="select_cards" value="">

            <input type="hidden" name="feePayer" value="{$shop.fee_payer|default=1}">
            <input type="hidden" name="fee_rate" value="{$userChannels[0]['rate']|default=0}">
            <input type="hidden" name="min_fee" value="{:sysconf('transaction_min_fee')}">

            <input type="hidden" name="rate" value="100">
            <input type="hidden" name="pid"  value="">
        </form>

        <script src="__RES__/app/theme/default/js/app_mobile.js?v=20210611"></script>
        <iframe  allow="autoplay" style="display:none" id="iframeAudio"></iframe>
        <script>
                        $.ajax({
                            type: 'get',
                            url: "{:url('index/resource/musicDetail',['id'=>$shop.music])}",
                            dataType: "json",
                            success: function (res) {
                                if (res.code == 1)
                                {
                                    $("#iframeAudio").attr("src", res.musicLink);
                                }
                            }
                        });
        </script>

    </body>
</html>