﻿
/*
 *  http://qietu.com
Copyright 2007~2018 Qietu, Inc.*/

/*reset*/
 html{-webkit-text-size-adjust:none; /*解决chrome浏览器下字体不能小于12px*/}
a{outline:none; text-decoration:none;}  
html{zoom:1;}html *{outline:0;zoom:1;} html button::-moz-focus-inner{border-color:transparent!important;} 
body{overflow-x: hidden; font-size:12px;} body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0;} table{/*border-collapse:collapse;border-spacing:0;*/} fieldset,a img{border:0;} address,caption,cite,code,dfn,em,th,var{font-style:normal;font-weight:normal;} li{list-style:none;} caption,th{text-align:left;} h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;} q:before,q:after{content:'';}
input[type="submit"], input[type="reset"], input[type="button"], button { -webkit-appearance: none; /*去掉苹果的默认UI来渲染按钮*/} em,i{ font-style:normal;}



/*common*/
.clearfix:after {content:"."; display:block; height:0; clear:both; visibility:hidden; }.clearfix {display:block;}.clear{ clear:both;}
.colwrapper { overflow:hidden; zoom:1 /*for ie*/; margin:5px auto; }
.strong{ font-weight: bold;} .left{ float: left;} .right{ float: right;} .center{ margin:0 auto; text-align:center;}
.show{ display:block; visibility:visible;}.hide{ display: none; visibility:hidden;}
.block{ display:block;} .inline{ display:inline;}
.break{ word-wrap:break-word;overflow:hidden; /*word-break:break-all;*/}

.tal{ text-align:left} .tar{ text-align:right;}

/*文字两侧对齐*/
.justify {
	text-align:justify;
	text-justify:distribute-all-lines;/*ie6-8*/
	text-align-last:justify;/* ie9*/
	-moz-text-align-last:justify;/*ff*/
	-webkit-text-align-last:justify;/*chrome 20+*/
}

.toe{
	/*超出省略号*/
	 word-break:keep-all;
white-space:nowrap;
overflow:hidden;
text-overflow:ellipsis;
}
@media screen and (-webkit-min-device-pixel-ratio:0){/* chrome*/
	.justify:after{
		content:".";
		display: inline-block;
		width:100%;
		overflow:hidden;
		height:0;
	}
}

  a{ color:#5d5d5e;}
  a:hover{-webkit-transition: all .3s;-moz-transition: all .3s; transition: all .3s;}
 
 

.css3{
	/*transition: all 0.3s ease-in-out 0s;*/
	
	-webkit-transform:translate3d(0, -20px, 0);
	-ms-transform:translate3d(0, -20px, 0);
	transform:translate3d(0, -20px, 0);
	
	-webkit-transition-property:opacity, -webkit-transform;
	transition-property:opacity, transform;
	-webkit-transition-duration:1000ms;
	transition-duration:1000ms;
	-webkit-transition-timing-function:cubic-bezier(0.25, 0.46, 0.33, 0.98);
	transition-timing-function:cubic-bezier(0.25, 0.46, 0.33, 0.98);
	
	-webkit-transition-delay:800ms;
	transition-delay:800ms
}
.css3.animated{
	-webkit-transform:translate3d(0, 0, 0);
	-ms-transform:translate3d(0, 0, 0);
	transform:translate3d(0, 0, 0);
}
@media screen and (max-width: 650px) {    
  
  }
  
  @media screen and (max-width: 480px) {   
	 
  }

body {
    font-family: font-family: 'PingFang SC','microsoft yahei',arial,'helvetica neue','hiragino sans gb',sans-serif;
    -webkit-text-size-adjust: 100%; /*关闭自动调整字体*/
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}