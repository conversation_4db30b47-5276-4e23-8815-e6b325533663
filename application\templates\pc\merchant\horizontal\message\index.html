{extend name="base"}

{block name="css"}

{/block}
{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-auto">
                                <select name="status" class="form-control select2">
                                    <option value="" {if $Think.get.status==''}selected{/if}>全部状态</option>
                                    <option value="1" {if $Think.get.status=='1'}selected{/if}>已读取</option>
                                    <option value="2" {if $Think.get.status=='2'}selected{/if}>未读取</option>
                                </select>
                                <button type="submit" class="btn btn-primary waves-effect waves-light"><i class="bx bx-search"></i> 搜索</button>
                            </div>
                        </div>


                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>发件人</th>
                                        <th>标题</th>
                                        <th>内容</th>
                                        <th>读取状态</th>
                                        <th>发送时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach $messages as $k=> $v}
                                    <tr>
                                        <th>{if $v.from_id==0}<span class="badge badge-danger">系统</span>{else/}{$v.fromUser.username}{/if}</th>
                                        <td>{$v.title}</td>
                                        <td>{$v.content}</td>
                                        <td>
                                            <div class="custom-control custom-switch  mb-3" dir="ltr">
                                                <input onchange="change_status(this, '{$v.id}')"  {if $v.status==1}checked{/if} type="checkbox" class="custom-control-input" id="customSwitchsizemd{$k}">
                                                       <label class="custom-control-label" for="customSwitchsizemd{$k}"></label>
                                            </div>
                                        </td>
                                        <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm " role="group" aria-label="Basic group">
                                                <button onclick="del(this, '{$v.id}')" class="btn btn-light waves-effect waves-light text-danger">删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="...">
                            {$page}
                        </nav>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}

<script>
    function change_status(obj, id)
    {
        var status = $(obj).prop('checked');
        if (status) {
            status = 1;
        } else {
            status = 0;
        }
        $.post("{:url('message/changeStatus')}", {
            id: id,
            status: status
        }, function (res) {
            if (res.code != 1) {
                $.alert(res.msg);
            }
        });
    }

    function del(obj, id)
    {

        $.confirm({
            title: '确定删除吗？',
            content: '你将无法恢复该操作！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('message/del')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert('删除失败' + res.msg);
                            } else {
                                $.alert('删除成功');
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });

    }
</script>
{/block}
