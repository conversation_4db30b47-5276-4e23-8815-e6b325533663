{extend name="base"}

{block name="css"}

{/block}
{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-auto">

                                <form class="form-inline" role="form" action="" method="get">
                                    <div class="form-group">
                                        <select name="paytype" class="form-control select2">
                                            <option value="" {if $Think.get.paytype==''}selected{/if}>全部方式</option>
                                            {foreach $pay_product as $k => $v}
                                            <option value="{$v.id|htmlentities}" {if $Think.get.paytype==$v.id}selected{/if}>{$v.name}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                    <div class="form-group ml-1">
                                        <select name="type" class="form-control select2">
                                            <option value="0" {if $Think.get.type=='0'}selected{/if}>订单编号</option>
                                            <option value="1" {if $Think.get.type=='1'}selected{/if}>商品名称</option>
                                            <option value="2" {if $Think.get.type=='2'}selected{/if}>联系方式</option>
                                            <option value="3" {if $Think.get.type=='3'}selected{/if}>代理ID</option>
                                        </select>
                                    </div>
                                    <div class="form-group ml-1">
                                        <input class="form-control" type="text" name="keywords" value="{$Think.get.keywords|htmlentities}" placeholder="请输入关键字">
                                    </div>
                                    <div class="form-group ml-1">
                                        <input class="form-control input-daterange-datepicker"  type="text" name="date_range" value="{$Think.get.date_range|default=''}" placeholder="点击选择查询日期">
                                    </div>

                                    <button type="submit" class="btn btn-light waves-effect waves-light ml-1"><i class="bx bx-search mr-1"></i>搜索</button>
                                </form>
                            </div>
                        </div>


                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>代理ID</th>
                                        <th>订单号</th>
                                        <th>商品名称</th>
                                        <th>支付方式</th>
                                        <th>总价</th>
                                        <th>实付款</th>
                                        <th>购买者信息</th>
                                        <th>状态</th>
                                        <th>提卡密码</th>
                                        <th>取卡状态</th>
                                        <th>交易时间</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    {foreach $orders as $v}

                                    <tr>
                                        <th>{$v.user_id}</th>
                                        <th><a href="{:url('order/signQuery',['id'=>$v.id])}" target="_blank">{$v.trade_no}</a></th>
                                        <td>
                                            {$v.goods_name}
                                            {eq name="v.status" value="1"}
                                            <a class="fetch" href="{:url('order/fetchcard',['id'=>$v.id])}">（{$v.quantity}张）</a>
                                            {else}
                                            （{$v.quantity}张）
                                            {/eq}
                                            {if $v.coupon_type==1}
                                            <span class="badge badge-pill badge-success font-size-12" data-toggle="tooltip" data-placement="top" title="支持优惠券" data-original-title="支持优惠券">券</span>
                                            {/if}
                                        </td>


                                        <td>{:get_paytype_name($v.paytype)}</td>
                                        <td>{$v.total_product_price}</td>
                                        <td>{$v.total_price}</td>
                                        <td>{$v.contact}</td>
                                        <td>
                                            {if $v.status==1}
                                            <span class="badge badge-pill badge-success">{$v.status_text}</span>
                                            {elseif $v.status==0/}
                                            <span class="badge badge-pill badge-danger">{$v.status_text}</span>
                                            {else/}
                                            <span class="badge badge-pill badge-light">{$v.status_text}</span>
                                            {/if}
                                        </td>
                                        <td>
                                            {$v.take_card_password}
                                        </td>
                                        <td>
                                            {if $v.cards_count>0}
                                            {if $v.cards_count>=$v.quantity}
                                            已取
                                            {else/}
                                            已取部分
                                            {/if}
                                            {else/}
                                            未取
                                            {/if}
                                        </td>
                                        <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                                    </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="...">
                            {$page}
                        </nav>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}
<script>

</script>
{/block}
