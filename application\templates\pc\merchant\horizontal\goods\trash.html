{extend name="base"}
{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-auto">

                                <form class="form-inline" role="form" action="" method="get">
                                    <div class="form-group">
                                        <select name="cate_id" class="form-control select2">
                                            <option value="" {if $Think.get.cate_id=='' }selected{/if}>全部分类 </option> 
                                            {foreach $categorys as $v}
                                            <option value="{$v.id|htmlentities}" {if $Think.get.cate_id==$v.id}selected{/if}>{$v.name} </option> 
                                            {/foreach}
                                        </select>
                                    </div>
                                    <div class="form-group ml-1">
                                        <select name="type" class="form-control select2">
                                            <option value="" {if $Think.get.type=='' }selected{/if}>全部类型 </option>
                                            <option value="1" {if $Think.get.type==1}selected{/if}>普通商品 </option>
                                            <option value="2" {if $Think.get.type==1}selected{/if}>代理商品 </option>
                                        </select> 
                                    </div>
                                    <div class="form-group ml-1">
                                        <input name="name" type="text" class="form-control" placeholder="商品名" value="{$Think.get.name|default=''|htmlentities}">
                                    </div>
                                    <button type="submit" class="btn btn-light waves-effect waves-light ml-1"><i class="bx bx-search mr-1"></i>搜索</button>
                                </form>

                            </div>

                            <div class="col-sm-auto">
                                <button onclick="batch_restore()" class="btn btn-success" ><i class="bx bx-history mr-1"></i>批量恢复</button>
                            </div>
                        </div>


                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th> 
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="select_all">
                                                <label class="custom-control-label" for="select_all"></label>
                                            </div>
                                        </th>
                                        <th>商品名称</th>
                                        <th>商品分类</th>
                                        <th>价格</th>
                                        <th>排序</th>
                                        <th>创建时间</th>
                                        <th>删除时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    {foreach $goodsList as $k=>$v}
                                    <tr>
                                        <td>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input select-checkbox" id="checkbox{$k}" name="good_id" value="{$v.id|htmlentities}">
                                                <label class="custom-control-label" for="checkbox{$k}"></label>
                                            </div>
                                        </td>
                                        <th scope="row">   
                                            {$v.name}
                                            {if $v.wholesale_discount==1}
                                            <span class="badge badge-pill badge-warning font-size-12" data-toggle="tooltip" data-placement="top" title="批发优惠" data-original-title="批发优惠">批</span>
                                            {/if}
                                            {if $v.coupon_type==1}
                                            <span class="badge badge-pill badge-success font-size-12" data-toggle="tooltip" data-placement="top" title="支持优惠券" data-original-title="支持优惠券">券</span>
                                            {/if}
                                            {if $v.take_card_type!=0}
                                            <span class="badge badge-pill badge-primary font-size-12"  data-toggle="tooltip" data-placement="top" title="提卡必填或选填取卡密码" data-original-title="提卡必填或选填取卡密码">取</span>
                                            {/if}
                                            {if $v.visit_type==1}
                                            <span class="badge badge-pill badge-info font-size-12" data-toggle="tooltip" data-placement="top" title="商品购买页面需要访问密码" data-original-title="商品购买页面需要访问密码">密</span>
                                            {/if}
                                        </th>
                                        <td>{$v.category.name|default="未分类"}</td>
                                        <td>{$v.price}</td>

                                        <td>{$v.sort}</td>
                                        <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                                        <td>{$v.delete_at|date="Y-m-d H:i:s",###}</td>

                                        <td>

                                            <div class="btn-group btn-group-sm " role="group" aria-label="Basic group">
                                                <button  onclick="restore(this, '{$v.id}')"  class="btn btn-light waves-effect waves-light text-primary">恢复</button>
                                                <a href="{:url('goods/edit',['id'=>$v.id])}" class="btn btn-light waves-effect waves-light text-primary">编辑</a>
                                            </div>

                                        </td>
                                    </tr>
                                    {/foreach}

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>
<!-- End Page-content -->

{/block}
{block name="js"}
<script>
    $('#select_all').click(function () {
        if ($(this).is(':checked')) {
            $('tbody').find('.select-checkbox').each(function () {
                $(this).prop("checked", true)
            })
        } else {
            $('tbody').find('.select-checkbox').each(function () {
                $(this).prop("checked", false)
            })
        }
    });

    function restore(obj, id)
    {
        $.confirm({
            title: '提示！',
            content: '确定恢复吗？',
            type: 'green',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-green',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('goods/restore')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert("恢复失败");
                            } else {
                                $.alert(res.msg);
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });

    }

    function batch_restore() {
        var ids = new Array();
        $('tbody').find('input[name="good_id"]').each(function () {
            if ($(this).is(':checked')) {
                ids.push($(this).val());
            }
        })
        if (ids.length == 0) {
            $.alert("选择要恢复的数据");
            return false;
        }

        $.confirm({
            title: '提示！',
            content: '确定要批量恢复吗？',
            type: 'green',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-green',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('goods/batch_restore')}", {
                            ids: ids
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("恢复成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });


    }

</script>
{/block}
