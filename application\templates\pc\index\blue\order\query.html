<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title>订单查询 - {:sysconf('site_name')}</title>
    <link rel="stylesheet" href="__RES__/theme/blue/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/style.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/response.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/order.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/captcha.css">
    <style>
    	#cardinfo0{width: 100%;}
		#cardinfo0 p{    
            width: 100%;
            display: block;
            padding-left: 20px;
            line-height: 32px;
            margin-bottom: 12px;
            position: relative;
            border-bottom: 1px solid #e2e2e2;
            padding-bottom: 12px;
        }
		#cardinfo0 p a{
            height: 32px;
            line-height: 32px;
            font-size: 16px;
            color: #3476fe;
            font-weight: bold;
            background-color: #d9dff5;
            margin-left: 30px;
            padding: 0 20px;
            margin-right: 50px;
        }
        .order_btn_vy4cripo1p36qtjp{}
        .order_btn_vy4cripo1p36qtjp .btn_vy4cripo1p36qtjp{font-size: 16px}
        .order_btn_vy4cripo1p36qtjp .btn_vy4cripo1p36qtjp:hover{background: #3476fe;color: #fff}
        .order_btn_vy4cripo1p36qtjp .btn_g8fk1qj3wb0ziz15{border: 1px solid #e43f52;color: #e43f52}
        .order_btn_vy4cripo1p36qtjp .btn_g8fk1qj3wb0ziz15:hover{background: #e43f52;color: #fff}
        .order-list {font-size: 16px;background-color: #fff;box-shadow: 0 0 5px rgba(156, 130, 130, .3);position: relative;z-index: 9;border-radius: 20px;padding: 20px;margin-top: 40px;}
		.order-user-explain .btn {padding: .05rem;font-size: 16px;}
		.order-btnbox {font-size: 16px;}
		.order-search .title .search-btn {padding: .1rem .25rem;border: .1rem solid #e1ebff;background-color: #3476fe;border-radius: 25px;color: #fff;cursor: pointer;margin-right: .6rem;}
		.order-search .title .active {opacity: 1;}
		@media (max-width: 750px) {.order-list-title {font-size: 14px;}}
		.no-pay {background-color: #999 !important}
		.no-order {text-align: center;color: #999;padding: 20px 0;background-color: #fff;}
		.order-search .title .search-btn {padding: .1rem .25rem;border: .1rem solid #e1ebff;background-color: #3476fe;border-radius: 25px;color: #fff;cursor: pointer;margin-right: .6rem;}
		.order-search .title .active {opacity: 1;}
		.faq-check {color: #3476fe;font-size: 16px;float: right;cursor: pointer;}
		.faq-check p {color: #3476fe;margin-left: .1rem;display: inline;}
		@media (max-width: 768px) {.faq-check {float: none;margin-top: .5rem;font-size: 16px;}
		.order-search .title .search-btn {margin-right: .2rem;}}
    </style>
</head>

<body>
<header class="header query-header">
    <div class="bgimg"><img src="__RES__/theme/blue/picture/header_bg.png" alt=""></div>
    <div class="container">
{include file="./default_header"}
        <div class="banner">
            <div class="text-introduce">
                <div class="h1">轻松查询订单，享受卡密自动交易</div>
            </div>
             <div class="img"><img src="__RES__/theme/blue/picture/banner_query_img.png" alt=""></div>
        </div>
    </div>
</header>
<div class="query">
    <div class="container" style="padding-bottom: .5rem;">
        <div class="order-search">
            <div class="order-search-box">
                <div class="title">
                    <span class="search-btn active">订单查询</span>
                    <span class="search-btn" onclick="window.location.href='/complaintquery'">投诉查询</span>
                    <span class="search-btn" onclick="window.location.href='/complaint'">订单投诉</span>
                    <div class="faq-check" onclick="window.location.href='/company/faq'">
                        <img src="__RES__/theme/blue/picture/faq.png" width="18" height="18" alt="">
                        <p>我已经付款，如何提取卡密？</p>
                    </div>
                </div>
                <form action="/orderquery" onsubmit="return formCheck();" class="search">
                    <input type="text" value="" placeholder="请输入订单编号/联系方式" id="orderid_input" autocomplete="off" class="search-text">
                    <input type="hidden" id="clicaptcha-submit-info" name="clicaptcha-submit-info">
                    <input type="submit" value="查询订单" onclick="orderid_or_contact()" class="btn-search">
                    
                    
                </form>
                <div class="statement">
                    <img src="__RES__/theme/blue/picture/query_light.png" alt="">
                    <span>温馨提示：</span>
                    <p>如果您对订单存在疑惑，请在购买当天23：30前向平台客服反馈，逾期请自行与卖家协商解决，平台将于24h内处理您的订单投诉。</p><br><br>
                <div class="statement">
                    <img src="__RES__/theme/blue/picture/query_light.png" alt="">
                    <span>免责声明：</span>
                    <p>平台为次日结算，款项结算给商户后所出现的售后问题请自行与卖家协商。订单投诉：通过订单号查询订单，可在【订单投诉】等待平台处理。</p>
                </div>
            </div>
        </div>
        
        
        
        
        
        
        
        {if $trade_no!==null}
                                    {if empty($order)}
                                    {if $is_verify}
                                    <div class="order-search-result">
            <div class="col-lg-12">
                <div class="no-order">
                    <p>没有查询到订单信息</p>
                </div>
            </div>
        </div>
                                    
                                    
                                    {/if}
                                    {else/}
                                  
                                    
                                    
                                    <div class="order-list">
            <div class="order-list-title">订单号：{$order.trade_no}</div>
            <div class="h" style="display:flex;justify-content: space-between;align-items: center;">
                <dl>
                    <dt>下单时间</dt>
                    <dd>{$order.create_at|date="Y-m-d H:i:s",###}</dd>
                </dl>
                <dl>
                    <dt>交易金额</dt>
                    <dd>{$order.total_price}元</dd>
                </dl>
                <dl>
                    <dt>支付金额</dt>
                    <dd>{$order.total_price}元</dd>
                </dl>
                <dl>
                    <dt>支付方式</dt>
                    <dd>{:get_paytype_name($order.paytype)}</dd>
                </dl>
                <dl style="flex-shrink: 0;margin-right: 0; padding: 0;">
                </dl>
            </div>
            <div class="order-list-item" style="margin-bottom: 0;">
            	<div id="cardinfo0">
            	    </div>
            </div>            
            <dl class="order-explain" style="margin: 30px 20px 15px;">
                <dt>商品名称</dt>
                <dd>{$order.goods.name}</dd>
            </dl>
            <dl class="order-explain tips0"><b>已发卡密：</b><span class="kamiok">1</span>张，未发卡密0张。</dl>
            <div class="row">
                <div class="col-lg-7">
                    <div class="order-user-explain">
                        <dl>
                            <dt><button class="btn">疑难问题</button></dt>
                            <dd>
                                <span style="line-height: 50px;">卡密错误，无法充值?</span>
                                <br class="d-lg-none">
                                <span class="btn-qq btn-copy" data-clipboard-text="{$order.user.qq}">卖家QQ：{$order.user.qq}</span>
                                <a href="//wpa.qq.com/msgrd?v=3&amp;uin={$order.user.qq}&amp;site=qq&amp;menu=yes" class="d-none d-lg-block y">
                                    <img border="0" src="/static/theme/fakabang/images/icon_footer1.png" alt="" title="请问有什么可以帮您" style=" vertical-align: sub;" width="25">点击咨询</a>
                            </dd>
                        </dl>
                        <dl>
                            <dt><button class="btn">注意事项</button></dt>
                            <dd>处理无果请在订单生成24小时内进行订单投诉。</dd>
                        </dl>
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="order-btnbox order_btn_vy4cripo1p36qtjp">
                        <a class="btn btn-group btn_vy4cripo1p36qtjp" href="/index/order/dumpCards?trade_no={$order.trade_no}">导出卡密</a>
                        <button class="btn btn-group btn_vy4cripo1p36qtjp" data-clipboard-action="copy" data-clipboard-target=".cardslite">一键复制</button>
                        {if condition="isset($canComplaint) && $canComplaint" }
                    	                        <a class="btn-group btn_g8fk1qj3wb0ziz15" href="/complaint?trade_no={$order.trade_no}">投诉订单</a>
                    	                        {/if}
                                            </div>
                </div>
                
                
              
                
                <div style="opacity:0;/*width:0px;height: 0px;display:none*/">
                	<textarea class="cardslite"></textarea>
                </div>
            </div>
        </div>
                                    
                                    {/if}
                                    {/if}
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        

        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
                
    </div>
</div>


{include file="./default_footer"}


</div>

<!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="__RES__/app/js/clipboard.js"></script>
        <script src="__RES__/app/js/layer.js"></script>
        <script src="__RES__/plugs/clicaptcha/cookie.min.js"></script>
        <script src="__RES__/plugs/clicaptcha/CryptoJS.js"></script>
        <script src="__RES__/plugs/clicaptcha/clicaptcha.js"></script>
        <script>
                                            var flag = true;
                                            var loading = '';
                                            var stop = false;
                                            var order_query_captcha_type = "{:sysconf('order_query_captcha_type')}";
                                            $(function () {
                                                getgoods('{$order.trade_no}', '0');
                                                /*{eq name = "order.status" value = "0"}*/
                                                layer.msg('正在获取支付状态 ...', function () {
                                                    loading = layer.load(1, {
                                                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                                                    });
                                                });
                                                setTimeout('oderquery(1)', 3000);
                                                window.setTimeout("request_stop()", 30000);
                                                /*{/eq}*/
                                            });
                                            function formCheck() {
                                                var obj = $('[name="orderid"]');
                                                if (!obj.val()) {
                                                    obj.focus();
                                                    return false;
                                                }
                                                return true;
                                            }
                                            function getgoods(orderid, id) {
                                                setTimeout(function () {
                                                    $.getJSON('/checkgoods', {
                                                        orderid: orderid,
                                                        t: new Date().getTime(),
                                                        token: '{$token}'
                                                    }, function (data) {
                                                        if (data) {
                                                            $('.cardslite').val(data.cardslite);
                                                            $('#cardinfo' + id).html(data.msg);
                                                            if (data.status == 1) {
                                                                $('.tips' + id).html('<b>已发卡密：</b><span class="kamiok">' + data.quantity + '</span>张' + '，未发卡密0张。');
                                                            }
                                                        }
                                                    });
                                                }, 1000);
                                            }
                                            function orderid_or_contact() {
                                                var input_val = $('#orderid_input').val();

                                                if (input_val === "") {
                                                    layer.msg('请输入订单号或联系方式！', {icon: 6, time: 1000});
                                                    return false;
                                                } else {
                                                    var queryType = '';
                                                    if (input_val.indexOf("@") !== -1) {
                                                            queryType = '3';
                                                    } else if (input_val.length == '16' || input_val.length == '17' || input_val.length == '18' || input_val.length == '21' || input_val.length == '19' || input_val.length == '20') {
                                                        queryType = '2';
                                                    } else {
                                                        queryType = '3';
                                                    }
                                                    var needChkcode = "{:sysconf('order_query_chkcode')}";
                                                    if (needChkcode == 1) {
                                                        chkcode(input_val, queryType);
                                                    } else {
                                                        window.location.href = '/orderquery?orderid=' + input_val + '&querytype=' + queryType;
                                                    }
                                                }
                                            }

                                            function oderquery(t) {
                                                if (flag == false)
                                                    return false;
                                                var orderid = '{$Think.get.orderid}';
                                                $.post('/pay/getOrderStatus', {
                                                    orderid: orderid,
                                                    token: '{$token}'
                                                }, function (ret) {
                                                    if (ret == 1) {
                                                        layer.close(loading);
                                                        flag = false;
                                                        stop = true;
                                                        $('#paystatus').html('付款成功');
                                                        getgoods('{$order.trade_no}', '0');
                                                    }
                                                });
                                                t = t + 1;
                                                setTimeout('oderquery(' + t + ')', 3000);
                                            }

                                            function request_stop() {
                                                if (stop == true)
                                                    return false;
                                                flag = false;
                                                layer.close(loading);
                                                layer.alert('系统未接收到付款信息，如您已付款请联系客服处理！');
                                            }
                                            function chkcode(input_val, queryType) {

                                                if (order_query_captcha_type == 0)
                                                {
                                                    layer.prompt({
                                                        title: '请输入验证码',
                                                        formType: 3
                                                    }, function (chkcode) {
                                                        $.post('/orderquery/checkverifycode', {
                                                            chkcode: chkcode,
                                                            token: '{$token}'
                                                        }, function (data) {
                                                            if (data == 'ok') {
                                                                layer.msg('验证码输入正确', {
                                                                    icon: 1
                                                                }, function () {
                                                                    window.location.href = '/orderquery?orderid=' + input_val + '&chkcode=' + chkcode + '&querytype=' + queryType;
                                                                });
                                                            } else {
                                                                layer.msg('验证码输入错误', {
                                                                    icon: 2,
                                                                    time: 3000
                                                                }, function () {
                                                                });
                                                            }

                                                        });
                                                    });
                                                    $('.layui-layer-prompt .layui-layer-content').prepend($(
                                                            '<img style="cursor:pointer;height: 60px;" id="chkcode_img" src="/chkcode" onclick="javascript:this.src=\'/chkcode\'+\'?time=\'+Math.random()">'
                                                            ))
                                                } else {
                                                    $('#clicaptcha-submit-info').clicaptcha({
                                                        src: "{:url('/chkcode',['token'=>$token])}",
                                                        checksrc: "{:url('/orderquery/checkverifycode',['token'=>$token])}",
                                                        token: "{$token}",
                                                        callback: function (chkcode) {
                                                            layer.msg('验证码输入正确', {
                                                                icon: 1
                                                            }, function () {
                                                                window.location.href = '/orderquery?orderid=' + input_val + '&chkcode=' + encodeURIComponent(chkcode) + '&querytype=' + queryType;
                                                            });
                                                        }
                                                    });
                                                }
                                            }
                                            var clipboard = new ClipboardJS('.btn');
                                            clipboard.on('success', function (e) {
                                                layer.msg('复制成功！', {
                                                    icon: 1
                                                });
                                            });
                                            clipboard.on('error', function (e) {
                                                layer.msg('复制失败，请手动复制！', {
                                                    icon: 2
                                                });
                                            });

        </script>
</body>
</html>