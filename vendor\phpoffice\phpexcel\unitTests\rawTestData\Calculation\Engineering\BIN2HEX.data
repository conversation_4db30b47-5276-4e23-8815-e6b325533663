"10110010",		"B2"
"111001010101",		"#NUM!"		//	Too large
"11111011",	4,	"00FB"		//	Leading places
"11111011",	3.75,	"0FB"		//	Leading places as a float
"11111011",	-1,	"#NUM!"		//	Leading places negative
"11111011",	"ABC",	"#VALUE!"	//	Leading places non-numeric
"1110",			"E"
"101",			"5"
"10",			"2"
"0",			"0"
"21",			"#NUM!"		//	Invalid binary number
TRUE,			"#VALUE!"	//	Non string
"1110010101",		"FFFFFFFF95"	//	2's Complement
"1111111111",		"FFFFFFFFFF"	//	2's Complement
