{extend name="lite_base" /}

{block name="content"}
<div class="card mb-0">

    <div class="card-content">
        <div class="card-body">
            <form id="form1" class="form form-vertical"  method="post">
                <div class="form-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="money">退还金额</label>
                                <input type="text" id="money" class="form-control" name="money" placeholder="请输入退还金额">
                            </div>
                        </div>
                        <div class="col-12">
                            <p class="text-muted mb-0">当前代理保证金余额{$_user.agentdeposit_money}元，退还后自动退还到账号余额</p>
                        </div>
                    </div>
                    <div class="col-12 d-flex justify-content-center mt-4">
                        <button class="btn btn-success mr-1 mb-1 btn-submit">立即退还</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>
{/block}

{block name="js"}
<!-- BEGIN: Page JS-->

<script>
    $('.btn-submit').click(function () {
        var money = $("form").find("input[name='money']").val();
        var loading = layer.load(1, {shade: [0.1, '#fff']});
        $.post("{:url('agent/userAgentDepositReturn')}", {money: money}, function (res) {
            layer.close(loading);
            if (res.code != 1) {
                $.alert(res.msg);
            } else {
                $.alert("退还成功");
                setTimeout(function () {
                    parent.location.reload();
                }, 200);
            }
        });
        return false;
    });
</script>
<!-- END: Page JS-->
{/block}
