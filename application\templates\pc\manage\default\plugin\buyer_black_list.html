{extend name='./content'}

{block name="content"}


<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">参数值</label>
        <div class="layui-input-inline">
            <input name="data" value="{$Think.get.data|default=''|htmlentities}" placeholder="请输入IP/指纹/openid" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>

<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>参数类型</th>
                <th>参数值</th>
                <th class="text-center">拦截次数</th>
                <th class="text-center">添加时间</th>
                <th class="text-center">操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $list as $v}
            <tr>
                <td>
                    {if $v.types=='ip'}
                    <font color="red">【IP地址】</font>
                    {elseif $v.types=='fingerprint'}
                    <font color="blue">【设备指纹】</font>
                    {else/}
                    <font color="green">【微信OPENID】</font>
                    {/if}
                </td>
                <td>{$v.data}</td>
                <th class="text-center">{$v.count}</th>
                <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                <td class='text-center'>
                    <a href="javascript:;" onclick="order_del(this, '{$v.id}')">删除</a>
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });

    /*订单-删除*/
    function order_del(obj, id) {
        layer.confirm('确认要删除这条记录吗？该操作不可恢复', function (index) {
            $.ajax({
                url: "{:url('buyerBlackList')}",
                type: 'post',
                data: {act: 'del', 'id': id},
                success: function (res) {
                    if (res.code == 1) {
                        $(obj).parents("tr").remove();
                        layer.msg('已删除!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('删除失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    }
</script>
{/block}
