{extend name="./layout"}

{block name="content"}

<style>
    .search .search_title{
        padding-top: .293333rem;
    }
    .search .search_title .search_title_span {
        padding-left: .186667rem;
        font-size: .31rem;
        color: #545454;
        font-weight: 700;
        position: relative;
    }
    .search .search_title .search_title_span::before {
        content: '';
        position: absolute;
        display: block;
        top: 2px;
        bottom: 2px;
        left: 0;
        width: 3px;
        background-color: #3369ff;
        border-radius: 4px;
    }

    .search .input {
        height: 32px;
        width: 100%;
        background: #fff;
        border: 1px solid #f0f0f0;
        -webkit-box-shadow: 0 4px 10px 0 rgba(135,142,154,.07);
        box-shadow: 0 4px 10px 0 rgba(135,142,154,.07);
        border-radius: 4px;
        overflow: hidden;
        margin-right: 16px;
    }
    .search .input input {
        display: inline-block;
        width: 100%;
        padding: 0 20px;
        height: 100%;
        font-weight: 500;
        border: none;
    }

    .search_button{
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 30px;
        background-color: #3369ff;
        border-radius:4px;
        background: linear-gradient(0deg,#48a0f9,#3d7aff);
        box-shadow: 0 2px 3px 0 rgba(73,105,230,.22);
        cursor: pointer;
        font-size:13px;
        margin-top:4px;
    }
    .search_button span{
        color: #ffffff;
    }
    .search-box{
        display: flex;
        flex-direction:column;
        margin-top:8px;

    }
</style>
<section class="search">
    <div class="search_title d-flex align-content-center">
        <span class='search_title_span'>商品搜索</span>
        <div class="search-box">
            <div class="input">
                <input name="keywords" class='keywords' type="text" placeholder="输入关键词搜索商品" >
            </div>
            <div class="search_button" href="/orderquery">
                <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path d="M455.253333 657.92c117.76 0 213.333333-95.573333 213.333334-213.333333s-95.573333-213.333333-213.333334-213.333334-213.333333 95.573333-213.333333 213.333334 95.573333 213.333333 213.333333 213.333333z m229.76-22.4l169.813334 169.813333c16.64 16.64 16.64 43.733333 0 60.373334-16.64 16.64-43.733333 16.64-60.373334 0l-172.8-172.8c-47.573333 32-104.746667 50.56-166.4 50.56-164.906667 0-298.666667-133.76-298.666666-298.666667s133.76-298.666667 298.666666-298.666667 298.666667 133.76 298.666667 298.666667c0 72.32-25.813333 138.88-68.906667 190.72z" fill="#ffffff"></path></svg>
                <span>商品查询</span>
            </div>
        </div>
    </div>

    <div class="category_list">


    </div>
</section>

<section class="category">
    <div class="category_title d-flex align-content-center">
        <span>选择分类</span>
    </div>
    <input name="cateid" id="cateid" type='hidden'/>
    <input name="types" id="types" type='hidden' value="shop"/>
    <div class="category_list" style="max-height: 380px;overflow-x: auto;">

        {foreach $categorys as $k=>$v}
        <div style="cursor:pointer" class="category_box {if $k==0}active{/if}" data-cateid="{$v.id}">
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAABDlBMVEX////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////uKVtWAAAAWnRSTlMAAgMEBQYHCBAREhMWHR4gJicqKywtLi8xMjM0NTY5Ojs9Pj9AQUJDREVGR0hJSktMTU5PUVJTVFVWV1laW11gYmNkZWZoaWpsbm9wcXJzdHV3eHl6e3x9fn8hmsUDAAACRElEQVR4Xq3XbVfaMBjH4ZvVYhkoYhWsFcfK+i+iU+Z0Pk11m49zE1ER8/2/yBxrThJaSNuz6/0vzemL5A7FM4pl2200gWbDtctFgxLLzy77UPjLs9OUhFXlpdpXLdIpOBjLKdAk5jwmmjdprJIHDa9E8XJzSGAuRzHeLCERZ4oiplaQUD1SG6LVWjFIkVuCVut0LayXciSrQGury64QqpCkBK2DPmPsGKGSaPMeNILv7K/Bdhh7eeIWofHxF/un1w7rRd4WoLH3xLhrhAph7Cgfud2HKvjGJDth7NCQBcmXJ9bfhGzjlgn9PXDWMK6KNDh9YYzdBRB2H5nwW1q3Sq9MH9z6DRs6V1YTfgQQfJOIZsB9emChQ7Ga0D+AYkb5Xe0ej587w3bngQndLagcIsMHh+0Br7stACfyli9aGOEbVITkmHGXaF8zYXCEqCKVIbti3FmPCfcdxCiTDdnaPYtxuYY4NrlQdAaRdPAV8VxqQHU02vY+Y4wGNTHiQm2v2xinSRjV6spbPsEEhIjOs2bL3AdqIuKQtzfrmOQ9NRB1PkxfTgNM9I5cRAV3r+3jLjRcshFjs89+bkDHpjLi7J8F0CpTEZkVyfCRkW8QOcjI4cdQFjP8AExHHIBUQyY1fuhnYfHrJgNHXHTpvRVXbGqL8uWekpeXx4qUSupAk0ol9SglODlSTNWB7COgmbiumxRhJB5cs4/Mldx/HNYF08ZEtpn5gbJcIB2rFv80qlmUxHTWR5l4Di64q57ve6vuwtjn4B94/uYnspcNGwAAAABJRU5ErkJggg==">
                <div class="cate_name">{$v.name}</div>
                <div class="cate_desc">包含{$v.count}种商品</div>
        </div>
        {/foreach}


    </div>
</section>

<section class="goods">
    <div class="goods_title d-flex align-content-center">
        <span  id="goods_title">选择商品</span>
    </div>
    <input name="goodid" id="goodid" type='hidden'/>
    <div class="goods_list" style="max-height: 380px;overflow-x: auto;">
    </div>
</section>

{/block}