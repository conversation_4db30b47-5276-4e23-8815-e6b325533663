* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
}

img {
    border: 0px;
}
ul, li {
    list-style-type: none;
}
.lottery-box {
    overflow: hidden;
}

.lottery-box p{
    margin-bottom:0px;
}


.lottery {
    animation: changeBg .5s ease infinite;
    overflow: hidden;
    padding: 20px;
    width: 400px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
@keyframes changeBg {
    0% {
        background-image:url(../image/k1.png);
    }
    100% {
        background-image:url(../image/k2.png);
    }
}
.lottery .lottery-item {
    height: 340px;
    position: relative;
    margin-top: 10px;
    margin-left: 10px;
}
.lottery .lottery-item ul li {
    width: 33.33333333%;
    position: absolute;
    padding-right: 10px;
}
.lottery .lottery-item ul li:nth-child(2) {
    left: 33.33333333%;
}
.lottery .lottery-item ul li:nth-child(3) {
    left: 66.66666666%;
}
.lottery .lottery-item ul li:nth-child(4) {
    left: 66.66666666%;
    top: 110px;
}
.lottery .lottery-item ul li:nth-child(5) {
    left: 66.66666666%;
    top: 220px;
}
.lottery .lottery-item ul li:nth-child(6) {
    left: 33.33333333%;
    top: 220px;
}
.lottery .lottery-item ul li:nth-child(7) {
    left: 0;
    top: 220px;
}
.lottery .lottery-item ul li:nth-child(8) {
    left: 0;
    top: 110px;
}
.lottery .lottery-item ul li .box {
    height: 100px;
    position: relative;
    text-align: center;
    overflow: hidden;
    background: url(../image/bg2.png) no-repeat center;
    background-size: 100% 100%;
}
.lottery .lottery-item ul li .box img {
    display: block;
    height: 50px;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 5px;
}
.lottery .lottery-item ul li .box p {
    color: #708ABF;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}
.lottery .lottery-item ul li.on .box {
    background: url(../image/bg1.png) no-repeat center;
    background-size: 100% 100%;
}
.lottery .lottery-item ul li.on .box p {
    color: #fff;
}
.lottery .lottery-item .lottery-start {
    position: absolute;
    left: 33.33333333%;
    width: 33.33333333%;
    top: 110px;
    padding-right: 10px;
}
.lottery .lottery-item .lottery-start .box {
    height: 100px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    overflow: hidden;
    background: url(../image/bg1.png) no-repeat center;
    background-size: 100% 100%;
}
.lottery .lottery-item .lottery-start .box p b {
    font-size: 40px;
    margin-top: 16px;
    margin-bottom: 15px;
    line-height: 30px;
    display: block;
}
.lottery .lottery-item .lottery-start .box:active {
    opacity: 0.7;
}
.lottery .lottery-item .lottery-start .box.gray {
    background: url(../image/bg3.png) no-repeat center;
    background-size: 100% 100%;
}
.lottery .lottery-item .lottery-start .box.gray p {
    color: #708ABF;
    font-weight: bold;
}
.mask {
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    position: fixed;
    overflow: hidden;
    z-index: 222;
    top: 0;
    left: 0;
}
.lottery-alert {
    max-width: 400px;
    text-align: center;
    z-index: 10000;
    border-radius: 10px;
    background: #fff;
    padding: 20px;
    position: fixed;
    left: 0;
    right: 0;
    margin: auto;
    top: 50%;
    transform: translateY(-50%);
}
.lottery-alert h1 {
    font-size: 18px;
    font-weight: bold;
    color: #D92B2F;
}
.lottery-alert img {
    display: block;
    height: 120px;
    margin: 0 auto;
}
.lottery-alert h2 {
    font-weight: normal;
    color: #D92B2F;
    font-size: 15px;
    padding-top: 15px;
}
.lottery-alert p {
    color: #666;
    font-size: 16px;
    padding-top: 5px;
}
.lottery-alert .btnsave {
    border-radius: 3px;
    box-shadow: none;
    height: 40px;
    cursor: pointer;
    line-height: 40px;
    color: #fff;
    margin-top: 12px;
    background: linear-gradient(180deg, rgba(213,60,63,1) 0%, rgba(201,20,24,1) 100%);
    font-size: 16px;
}
