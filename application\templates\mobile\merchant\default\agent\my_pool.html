{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->
            <div class="search-form-wrapper mb-3">
                <button onclick="$.x_show('添加商品池', '{:url(\'agent/myPoolEdit\')}', '90%')"  class="btn btn-primary w-100 d-flex align-items-center justify-content-center btn-sm">
                    <i class="bx bx-plus me-2"></i>添加商品池
                </button>
            </div>

            {foreach $list as $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">{$v.title}</h6>
                    {if $v.status==1}
                    <span class="badge rounded-pill bg-success float-end">已审核</span>
                    {else/}
                    <span class="badge rounded-pill bg-danger float-end">未审核</span>
                    {/if}
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>置顶标签：</span>
                        <span>
                            {if $v.span}<span style="background:{$v.span.color}; border-radius:10px;font-size: 10px;padding:3px 5px;white-space:nowrap;" class="text-white">{$v.span.name}</span>{else/}---{/if}
                        </span>
                    </div>
                    <div class="order-wrap-text">
                        <span>置顶到期时间：</span>
                        <span>
                            {if $v.expire_at==""}---{else/}{$v.expire_at|date="Y-m-d H:i:s",###}{/if}
                        </span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>创建时间：</span>
                        <span>
                            {$v.create_at|date="Y-m-d H:i:s",###}
                        </span>
                    </div>
                </div>

                <div class="goods-warp d-flex align-items-center mt-1 justify-content-end">
                    <a class='me-1' onclick="$.x_show('编辑商品池', '{:url(\'agent/myPoolEdit\',[\'id\'=>$v.id])}', '90%')"><span class="goods-warp-btn" >编辑</span></a>
                    <a href="javascript:void(0);" onclick="del(this, '{$v.id}')"><span class="goods-warp-btn goods-warp-btn-delete" >删除</span></a>
                </div>
            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}
<script>
    function del(obj, id)
    {

        $.confirm({
            title: '温馨提示',
            content: '确定删除吗？你将无法恢复该操作！',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('agent/myPoolDel')}", {
                            id: id
                        }, function (res) {
                            layer.closeAll();
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("删除成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }
</script>
{/block}


