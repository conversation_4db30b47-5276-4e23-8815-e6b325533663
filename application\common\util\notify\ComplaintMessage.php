<?php

namespace app\common\util\notify;

/**
 * 投诉通知
 * @package app\common\notify
 */
class ComplaintMessage {

    //商品名

    public function user_notify($user, $complaint) {
        if ($user && $complaint) {
            sendMail($user->qq . "@qq.com", '【' . sysconf('site_name') . '】买家回复了投诉订单', "投诉订单：{$complaint->trade_no}<br>商品名称：{$complaint->order->goods_name}<br>投诉内容：（{$complaint->type}）{$complaint->desc}<br><br>买家回复了投诉订单，请注意查看！", '', false);
        }
    }

    public function parent_notify($user, $complaint) {
        if ($user && $complaint) {
            sendMail($user->qq . "@qq.com", '【' . sysconf('site_name') . '】买家回复了投诉订单', "下级代理投诉单：{$complaint->trade_no}<br>商品名称：{$complaint->order->goods_name}<br>投诉内容：（{$complaint->type}）{$complaint->desc}<br><br>买家回复了投诉订单，请注意查看！", '', false);
        }
    }

    public function buyer_notify($complaint) {
        if ($complaint) {
            sendMail($complaint->qq . "@qq.com", '【' . sysconf('site_name') . '】商家回复了投诉订单', "投诉订单：{$complaint->trade_no}<br>商品名称：{$complaint->order->goods_name}<br>投诉内容：（{$complaint->type}）{$complaint->desc}<br><br>商家回复了投诉订单，请注意查看！", '', false);
        }
    }

}
