{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >


    <div class="hr-line-dashed"></div>
    <div class="form-group">
        <label class="col-sm-2 control-label">保证金功能是否开启</label>
        <div class='col-sm-8'>
            <select name="status" class="layui-input">
                <option value="0" {if plugconf('deposit','status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('deposit','status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">保证金功能是否开启</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">最低缴纳保证金金额</label>
        <div class='col-sm-8'>
            <input type="text" name="deposit_money_min"  autocomplete="off" class="layui-input"  value="{:plugconf('deposit','deposit_money_min')}">
            <p class="help-block">最低需要缴纳保证金金额</p>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>

<div class="row">
    <div class="col-sm-12 text-center">
        <div class="hr-line-dashed"></div>
        <a class="layui-btn" data-title="保证金订单" data-open="{:url('depositOrder')}" href="javascript:void(0)">保证金订单</a>
    </div>
</div>


<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });

</script>
{/block}