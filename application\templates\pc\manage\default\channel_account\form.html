<style>
.rate_type{
	border: 1px solid #C9C9C9;
    background-color: #fff;
	color: #555;
}
.layui-btn-normal {
    background-color: #1E9FFF;
	color:#fff;
}
</style>
<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="__SELF__" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">账号备注</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$account.name|default=''}" required="required" title="请输入账号名" placeholder="请输入账号名" class="layui-input">
        </div>
        
        <label class="layui-form-label">接口代码</label>
        <div class="layui-input-inline">
            <input type="text" name="code" value="{$channel.code|default=''}" readonly="readonly" class="layui-input">
        </div>
    </div>
	<div class="layui-form-item">
		<label class="layui-form-label">费率设置</label>
		<div class="layui-input-inline">
		   	<button type="button" data-value="0" class='rate_type layui-btn {if !isset($account) || 0==$account['rate_type']}layui-btn-normal{/if} layui-btn-small btn-status-open'>继承接口</button>
			<button type="button" data-value="1" class='rate_type layui-btn {if isset($account) && 1==$account['rate_type']}layui-btn-normal{/if} layui-btn-small btn-status-open'>单独设置</button>
			<input type="hidden" name="rate_type" value="{$account['rate_type']|default=''}" id="rate_type" />
		</div>
	</div>
	<div class="rate" {if !isset($account) || 0==$account['rate_type']}style="display:none;"{/if}>
	<div class="layui-form-item">
        <label class="layui-form-label">充值费率(‰)</label>
        <div class="layui-input-inline">
            <input type="text" name="lowrate" value="{$account.lowrate|default=''}" placeholder="请输入充值费率" class="layui-input">
        </div>
      
    </div>
    
	</div>
    <div class="hr-line-dashed"></div>

    {foreach $fields as $k => $v}
    <div class="layui-form-item">
        <label class="layui-form-label">{$v['name']}</label>
        <div class="layui-input-block">
            <textarea class="layui-textarea" name="params[{$k}]" cols="30" rows="1" placeholder="请输入{$v['name']}">{$v['value']|default=''}</textarea>
        </div>
    </div>
    {/foreach}

    <div class="layui-form-item">
	<label class="layui-form-label"></label>
    <div class="layui-input-block">
        <p>如果证书路径类表单，可填写绝对路径，例如： /www/wwwroot/www.xxx.com/cert/xxx.pem</p>
    </div>
</div>
    <div class="hr-line-dashed"></div>
    
   
  
    
<div class="layui-form-item">
	<label class="layui-form-label">状态</label>
    <div class="layui-input-inline">
        <select class="layui-input" name="status" style="display:inline">
            <option value="1" {if isset($account) && 1==$account['status']}selected{/if}>开启</option>
            <option value="0" {if isset($account) && 0==$account['status']}selected{/if}>关闭</option>
        </select>
    </div>
</div>


   
    <div class="layui-form-item text-center">
        <input type="hidden" name="account_id" value="{$account.id|default=""}">
        <button class="layui-btn" type='submit'>保存</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消吗？" data-close>取消</button>
    </div>

</form>

<script>
    /* layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    }); */
	//更换费率设置
    $(".rate_type").click(function(){
    	$('.rate_type').removeClass('layui-btn-normal');
    	$(this).addClass('layui-btn-normal');
    	var type = $(this).data('value');
    	
    	$("#rate_type").val(type);
    	
    	if(type == 1) {
    		$(".rate").show();
    	}
    	else{
    		$(".rate").hide();
    	}
    });
</script>
