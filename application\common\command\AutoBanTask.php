<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use service\LogService;

/**
 * 自动封禁任务命令
 * Class AutoBanTask
 * @package app\common\command
 */
class AutoBanTask extends Command
{
    /**
     * 锁文件路径
     * @var string
     */
    private $lockFileName;

    /**
     * 配置指令
     */
    protected function configure()
    {
        $this->lockFileName = LOG_PATH . 'auto_ban_task.log';
        $this->setName('autoban')
            ->setDescription('自动封禁长期未登录的用户')
            ->addOption('force', 'f', \think\console\input\Option::VALUE_NONE, '强制执行，跳过频率限制');
    }

    /**
     * 执行命令
     * @param Input $input
     * @param Output $output
     * @return int|void
     */
    protected function execute(Input $input, Output $output)
    {
        $triggerTime = time();
        $lockFileHandler = null;

        try {
            $output->writeln('开始执行自动封禁任务...');

            // 获取文件锁，防止重复执行
            $lockFileHandler = fopen($this->lockFileName, "a+");
            if (!flock($lockFileHandler, LOCK_EX | LOCK_NB)) {
                $output->writeln('自动封禁任务正在执行中，获取锁失败，自动退出');
                return;
            }

            rewind($lockFileHandler);
            $lastTriggerTime = fgets($lockFileHandler);

            // 检查是否强制执行
            $forceExecute = $input->getOption('force');

            // 检查执行频率限制（可配置的最小间隔，默认1小时）
            $minInterval = intval(sysconf('autoban_min_interval')) ?: 3600; // 默认1小时
            if (!$forceExecute && !empty($lastTriggerTime) && ($triggerTime - intval($lastTriggerTime)) < $minInterval) {
                $remainingTime = $minInterval - ($triggerTime - intval($lastTriggerTime));
                $hours = floor($remainingTime / 3600);
                $minutes = floor(($remainingTime % 3600) / 60);
                $timeStr = $hours > 0 ? "{$hours}小时{$minutes}分钟" : "{$minutes}分钟";
                $message = "距离上次执行时间不足" . round($minInterval/3600, 1) . "小时，还需等待{$timeStr}（使用 --force 参数可强制执行）";
                $output->writeln($message);
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }

            if ($forceExecute) {
                $output->writeln('使用强制执行模式，跳过频率限制检查');
                LogService::write('自动任务', '自动封禁任务：使用强制执行模式');
            }

            // 检查自动封禁是否开启
            $autoban_status = sysconf('autoban_status');
            if (empty($autoban_status)) {
                $message = '自动封禁功能未开启，任务已终止！';
                $output->writeln($message);
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }

            $autoban_time = intval(sysconf('autoban_time'));
            if ($autoban_time < 0) {
                $message = '自动封禁时间设置错误，任务已终止！';
                $output->writeln($message);
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }

            // 如果设置为0，表示不限制（不执行自动封禁）
            if ($autoban_time == 0) {
                $message = '自动封禁时间设置为0，表示不执行自动封禁，任务已终止！';
                $output->writeln($message);
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }

            LogService::write('自动任务', "自动封禁任务开始执行，封禁时间设置：{$autoban_time}天");
            
            // 获取当前系统时间
            $current_time = time();
            
            // 获取所有用户，包括注册时间
            $all_users = Db::name('user')->field('id, username, is_freeze, create_at')->select();
            $output->writeln('获取到 ' . count($all_users) . ' 个用户账号');

            // 获取白名单用户
            $whitelist = $this->getWhitelistArray();
            if (!empty($whitelist)) {
                $output->writeln('已配置 ' . count($whitelist) . ' 个白名单用户');
            }

            // 筛选有效用户
            $valid_users = [];
            foreach ($all_users as $user) {
                if ($user['is_freeze'] == 0) {
                    $valid_users[] = $user['id'];
                }
            }

            $output->writeln('其中有效用户（未冻结）：' . count($valid_users) . ' 个');

            if (empty($valid_users)) {
                $message = '系统中没有有效用户，无需执行封禁操作！';
                $output->writeln($message);
                // 修复：为命令行执行时设置session
                $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }

            // 查询所有用户的登录记录
            $lastLoginQuery = Db::name('user_login_log')
                ->field('user_id, MAX(create_at) as last_login')
                ->group('user_id')
                ->select();

            $output->writeln('获取到 ' . count($lastLoginQuery) . ' 条用户登录记录');

            // 将登录记录转换为以user_id为键的数组，提高查询效率
            $login_records = [];
            foreach ($lastLoginQuery as $record) {
                $login_records[$record['user_id']] = $record['last_login'];
            }

            // 找出需要封禁的用户
            $users_to_ban = [];
            $skipped_whitelist = 0;
            $never_logged_in = 0;

            foreach ($all_users as $user) {
                // 跳过已冻结的用户
                if ($user['is_freeze'] != 0) {
                    continue;
                }

                // 检查用户是否在白名单中
                if (in_array($user['username'], $whitelist)) {
                    $skipped_whitelist++;
                    $output->writeln(" - 白名单用户: {$user['username']}，跳过检查");
                    continue;
                }

                // 获取用户最后活动时间（登录时间或注册时间）
                $last_activity_time = null;
                $activity_type = '';

                if (isset($login_records[$user['id']])) {
                    // 有登录记录，使用最后登录时间
                    $last_activity_time = $login_records[$user['id']];
                    $activity_type = '最后登录';
                } else {
                    // 没有登录记录，使用注册时间
                    $last_activity_time = $user['create_at'];
                    $activity_type = '注册时间';
                    $never_logged_in++;
                }

                // 计算距离最后活动的天数
                $days_since_activity = round(($current_time - $last_activity_time) / 86400, 2);

                if ($days_since_activity > $autoban_time) {
                    $users_to_ban[] = [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'days' => $days_since_activity,
                        'activity_type' => $activity_type
                    ];
                }
            }

            if ($never_logged_in > 0) {
                $output->writeln('发现 ' . $never_logged_in . ' 个从未登录的用户（基于注册时间判断）');
            }
                
            if (empty($users_to_ban)) {
                $whitelist_msg = $skipped_whitelist > 0 ? "（跳过了{$skipped_whitelist}个白名单用户）" : "";
                $never_msg = $never_logged_in > 0 ? "，其中{$never_logged_in}个从未登录" : "";
                $message = "没有符合条件的用户需要封禁！{$whitelist_msg}{$never_msg}";
                $output->writeln($message);
                // 修复：为命令行执行时设置session
                $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
                LogService::write('自动任务', "自动封禁任务：{$message}");
                return;
            }
            
            $output->writeln('发现 ' . count($users_to_ban) . ' 个用户需要被封禁：');
            foreach ($users_to_ban as $user) {
                $output->writeln(" - ID: {$user['id']}, 用户名: {$user['username']}, 未活动天数: {$user['days']} ({$user['activity_type']})");
            }
            
            // 提取用户ID
            $ban_ids = array_column($users_to_ban, 'id');

            // 修复：为命令行执行时设置session
            $_SERVER['REMOTE_ADDR'] = '127.0.0.1';

            try {
                // 批量更新用户状态为冻结
                $update = Db::name('user')->where('id', 'in', $ban_ids)->update(['is_freeze' => 1]);

                if ($update !== false) {
                    $count = count($users_to_ban);
                    $whitelist_msg = $skipped_whitelist > 0 ? "（跳过了{$skipped_whitelist}个白名单用户）" : "";
                    $never_msg = $never_logged_in > 0 ? "，其中{$never_logged_in}个从未登录" : "";
                    $message = "自动封禁成功，共封禁了{$count}个用户！{$whitelist_msg}{$never_msg}";
                    $output->writeln($message);
                    LogService::write('自动任务', $message);

                    // 记录详细的封禁用户信息
                    $ban_details = [];
                    foreach ($users_to_ban as $user) {
                        $ban_details[] = "ID:{$user['id']},用户名:{$user['username']},未活动天数:{$user['days']}({$user['activity_type']})";
                    }
                    LogService::write('自动任务', "封禁用户详情：" . implode('; ', $ban_details));
                } else {
                    $message = '自动封禁执行失败！数据库更新返回false';
                    $output->writeln($message);
                    LogService::write('自动任务', $message);
                }
            } catch (\Exception $dbException) {
                $message = '自动封禁数据库操作失败：' . $dbException->getMessage();
                $output->writeln($message);
                LogService::write('自动任务', $message);
                throw $dbException; // 重新抛出异常，让外层catch处理
            }

            // 更新锁文件时间戳
            if ($lockFileHandler) {
                ftruncate($lockFileHandler, 0);
                fwrite($lockFileHandler, $triggerTime);
                fflush($lockFileHandler);
            }

        } catch (\Exception $e) {
            $output->writeln('执行出错：' . $e->getMessage());
            // 修复：为命令行执行时设置session
            $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
            LogService::write('自动任务', "自动封禁出错：" . $e->getMessage());
        } finally {
            // 释放文件锁
            if ($lockFileHandler) {
                flock($lockFileHandler, LOCK_UN);
                fclose($lockFileHandler);
            }

            $timeUsed = time() - $triggerTime;
            $output->writeln("自动封禁任务执行完成，用时：{$timeUsed}秒");
        }
    }
    
    /**
     * 获取白名单用户数组
     * @return array 白名单用户名数组
     */
    protected function getWhitelistArray()
    {
        $whitelist_str = sysconf('autoban_whitelist') ?: '';
        if (empty($whitelist_str)) {
            return [];
        }
        
        // 分割白名单字符串为数组，支持多种分隔符(逗号、分号、换行符)
        $whitelist = preg_split('/[,;\r\n]+/', $whitelist_str);
        // 移除空白元素和去除每项的首尾空格
        $whitelist = array_filter(array_map('trim', $whitelist));
        
        return $whitelist;
    }
} 