{extend name="base"}

{block name="css"}

{/block}
{block name="content"}
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-12">
                                <div class="alert alert-primary alert-dismissable">
                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                    只保留显示最近30天的流水日志
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>变动金额</th>
                                        <th>变动后账户余额</th>
                                        <th>变动后冻结余额</th>
                                        <th>描述</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach $logs as $v}
                                    <tr>
                                        <th>{$v.create_at|date="Y-m-d H:i:s",###}</th>
                                        <td>  
                                            {if $v.money>0}
                                            <span class="text-success">{$v.money}</span>
                                            {else/}
                                            <span class="text-danger">{$v.money}</span>
                                            {/if}
                                        </td>
                                        <td>{$v.balance}</td>
                                        <td>{$v.freeze_money}</td>
                                        <td>{$v.reason}</td>

                                    </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="...">
                            {$page}
                        </nav>
                    </div>

                </div>
            </div>
        </div>

    </div>
    <!-- container-fluid -->
</div>

{/block}
{block name="js"}

{/block}
