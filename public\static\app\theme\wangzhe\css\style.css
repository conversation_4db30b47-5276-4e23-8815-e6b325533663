html,body{
	font: 14px Microsoft YaHei,sans-serif,arial,tahoma;
}
input[type="text"],
input[type="submit"],
input[type="button"]{
	font: 14px Microsoft YaHei,sans-serif,arial,tahoma;
}

a:hover{
	text-decoration: none;
}
.wrapper{
	width: 1200px; margin: 0 auto;
}
.header{
	height: 80px;
	 background:rgba(0,0,0,0.5); filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#7f000000,endColorstr=#7f000000); 
	position: relative;  max-width: 1920px; margin: 0 auto;
	z-index: 2;
}

.header-logo{
	float: left; display: inline; margin-top: 15px;
}
.header-logo img{
    width:180px;
    max-width:180px;
    height:auto;
}
.header-nav{
	float: right; display: inline; font-size: 22px; line-height: 80px;
}
.header-nav li{
	display: inline-block;  float: left; *width: 110px;
}
.header-nav-a{
	color: #fff;display: block;padding: 0 20px; *padding: 0; text-align: center;
}
.header-nav-a.on,
.header-nav-a:hover{
	color: #e7383f;
	background: url(../imgs/bg1.png) repeat-x;
}
/*banner*/
.banner{
	background: url(../imgs/bg1.jpg) no-repeat bottom center; margin-top: -80px;height: 635px;
}
.banner .wrapper{
	position: relative; height: 635px;
}
.banner-main{
	 background: rgba(0,0,0,0.8);position: absolute; left: 0; right: 0; bottom: 0; height: 265px;color: #d7d6d6; font-size: 18px;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#c8000000,endColorstr=#c8000000);
}
.banner-main-l{
	position: absolute; left: -90px; bottom: -2px; width: 507px; height: 359px;
}

.banner-main-c{
	padding-left: 425px; width: 500px; padding-top: 35px; float: left; display: inline;
}
.banner-tit{
	font-size: 33px;color: #fff; position: relative; padding-bottom: 13px; margin-bottom: 20px;
}
.banner-tit:after{
	position: absolute; left: 0; bottom: 0; height: 3px;width: 125px; background: #fb6468; content: "";
}
.banner-desc{
	line-height: 1.7;
}
.banner-main-r{
	float: left; display: inline; width: 190px; height: 185px; margin: 20px 0 0 45px; text-align: center; border: 1px solid #fff; padding: 20px 0;
}
.banner-main-r h4{
	font-size: 18px; padding-top: 8px;
}

/*choose*/
.choose{
	background: url(../imgs/bg2.jpg) no-repeat center -1px; height: 953px; font-size: 24px; color: #2e3346;max-width: 1920px; margin: 0 auto;
}
.choose .wrapper{
	position: relative; height: 100%;
}
.choose-wrap{
	 width: 605px; padding-top: 100px;
}
.choose-title{}
.choose-tip{
	margin: 30px 0; width: 553px;height: 100%; line-height: 33px; border: 1px solid #fb6468; padding: 10px 25px;color: #fb6468;
}
.choose-form{
	min-height: 405px;
}
.choose-item{
	padding: 13px 0; min-height: 50px;
}
.choose-left{
	float: left; display: inline; width: 120px; line-height: 50px;
}
.choose-rigt{
	float: left; display: inline; width: 485px;position: relative;
}
.choose-item-txt{
	line-height: 50px;
}
.choose-item input[type="text"]{
	width: 453px; border: 1px solid #919396; height: 48px; background: none;font-size: 18px; color: #2e3346; line-height: 48px; padding: 0 15px;
}
.choose-item input[type="text"]:focus{
	border-color: #fb6468;
}
.choose-item-t{
	height: 48px;line-height: 48px; padding: 0 15px; border: 1px solid #919396; display: inline-block; font-size: 18px;color: #a5a5a5; margin-right: 5px; cursor: pointer;
}
.choose-item-msg{
	position: absolute; right: 0; top: 0; line-height: 50px; font-size: 18px; display: inline-block; padding: 0 25px;
}
.choose-item-t:hover,
.choose-item-t.on{
	border-color: #fb6468;color: #fb6468;
}
.choose-pay{
	margin-top: 10px; font-size: 36px;
}
.f-fb6468{
	color: #fb6468;
}
.choose-info-wrap{
	position: absolute; background: rgba(255,255,255,0.6); box-shadow: 0px -2px 5px #D7D6D6;right: 10px; bottom: -180px; padding: 20px;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#99ffffff,endColorstr=#99ffffff);
}

.choose-info{
	  background: rgba(255,255,255,0.85); border: 2px solid #dc5c62; height: 440px; width: 400px;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#c8ffffff,endColorstr=#c8ffffff);
}
.choose-info h3{
	text-align: center; font-size: 36px; color: #fb6468; padding-top: 30px;
}
.choose-info-desc{
	padding: 30px 60px 0; line-height: 2.3;
}


/*paytype*/
.paytype{
	background: url(../imgs/bg3.jpg) no-repeat center #f0f0f0; height: 1080px;max-width: 1920px; margin: 0 auto;
}

.paytype-head{
	text-align: right;padding: 230px 0 0;
}
.paytype-wrap{
	padding: 50px; background: rgba(0,0,0,0.8); height: auto; overflow: hidden;margin-top: 30px;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#c8000000,endColorstr=#c8000000);
}
.paytype-tab{
	height: 70px; line-height: 70px;background: #333333; font-size: 26px; text-align: center;color: #fff; 
}
.paytype-tab-t{
	float: left; display: inline;width: 250px; cursor: pointer;
}
.paytype-tab-t.on{
	background: #fb6468;
}
.paytype-tab-qr{
	float: left; display: inline; 
}
.ico-qr{
	background: url(../imgs/ico1.png) no-repeat; width: 33px; height: 33px; display: inline-block; margin-right: 14px;vertical-align: middle;
}
.ico-bk{
	background: url(../imgs/ico2.png) no-repeat; width: 44px; height: 28px; display: inline-block; margin-right: 14px;vertical-align: middle;
}
.paytype-body{
	padding: 35px 0 20px; height: auto; overflow: hidden;  margin-right: -32px; 

}
.paytype-item{
	width: 248px; border: 1px solid #5e5055;margin-right: 32px; float: left;  margin-bottom: 28px; cursor: pointer;position: relative; vertical-align: middle;  text-align: center;line-height: 88px;
}

.paytype-item img{
	padding:20px 0; margin:0 auto;text-align:center;display: block;
}
.paytype-item:hover,
.paytype-item.on{
	border-color: #fb6468; background: url(../imgs/ico3.png) no-repeat right top;
}
.paytype-item:hover:after,
.paytype-item.on:after{
	display: block;
}
.paytype-foot{
	text-align: center; 
}
.paytype-foot input{
	width: 400px; height: 80px; border: 0; background: #fb6468; color: #fff; font-size: 24px; border-radius: 5px;cursor: pointer;
}
.paytype-foot input:hover{
	background: #d74047;
}


.footer{
	background: #313131;color: #fff; font-size: 14px; text-align: center; padding: 30px 0 ; height: auto; overflow: hidden;max-width: 1920px; margin: 0 auto;
}
