<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'think\\mongo\\' => array($vendorDir . '/topthink/think-mongo/src'),
    'think\\helper\\' => array($vendorDir . '/topthink/think-helper/src'),
    'think\\composer\\' => array($vendorDir . '/topthink/think-installer/src'),
    'think\\captcha\\' => array($vendorDir . '/topthink/think-captcha/src'),
    'think\\' => array($vendorDir . '/5ini99/think-addons/src', $vendorDir . '/topthink/think-queue/src'),
    'Wechat\\' => array($vendorDir . '/zoujingli/wechat-php-sdk/Wechat'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\OptionsResolver\\' => array($vendorDir . '/symfony/options-resolver'),
    'Qiniu\\' => array($vendorDir . '/qiniu/php-sdk/src/Qiniu'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Pdp\\' => array($vendorDir . '/jeremykendall/php-domain-parser/src'),
    'OSS\\' => array($vendorDir . '/aliyuncs/oss-sdk-php/src/OSS'),
    'Libern\\QRCodeReader\\' => array($vendorDir . '/libern/qr-code-reader/src'),
    'Endroid\\QrCode\\' => array($vendorDir . '/endroid/qrcode/src'),
);
