{"name": "symfony/options-resolver", "type": "library", "description": "Symfony OptionsResolver Component", "keywords": ["options", "config", "configuration"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": "^5.5.9|>=7.0.8"}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}}