{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-open='{:url("add")}' data-title="添加文章" class='layui-btn layui-btn-small'><i
            class='fa fa-plus'></i> 添加文章
    </button>
</div>
{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">文章名</label>
        <div class="layui-input-inline">
            <input name="title" value="{$Think.get.title|default=''}" placeholder="请输入文章名" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select name="status">
                <option value="">全部状态</option>
                <option value="1" {if $Think.get.status === '1'}selected{/if}>可见</option>
                <option value="0" {if $Think.get.status === '0'}selected{/if}>不可见</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">时间范围</label>
        <div class="layui-input-inline">
            <input name="date_range" id="date_range" value="{:urldecode($Think.get.date_range)}" placeholder="请选择时间范围" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<!-- 表单搜索 结束 -->
<div class="layui-form-item layui-inline">
    <label class="layui-form-label">文章数</label>
    <div class="layui-input-inline">
        <input type="text" class="layui-input" value="{$counts|default="0"} 篇" readonly>
    </div>
</div>

<div class="layui-form-item layui-inline">
    <a class="layui-btn layui-btn-small" data-title="一键发布结算公告" data-modal="{:url('manage/plugin/publishSettlement')}" href="javascript:void(0)">一键发布结算公告</a>
</div>
<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
            <tr>
                <th>ID</th>
                <th>分类</th>
                <th>文章标题</th>
                <th>链接</th>
                <th>浏览量</th>
                <th>状态</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $articles as $v}
            <tr>
                <td>{$v.id}</td>
                <td>{if $v.cate_id==0}未分类{else/}{$v.category.name}{/if}</td>
                <td>{$v.title}</td>
                <td>{if $v.category.alias == 'single'}{:url('index/index/content',['id'=>$v.id])}{/if}</td>
                <td>{$v.views}</td>
                <td>
                    {if $v.status==1}
                    <a style="color:green" data-tips="确定不可用吗？ " data-update="{$v.id}" data-field='status' data-value='0' data-action='{:url("change_status")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-ok"></i></a>
                    {else/}
                    <a style="color:red" data-tips="确定可用吗？" data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("change_status")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-remove"></i></a>
                    {/if}
                </td>
                <td>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                <td>
                    <a data-title="置顶" href="javascript:stick(this,'{$v.id}')" href="">置顶</a>
                    <a data-title="编辑" data-modal='{:url("edit")}?article_id={$v.id}' href="javascript:void(0)">编辑</a>
                    {if $v['is_system'] == 0}<a data-title="删除"  href="javascript:void(0)" onclick="del(this, '{$v.id}')">删除</a>{/if}
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    $(function () {
        layui.use('form', function () {
            console.log('render')
            var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
            form.render();
        });
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //日期范围
            laydate.render({
                elem: '#date_range',
                range: true
            });
        });
    })

    function del(obj, id) {
        layer.confirm('确认要删除这篇文章吗？', function (index) {
            $.ajax({
                url: "/manage/article/del",
                type: 'post',
                data: 'id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        $(obj).parents("tr").remove();
                        layer.msg('已删除!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('删除失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    }

    function stick(obj, id) {
        layer.confirm('确认要置顶这篇文章吗？', function (index) {
            $.ajax({
                url: "/manage/article/top",
                type: 'post',
                data: 'id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        layer.msg('已置顶!', {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg('置顶失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    }
</script>
{/block}
