{extend name='./main'}

{block name="body"}
<div class="news-container" id='news_box'>
    {foreach $list as $key=>$vo}
    <div class="news-box item transition" data-news-id='{$vo.id}'>
        {foreach $vo.articles as $key=>$value}
        <div class="news-item transition" data-id="{$value.id}">
            <div class="news-image">
                <img alt="image" class="img-responsive" src="{$value.local_url}"/>
            </div>
            <span class="news-title"> {$value.title}</span>
        </div>
        {/foreach}
    </div>
    {/foreach}
</div>

{if isset($page)}<p>{$page}</p>{/if}

{/block}

{block name='style'}
<style>
    body { min-width: 500px }
    .news-container { margin: 20px 0; width: 100%; position: relative;padding:0 20px}
    .news-container .news-box { border: 1px solid #eee; padding: 8px; width: 250px; border-radius: 5px; position: absolute; margin-bottom: 20px; cursor: pointer }
    .news-container .news-box:hover, .news-container .news-box.active { box-shadow: 1px 0px 10px #0099CC; border-color: #0099CC }
    .news-container .news-box hr { margin: 4px }
    .news-container .news-box .table-hover { margin-bottom: 0; margin-top: 10px; border-top: none }
    .news-container .news-box .news-item { position: relative; border-radius: 2px; overflow: hidden; }
    .news-container .news-box .news-image { text-align: center }
    .news-container .news-box .news-image img { height: 159px; width: 100%; border-radius: 2px }
    .news-container .news-box .news-btn a { padding: 15px 5px; color: #666 }
    .news-container .news-box .news-btn .fa, .news-container .news-box .news-btn .glyphicon { font-size: 1.2em; }
    .news-container .news-box .news-btn { display: block; text-align: center; font-size: 1em; color: #cecece; padding: 3px; position: relative; cursor: pointer }
    .news-container .news-box .news-title { position: absolute; background: rgba(0, 0, 0, 0.5); color: #fff; padding: 5px; margin: 0; bottom: 0; left: 0; right: 0; text-align: right; white-space: nowrap; text-overflow: ellipsis; overflow: hidden }
</style>
{/block}

{block name="script"}
<script>
    require(['jquery.masonry'], function (Masonry) {
        var container = document.querySelector('#news_box');
        var msnry = new Masonry(container, {itemSelector: '.news-box', columnWidth: 0});
        msnry.layout();
        /* 事件处理 */
        $('.news-container').on('mouseenter', '.news-box', function () {
            $(this).addClass('active');
        }).on('mouseleave', '.news-box', function () {
            $(this).removeClass('active');
        });
        var seletor = '[name="{$Think.get.field|decode|default=0}"]';
        if (seletor) {
            $('[data-news-id]').on('click', function () {
                window.top.$(seletor).val($(this).attr('data-news-id')).trigger('change');
                parent.layer.close(parent.layer.getFrameIndex(window.name))
            });
        }
    });
</script>
{/block}