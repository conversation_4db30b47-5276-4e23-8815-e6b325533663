﻿/*
 | ------------------------------------------
 | Glide styles
 | ------------------------------------------
 | Here are slider styles
 | 
*/

.slider {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
	
	.slides {
		height: 100%;
		
		/* Simple clear fix */
		overflow: hidden;
		
		/**	
		 * Prevent blinking issue
		 * Not tested. Experimental.
		 */
		-webkit-backface-visibility: hidden;
		-webkit-transform-style: preserve-3d;

		/**
		 * Here is CSS transitions 
		 * responsible for slider animation in modern broswers
		 */
		-webkit-transition: all 500ms cubic-bezier(0.165, 0.840, 0.440, 1.000); 
		   -moz-transition: all 500ms cubic-bezier(0.165, 0.840, 0.440, 1.000); 
		    -ms-transition: all 500ms cubic-bezier(0.165, 0.840, 0.440, 1.000); 
		     -o-transition: all 500ms cubic-bezier(0.165, 0.840, 0.440, 1.000); 
		        transition: all 500ms cubic-bezier(0.165, 0.840, 0.440, 1.000);
	}
	
		.slide {
			height: 100%;
			float: left;
			clear: none;
		}
		.slide  img{
			display:block;
		}


	.slider-arrows {}

		.slider-arrow {
			position: absolute;
			display: block;
			margin-bottom: -20px;			
			width: 61px;
			height: 86px;
			text-decoration: none;
			text-align: center;
			color: #fff;
			font-size: 2em;
			background-color: #333;
			background-color: rgba(50,50,50,.3);
			background:url(../images/img4.png) no-repeat; 
		}

			.slider-arrow--right { bottom: 50%; right: 0; width:58px; }
			.slider-arrow--left { bottom: 50%; left: 0; background:url(../images/img3.png) no-repeat;}


	.slider-nav {
		
		bottom: 25px;
		left: 50%;
		margin-left: -550px !important;
		position: absolute;
		width: 1110px !important;
		z-index:9;
		text-align:center;
	}

		.slider-nav__item {
			width:18px; height:17px; cursor:pointer; display:inline-block; margin:0 5px; background:url(../images/img1.png) no-repeat;
		}

				.slider-nav__item:hover { /*background: #ccc;*/ }
				.slider-nav__item--current { 
				
					background:url(../images/img2.png) no-repeat;
				}