*{
	line-height:1;
}
html,body{
	width:100%;
	height:100%;
}
.header{
	position: relative;
	overflow:hidden;
}
.header::after {
	content: '';
	width: 140%;
	height: 100%;
	position: absolute;
	left: -20%;
	top: 0;
	border-radius: 0 0 50% 50%;
	background-color: #5c75fe;
	background-image: -webkit-linear-gradient(#3478fd,#33a9fe);
	background-image: -ms-linear-gradient(#3478fd,#33a9fe);
	background-image: -o-linear-gradient(#3478fd,#33a9fe);
	background-image: linear-gradient( #3478fd,#33a9fe);
	z-index:-1;
}

.logo-login{
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	justify-content:center;
	align-items:center;
	color:#fff;
	padding:40px 0px;
}
.logo-login img{
	height:40px;
}
.logo-login .p{
	margin-left: 10px;
	border-left:2px solid #d3d3d3;
	line-height:30px;
	padding-left: 10px;
	font-weight: bold;
}
.login-box{
	background-color: #fff;
}

.login-form{
	max-width:600px;
	margin:0 auto;
	padding:0 50px;
	padding-bottom: 100px;
	background-color: #fff;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;

}
.login-form h1{
	font-size: 18px;
	color: #4d4d4d;
	padding-top: 30px;
	padding-bottom: 30px;
	line-height:1;
	margin-bottom: 0;
	letter-spacing: 3px;
	text-align: center;
}
.other-login{}
.other-login .t{
	display:flex;
	justify-content:center;
	align-items:center;
	font-size:12px;
	text-align: center;
	color: #266cfb;
	height:20px;
	line-height:20px;
	position: relative;
}
.other-login .t span{
	display:block;
	width:180px;
	height:100%;
	position:absolute;
	left:50%;
	margin-left: -90px;
	top:0;
	z-index:2;
	background-color: #fff;
}
.other-login .t:before{
	content:"";
	display:block;
	width:80%;
	height:1px;
	background-color: #e5e5e5;
	margin:0 auto;
	position: relative;
	z-index:1;
}
.other-login .bd{
	display:flex;
	justify-content:space-between;
}
.other-login .bd dl{
	text-align: center;
	color: #999999;
}
.other-login .bd img{
	width:54px;
	margin-top: 30px;
}
.other-login .bd dt{
	font-size: 14px;
	color: #999999;
	padding-bottom: 30px;
}

.login-form .item{
	margin-bottom: 20px;
	position: relative;
}
.login-form .form-control{
	height:40px;
	line-height:40px;
	padding:0 35px;
	border: none;
	background-color: #f5f9ff;
	box-shadow:none;
	font-size:14px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}
.login-form .form-control:focus{
	border-color: #fe7264;
}
.login-form .form-control::-webkit-input-placeholder{
	color: #b5b5b5;
}
.login-form .p{
	font-size: 12px;
	line-height: 1;
	text-align: right;
	margin-top: 80px;
	color: #999;
}
.login-form .p a{
	color: #999;
}
.login-form .p span a{
	color: #256cfb;
}
.login-form .p a:hover{
	color: #5375e0;
}
.btn-login{
	background-color: #3476fe !important;
	border:none !important;
	font-size: 18px;
	color: #fff !important;
	letter-spacing:10px;
}
.login-form .code{
	width:120px;
	height:36px;
	line-height:36px;
	text-align:center;
	font-size:30px;
	color: #0e8f52;
	position:absolute;
	top:7px;
	right:7px;
	background-color: #f3fbfe;
}
.login-form .msg{
	display: block;
	height:40px;
	line-height:40px;
	cursor:pointer;
	padding:0 10px;
	position:absolute;
	top:0;
	right:0;
	font-size:12px;
	color: #5375e0;
	font-weight: bold;
}

.login-form .item .icon{
	display:flex;
	justify-content:center;
	align-items:center;
	width:35px;
	height:100%;
	position: absolute;
	left:0;
	top:0;
	z-index:2;
}
.login-form .item .icon img{
	height:25px;
}
.login-form .item .icon-close{
	width:22px;
	height:22px;
	position:absolute;
	right:20px;
	top:50%;
	margin-top: -11px;
}
.login-form .item-yzm{
	display:flex;
	align-items:center;
	margin-bottom: 40px;
}
.login-form .item .input-text{
	flex:1;
	padding:0 20px;
}
.login-form .item-yzm .yzm-img{
	width:100px;
	margin-left: 20px;
	text-align: center;
	padding:0;
	position: relative;
}
.login-form .item-yzm .yzm-img img{
	max-width:100%;
	max-height:100%;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}
.login-form .item-yzm .yzm-img .refresh{
	width:100%;
	font-size: 14px;
	color:#999999;
	padding:15px 0;
	text-align: center;
	position: absolute;
	left:0;
	top:100%;
	z-index:2;
}
.login-form .btn-send{
	height:24px;
	line-height:24px;
	position: absolute;
	right:0;
	top:8px;
	font-size: 16px;
	color:#3476fe;
	border-left:1px solid #3476fe;
	padding:0 20px;
	cursor:pointer;
}

.login-form .end-item{
	display:flex;
	display:-ms-flexbox;
	justify-content:space-between;
	align-items:center;
	color: #999;
	font-size: 12px;
}
.login-form .end-item a{
	color: #999;
}
.login-form .end-item .r a{
	color: #3476fe;
}
.login-form .end-item .r a:hover{
	text-decoration:none;
}
.merchant .login-form{
	padding:0;
}
.otherlogin{
	position:relative;
	height:30px;
	margin-top: 40px;
}
.otherlogin .h{
	display:flex;
	justify-content:center;
	align-items:center;
	height:30px;
}
.otherlogin .h:before{
	content:"";
	display: block;
	width:80%;
	height:1px;
	background-color: #e5e5e5;
}
.otherlogin .h span{
	display: block;
	padding:0 15px;
	width:140px;
	height:100%;
	background-color: #fff;
	position: absolute;
	left:50%;
	margin-left: -70px;
	top:0;
	color: #999;
	font-size: 10px;
	line-height:30px;
}
.otherlogin .icon{
	position: relative;
	text-align: center;
	padding-top: 20px;
}
.otherlogin .bd{
	position: relative;
}
.otherlogin .bd dl{
	padding:0 15px 15px;
	background-color: #fff;
	box-shadow:0 0 10px #eee;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	width:160px;
	position:absolute;
	z-index:99;
	left:50%;
	margin-left: -80px;
	bottom:56px;
	display:none;
}
.otherlogin .bd:hover dl{
	display: block;
}
.otherlogin .bd dl:before{
	content:"";
	display:block;
	border:10px solid transparent;
	border-top:10px solid #fff;
	position: absolute;
	left:50%;
	margin-left: -10px;
	bottom:-20px;
}
.otherlogin .bd dt{
	padding:10px 0;
	text-align:center;
	font-size: 12px;
}
.otherlogin .bd dt img{
	margin-right: 10px;
}
.otherlogin .bd dd{
	padding:0;
	margin:0;
}
.otherlogin .bd dd img{
	max-width:100%;
}
.merchantbg1{
	position: absolute;
	right:0;
	top:0;
	display: none;
}
.merchantbg2{
	position: absolute;
	right:0;
	bottom:0;
	display: none;
}
.login-form .btn-login{
	font-weight: bold;
}

.merchant .nav-tabs{
	margin-bottom: 40px;
	border:none;
}
.merchant .nav-tabs .nav-link{
	font-size: 16px;
	color: #999999;
	padding:0;
	line-height:20px;
	margin-right: 70px;
	border:none;
}
.merchant .nav-tabs .nav-item.show .nav-link,.merchant .nav-tabs .nav-link.active{
	font-size: 20px;
	color:#3476fe;
	position: relative;
	border:none;
}
.merchant .nav-tabs .nav-link.active:before{
	content:"";
	display:block;
	width:100%;
	height:3px;
	background-color: #3476fe;
	position: absolute;
	left:0;
	bottom:-15px;
}

@media (min-width: 992px){
	.container {
		max-width: 100%;
	}
	.login{

	}
	.header::after{
		display:none;
	}
	.logo-login{
		justify-content:flex-start;
		padding:60px 60px;
	}
	.logo-login img{
		height:58px;
	}

	.login-leftbox{
		text-align: center;
	}
	.login-leftbox .img img{
		max-width:100%;
	}
	.login-leftbox dl{
		margin-top: 40px;
		color: #fff;
	}
	.login-leftbox dt{
		font-size: 25px;
		font-weight: bold;
		line-height:1;
		margin-bottom: 15px;
		letter-spacing:4px;
	}
	.login-leftbox dd{
		font-size: 20px;
		line-height: 30px;
	}
	.login-form{
		padding:0 90px;
		margin-top: -50px;
	}
	.login-form .form-control{
		height:40px;
		line-height:40px;
		padding:0 50px;
		font-size:17px;
	}
	.login-form .btn-login{
		font-size: 21px;
		letter-spacing:20px;
	}
	.login-form .item{
		margin-bottom: 30px;
	}
	.login-form .item .icon{
		width:50px;
	}
	.login-form .item .icon img{
		height:25px;
	}
	.login-form h1{
		font-size: 34px;
		letter-spacing: 5px;
		padding-top: 0;
		padding-bottom: 50px;
	}
	.other-login .t{
		font-size: 18px;
		line-height:30px;
		height:30px;
	}
	.other-login .bd img{
		width:108px;
		margin-top: 60px;
		margin-bottom: 30px;
	}
	.other-login .bd{
		font-size: 20px;
	}

	.login-form .p{
		font-size:16px;
		margin-top: 165px;
		padding-bottom: 50px;
	}
	.login-form .msg{
		height:50px;
		line-height:50px;
		font-size:16px;
	}


	.merchant{
		min-height:900px;
		background: none;
		position: relative;
	}
	.merchant:before{
		content:"";
		display:block;
		width:50%;
		height:100%;
		background-image:linear-gradient(#3478fd,#33aafe);
		position:absolute;
		left:0;
		top:0;
	}
	.merchant .login-leftbox dl{

	}
	.merchant .login-form{
		width:520px;
		margin-right: auto;
		padding:0 20px;
		margin-left: auto;
	}
	.merchant .logo-login .p{
		color:#4d4d4d;
		border-color:#dddedf;
	}
	.merchant .login-box{
		background-color: transparent;
	}
	.merchant .login-box:before,.merchant .login-box:after{
		display: none;
	}
	.otherlogin .bd dl{
		width:260px;
		margin-left: -130px;
		padding:0 20px 20px;
	}
	.otherlogin .bd dt{
		font-size: 16px;
		padding:15px 0;
	}
	.login-form .end-item{
		font-size: 16px;
	}
	.merchantbg1,.merchantbg2{
		display: block;
	}
	.login-form .item-yzm .yzm-img{
		width:180px;
	}
	.login-form .btn-send{
		height:27px;
		line-height:27px;
		padding:0 34px;
	}

}