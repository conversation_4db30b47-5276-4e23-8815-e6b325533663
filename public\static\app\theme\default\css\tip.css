body {
    background: #1b66ff !important;
}

.el-date-table.is-week-mode .el-date-table__row.current div,.el-date-table.is-week-mode .el-date-table__row:hover div,.el-date-table td.in-range div,.el-date-table td.in-range div:hover {
    background-color: #f2f6fc
}

.el-date-table {
    font-size: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.el-date-table.is-week-mode .el-date-table__row:hover td.available:hover {
    color: #606266
}

.el-date-table.is-week-mode .el-date-table__row:hover td:first-child div {
    margin-left: 5px;
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px
}

.el-date-table.is-week-mode .el-date-table__row:hover td:last-child div {
    margin-right: 5px;
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px
}

.el-date-table td {
    width: 32px;
    height: 30px;
    padding: 4px 0;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative
}

.el-date-table td div {
    height: 30px;
    padding: 3px 0;
    box-sizing: border-box
}

.el-date-table td span {
    width: 24px;
    height: 24px;
    display: block;
    margin: 0 auto;
    line-height: 24px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 50%
}

.el-date-table td.next-month,.el-date-table td.prev-month {
    color: #c0c4cc
}

.el-date-table td.today {
    position: relative
}

.el-date-table td.today span {
    color: #409eff;
    font-weight: 700
}

.el-date-table td.today.end-date span,.el-date-table td.today.start-date span {
    color: #fff
}

.el-date-table td.available:hover {
    color: #409eff
}

.el-date-table td.current:not(.disabled) span {
    color: #fff;
    background-color: #409eff
}

.el-date-table td.end-date div,.el-date-table td.start-date div {
    color: #fff
}

.el-date-table td.end-date span,.el-date-table td.start-date span {
    background-color: #409eff
}

.el-date-table td.start-date div {
    margin-left: 5px;
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px
}

.el-date-table td.end-date div {
    margin-right: 5px;
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px
}

.el-date-table td.disabled div {
    background-color: #f5f7fa;
    opacity: 1;
    cursor: not-allowed;
    color: #c0c4cc
}

.el-date-table td.selected div {
    margin-left: 5px;
    margin-right: 5px;
    background-color: #f2f6fc;
    border-radius: 15px
}

.el-date-table td.selected div:hover {
    background-color: #f2f6fc
}

.el-date-table td.selected span {
    background-color: #409eff;
    color: #fff;
    border-radius: 15px
}

.el-date-table td.week {
    font-size: 80%;
    color: #606266
}

.el-date-table th {
    padding: 5px;
    color: #606266;
    font-weight: 400;
    border-bottom: 1px solid #ebeef5
}

.el-month-table {
    font-size: 12px;
    margin: -1px;
    border-collapse: collapse
}

.el-month-table td {
    text-align: center;
    padding: 8px 0;
    cursor: pointer
}

.el-month-table td div {
    height: 48px;
    padding: 6px 0;
    box-sizing: border-box
}

.el-month-table td.today .cell {
    color: #409eff;
    font-weight: 700
}

.el-month-table td.today.end-date .cell,.el-month-table td.today.start-date .cell {
    color: #fff
}

.el-month-table td.disabled .cell {
    background-color: #f5f7fa;
    cursor: not-allowed;
    color: #c0c4cc
}

.el-month-table td.disabled .cell:hover {
    color: #c0c4cc
}

.el-month-table td .cell {
    width: 60px;
    height: 36px;
    display: block;
    line-height: 36px;
    color: #606266;
    margin: 0 auto;
    border-radius: 18px
}

.el-month-table td .cell:hover {
    color: #409eff
}

.el-month-table td.in-range div,.el-month-table td.in-range div:hover {
    background-color: #f2f6fc
}

.el-month-table td.end-date div,.el-month-table td.start-date div {
    color: #fff
}

.el-month-table td.end-date .cell,.el-month-table td.start-date .cell {
    color: #fff;
    background-color: #409eff
}

.el-month-table td.start-date div {
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px
}

.el-month-table td.end-date div {
    border-top-right-radius: 24px;
    border-bottom-right-radius: 24px
}

.el-month-table td.current:not(.disabled) .cell {
    color: #409eff
}

.el-year-table {
    font-size: 12px;
    margin: -1px;
    border-collapse: collapse
}

.el-year-table .el-icon {
    color: #303133
}

.el-year-table td {
    text-align: center;
    padding: 20px 3px;
    cursor: pointer
}

.el-year-table td.today .cell {
    color: #409eff;
    font-weight: 700
}

.el-year-table td.disabled .cell {
    background-color: #f5f7fa;
    cursor: not-allowed;
    color: #c0c4cc
}

.el-year-table td.disabled .cell:hover {
    color: #c0c4cc
}

.el-year-table td .cell {
    width: 48px;
    height: 32px;
    display: block;
    line-height: 32px;
    color: #606266;
    margin: 0 auto
}

.el-year-table td .cell:hover,.el-year-table td.current:not(.disabled) .cell {
    color: #409eff
}

.el-time-spinner.has-seconds .el-time-spinner__wrapper {
    width: 33.3%
}

.el-time-spinner__wrapper {
    max-height: 190px;
    overflow: auto;
    display: inline-block;
    width: 50%;
    vertical-align: top;
    position: relative
}

.el-time-spinner__wrapper .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
    padding-bottom: 15px
}

.el-time-spinner__wrapper.is-arrow {
    box-sizing: border-box;
    text-align: center;
    overflow: hidden
}

.el-time-spinner__wrapper.is-arrow .el-time-spinner__list {
    transform: translateY(-32px)
}

.el-time-spinner__wrapper.is-arrow .el-time-spinner__item:hover:not(.disabled):not(.active) {
    background: #fff;
    cursor: default
}

.el-time-spinner__arrow {
    font-size: 12px;
    color: #909399;
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 1;
    text-align: center;
    height: 30px;
    line-height: 30px;
    cursor: pointer
}

.el-time-spinner__arrow:hover {
    color: #409eff
}

.el-time-spinner__arrow.el-icon-arrow-up {
    top: 10px
}

.el-time-spinner__arrow.el-icon-arrow-down {
    bottom: 10px
}

.el-time-spinner__input.el-input {
    width: 70%
}

.el-time-spinner__input.el-input .el-input__inner,.el-time-spinner__list {
    padding: 0;
    text-align: center
}

.el-time-spinner__list {
    margin: 0;
    list-style: none
}

.el-time-spinner__list:after,.el-time-spinner__list:before {
    content: "";
    display: block;
    width: 100%;
    height: 80px
}

.el-time-spinner__item {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #606266
}

.el-time-spinner__item:hover:not(.disabled):not(.active) {
    background: #f5f7fa;
    cursor: pointer
}

.el-time-spinner__item.active:not(.disabled) {
    color: #303133;
    font-weight: 700
}

.el-time-spinner__item.disabled {
    color: #c0c4cc;
    cursor: not-allowed
}

.el-date-editor {
    position: relative;
    display: inline-block;
    text-align: left
}

.el-date-editor.el-input,.el-date-editor.el-input__inner {
    width: 220px
}

.el-date-editor--monthrange.el-input,.el-date-editor--monthrange.el-input__inner {
    width: 300px
}

.el-date-editor--daterange.el-input,.el-date-editor--daterange.el-input__inner,.el-date-editor--timerange.el-input,.el-date-editor--timerange.el-input__inner {
    width: 350px
}

.el-date-editor--datetimerange.el-input,.el-date-editor--datetimerange.el-input__inner {
    width: 400px
}

.el-date-editor--dates .el-input__inner {
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-date-editor .el-icon-circle-close {
    cursor: pointer
}

.el-date-editor .el-range__icon {
    font-size: 14px;
    margin-left: -5px;
    color: #c0c4cc;
    float: left;
    line-height: 32px
}

.el-date-editor .el-range-input,.el-date-editor .el-range-separator {
    height: 100%;
    margin: 0;
    text-align: center;
    display: inline-block;
    font-size: 14px
}

.el-date-editor .el-range-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: 0;
    padding: 0;
    width: 39%;
    color: #606266
}

.el-date-editor .el-range-input::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-date-editor .el-range-input:-ms-input-placeholder,.el-date-editor .el-range-input::-ms-input-placeholder {
    color: #c0c4cc
}

.el-date-editor .el-range-input::placeholder {
    color: #c0c4cc
}

.el-date-editor .el-range-separator {
    padding: 0 5px;
    line-height: 32px;
    width: 5%;
    color: #303133
}

.el-date-editor .el-range__close-icon {
    font-size: 14px;
    color: #c0c4cc;
    width: 25px;
    display: inline-block;
    float: right;
    line-height: 32px
}

.el-range-editor.el-input__inner {
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-align: center;
    align-items: center;
    padding: 3px 10px
}

.el-range-editor .el-range-input {
    line-height: 1
}

.el-range-editor.is-active,.el-range-editor.is-active:hover {
    border-color: #409eff
}

.el-range-editor--medium.el-input__inner {
    height: 36px
}

.el-range-editor--medium .el-range-separator {
    line-height: 28px;
    font-size: 14px
}

.el-range-editor--medium .el-range-input {
    font-size: 14px
}

.el-range-editor--medium .el-range__close-icon,.el-range-editor--medium .el-range__icon {
    line-height: 28px
}

.el-range-editor--small.el-input__inner {
    height: 32px
}

.el-range-editor--small .el-range-separator {
    line-height: 24px;
    font-size: 13px
}

.el-range-editor--small .el-range-input {
    font-size: 13px
}

.el-range-editor--small .el-range__close-icon,.el-range-editor--small .el-range__icon {
    line-height: 24px
}

.el-range-editor--mini.el-input__inner {
    height: 28px
}

.el-range-editor--mini .el-range-separator {
    line-height: 20px;
    font-size: 12px
}

.el-range-editor--mini .el-range-input {
    font-size: 12px
}

.el-range-editor--mini .el-range__close-icon,.el-range-editor--mini .el-range__icon {
    line-height: 20px
}

.el-range-editor.is-disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed
}

.el-range-editor.is-disabled:focus,.el-range-editor.is-disabled:hover {
    border-color: #e4e7ed
}

.el-range-editor.is-disabled input {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed
}

.el-range-editor.is-disabled input::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-range-editor.is-disabled input:-ms-input-placeholder,.el-range-editor.is-disabled input::-ms-input-placeholder {
    color: #c0c4cc
}

.el-range-editor.is-disabled input::placeholder {
    color: #c0c4cc
}

.el-range-editor.is-disabled .el-range-separator {
    color: #c0c4cc
}

.el-picker-panel {
    color: #606266;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    background: #fff;
    border-radius: 4px;
    line-height: 30px;
    margin: 5px 0
}

.el-picker-panel__body-wrapper:after,.el-picker-panel__body:after {
    content: "";
    display: table;
    clear: both
}

.el-picker-panel__content {
    position: relative;
    margin: 15px
}

.el-picker-panel__footer {
    border-top: 1px solid #e4e4e4;
    padding: 4px;
    text-align: right;
    background-color: #fff;
    position: relative;
    font-size: 0
}

.el-picker-panel__shortcut {
    display: block;
    width: 100%;
    border: 0;
    background-color: transparent;
    line-height: 28px;
    font-size: 14px;
    color: #606266;
    padding-left: 12px;
    text-align: left;
    outline: 0;
    cursor: pointer
}

.el-picker-panel__shortcut:hover {
    color: #409eff
}

.el-picker-panel__shortcut.active {
    background-color: #e6f1fe;
    color: #409eff
}

.el-picker-panel__btn {
    border: 1px solid #dcdcdc;
    color: #333;
    line-height: 24px;
    border-radius: 2px;
    padding: 0 20px;
    cursor: pointer;
    background-color: transparent;
    outline: 0;
    font-size: 12px
}

.el-picker-panel__btn[disabled] {
    color: #ccc;
    cursor: not-allowed
}

.el-picker-panel__icon-btn {
    font-size: 12px;
    color: #303133;
    border: 0;
    background: 0 0;
    cursor: pointer;
    outline: 0;
    margin-top: 8px
}

.el-picker-panel__icon-btn:hover {
    color: #409eff
}

.el-picker-panel__icon-btn.is-disabled {
    color: #bbb
}

.el-picker-panel__icon-btn.is-disabled:hover {
    cursor: not-allowed
}

.el-picker-panel__link-btn {
    vertical-align: middle
}

.el-picker-panel [slot=sidebar],.el-picker-panel__sidebar {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 110px;
    border-right: 1px solid #e4e4e4;
    box-sizing: border-box;
    padding-top: 6px;
    background-color: #fff;
    overflow: auto
}

.el-picker-panel [slot=sidebar]+.el-picker-panel__body,.el-picker-panel__sidebar+.el-picker-panel__body {
    margin-left: 110px
}

.el-date-picker {
    width: 322px
}

.el-date-picker.has-sidebar.has-time {
    width: 434px
}

.el-date-picker.has-sidebar {
    width: 438px
}

.el-date-picker.has-time .el-picker-panel__body-wrapper {
    position: relative
}

.el-date-picker .el-picker-panel__content {
    width: 292px
}

.el-date-picker table {
    table-layout: fixed;
    width: 100%
}

.el-date-picker__editor-wrap {
    position: relative;
    display: table-cell;
    padding: 0 5px
}

.el-date-picker__time-header {
    position: relative;
    border-bottom: 1px solid #e4e4e4;
    font-size: 12px;
    padding: 8px 5px 5px;
    display: table;
    width: 100%;
    box-sizing: border-box
}

.el-date-picker__header {
    margin: 12px;
    text-align: center
}

.el-date-picker__header--bordered {
    margin-bottom: 0;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5
}

.el-date-picker__header--bordered+.el-picker-panel__content {
    margin-top: 0
}

.el-date-picker__header-label {
    font-size: 16px;
    font-weight: 500;
    padding: 0 5px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    color: #606266
}

.el-date-picker__header-label.active,.el-date-picker__header-label:hover {
    color: #409eff
}

.el-date-picker__prev-btn {
    float: left
}

.el-date-picker__next-btn {
    float: right
}

.el-date-picker__time-wrap {
    padding: 10px;
    text-align: center
}

.el-date-picker__time-label {
    float: left;
    cursor: pointer;
    line-height: 30px;
    margin-left: 10px
}

.el-date-range-picker {
    width: 646px
}

.el-date-range-picker.has-sidebar {
    width: 756px
}

.el-date-range-picker table {
    table-layout: fixed;
    width: 100%
}

.el-date-range-picker .el-picker-panel__body {
    min-width: 513px
}

.el-date-range-picker .el-picker-panel__content {
    margin: 0
}

.el-date-range-picker__header {
    position: relative;
    text-align: center;
    height: 28px
}

.el-date-range-picker__header [class*=arrow-left] {
    float: left
}

.el-date-range-picker__header [class*=arrow-right] {
    float: right
}

.el-date-range-picker__header div {
    font-size: 16px;
    font-weight: 500;
    margin-right: 50px
}

.el-date-range-picker__content {
    float: left;
    width: 50%;
    box-sizing: border-box;
    margin: 0;
    padding: 16px
}

.el-date-range-picker__content.is-left {
    border-right: 1px solid #e4e4e4
}

.el-date-range-picker__content .el-date-range-picker__header div {
    margin-left: 50px;
    margin-right: 50px
}

.el-date-range-picker__editors-wrap {
    box-sizing: border-box;
    display: table-cell
}

.el-date-range-picker__editors-wrap.is-right {
    text-align: right
}

.el-date-range-picker__time-header {
    position: relative;
    border-bottom: 1px solid #e4e4e4;
    font-size: 12px;
    padding: 8px 5px 5px;
    display: table;
    width: 100%;
    box-sizing: border-box
}

.el-date-range-picker__time-header>.el-icon-arrow-right {
    font-size: 20px;
    vertical-align: middle;
    display: table-cell;
    color: #303133
}

.el-date-range-picker__time-picker-wrap {
    position: relative;
    display: table-cell;
    padding: 0 5px
}

.el-date-range-picker__time-picker-wrap .el-picker-panel {
    position: absolute;
    top: 13px;
    right: 0;
    z-index: 1;
    background: #fff
}

.el-time-range-picker {
    width: 354px;
    overflow: visible
}

.el-time-range-picker__content {
    position: relative;
    text-align: center;
    padding: 10px
}

.el-time-range-picker__cell {
    box-sizing: border-box;
    margin: 0;
    padding: 4px 7px 7px;
    width: 50%;
    display: inline-block
}

.el-time-range-picker__header {
    margin-bottom: 5px;
    text-align: center;
    font-size: 14px
}

.el-time-panel,.el-time-range-picker__body {
    border-radius: 2px;
    border: 1px solid #e4e7ed
}

.el-time-panel {
    margin: 5px 0;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    position: absolute;
    width: 180px;
    left: 0;
    z-index: 1000;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    box-sizing: content-box
}

.el-time-panel__content {
    font-size: 0;
    position: relative;
    overflow: hidden
}

.el-time-panel__content:after,.el-time-panel__content:before {
    content: "";
    top: 50%;
    position: absolute;
    margin-top: -15px;
    height: 32px;
    z-index: -1;
    left: 0;
    right: 0;
    box-sizing: border-box;
    padding-top: 6px;
    text-align: left;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed
}

.el-time-panel__content:after {
    left: 50%;
    margin-left: 12%;
    margin-right: 12%
}

.el-time-panel__content:before {
    padding-left: 50%;
    margin-right: 12%;
    margin-left: 12%
}

.el-time-panel__content.has-seconds:after {
    left: 66.66667%
}

.el-time-panel__content.has-seconds:before {
    padding-left: 33.33333%
}

.el-time-panel__footer {
    border-top: 1px solid #e4e4e4;
    padding: 4px;
    height: 36px;
    line-height: 25px;
    text-align: right;
    box-sizing: border-box
}

.el-time-panel__btn {
    border: none;
    line-height: 28px;
    padding: 0 5px;
    margin: 0 5px;
    cursor: pointer;
    background-color: transparent;
    outline: 0;
    font-size: 12px;
    color: #303133
}

.el-time-panel__btn.confirm {
    font-weight: 800;
    color: #409eff
}

.el-textarea {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: bottom;
    font-size: 14px
}

.el-textarea__inner {
    display: block;
    resize: vertical;
    padding: 5px 15px;
    line-height: 1.5;
    box-sizing: border-box;
    width: 100%;
    font-size: inherit;
    color: #606266;
    background-color: #fff;
    background-image: none;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1)
}

.el-textarea__inner::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-textarea__inner:-ms-input-placeholder,.el-textarea__inner::-ms-input-placeholder {
    color: #c0c4cc
}

.el-textarea__inner::placeholder {
    color: #c0c4cc
}

.el-textarea__inner:hover {
    border-color: #c0c4cc
}

.el-textarea__inner:focus {
    outline: 0;
    border-color: #409eff
}

.el-textarea .el-input__count {
    color: #909399;
    background: #fff;
    position: absolute;
    font-size: 12px;
    bottom: 5px;
    right: 10px
}

.el-textarea.is-disabled .el-textarea__inner {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed
}

.el-textarea.is-disabled .el-textarea__inner::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-textarea.is-disabled .el-textarea__inner:-ms-input-placeholder,.el-textarea.is-disabled .el-textarea__inner::-ms-input-placeholder {
    color: #c0c4cc
}

.el-textarea.is-disabled .el-textarea__inner::placeholder {
    color: #c0c4cc
}

.el-textarea.is-exceed .el-textarea__inner {
    border-color: #f56c6c
}

.el-textarea.is-exceed .el-input__count {
    color: #f56c6c
}

.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 100%
}

.el-input::-webkit-scrollbar {
    z-index: 11;
    width: 6px
}

.el-input::-webkit-scrollbar:horizontal {
    height: 6px
}

.el-input::-webkit-scrollbar-thumb {
    border-radius: 5px;
    width: 6px;
    background: #b4bccc
}

.el-input::-webkit-scrollbar-corner,.el-input::-webkit-scrollbar-track {
    background: #fff
}

.el-input::-webkit-scrollbar-track-piece {
    background: #fff;
    width: 6px
}

.el-input .el-input__clear {
    color: #c0c4cc;
    font-size: 14px;
    cursor: pointer;
    transition: color .2s cubic-bezier(.645,.045,.355,1)
}

.el-input .el-input__clear:hover {
    color: #909399
}

.el-input .el-input__count {
    height: 100%;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-align: center;
    align-items: center;
    color: #909399;
    font-size: 12px
}

.el-input .el-input__count .el-input__count-inner {
    background: #fff;
    line-height: normal;
    display: inline-block;
    padding: 0 5px
}

.el-input__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 40px;
    line-height: 40px;
    outline: 0;
    padding: 0 15px;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);
    width: 100%
}

.el-input__prefix,.el-input__suffix {
    position: absolute;
    top: 0;
    -webkit-transition: all .3s;
    text-align: center;
    height: 100%;
    color: #c0c4cc
}

.el-input__inner::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-input__inner:-ms-input-placeholder,.el-input__inner::-ms-input-placeholder {
    color: #c0c4cc
}

.el-input__inner::placeholder {
    color: #c0c4cc
}

.el-input__inner:hover {
    border-color: #c0c4cc
}

.el-input.is-active .el-input__inner,.el-input__inner:focus {
    border-color: #409eff;
    outline: 0
}

.el-input__suffix {
    right: 5px;
    transition: all .3s;
    pointer-events: none
}

.el-input__suffix-inner {
    pointer-events: all
}

.el-input__prefix {
    left: 5px;
    transition: all .3s
}

.el-input__icon {
    height: 100%;
    width: 25px;
    text-align: center;
    transition: all .3s;
    line-height: 40px
}

.el-input__icon:after {
    content: "";
    height: 100%;
    width: 0;
    display: inline-block;
    vertical-align: middle
}

.el-input__validateIcon {
    pointer-events: none
}

.el-input.is-disabled .el-input__inner {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed
}

.el-input.is-disabled .el-input__inner::-webkit-input-placeholder {
    color: #c0c4cc
}

.el-input.is-disabled .el-input__inner:-ms-input-placeholder,.el-input.is-disabled .el-input__inner::-ms-input-placeholder {
    color: #c0c4cc
}

.el-input.is-disabled .el-input__inner::placeholder {
    color: #c0c4cc
}

.el-input.is-disabled .el-input__icon {
    cursor: not-allowed
}

.el-input.is-exceed .el-input__inner {
    border-color: #f56c6c
}

.el-input.is-exceed .el-input__suffix .el-input__count {
    color: #f56c6c
}

.el-input--suffix .el-input__inner {
    padding-right: 30px
}

.el-input--prefix .el-input__inner {
    padding-left: 30px
}

.el-input--medium {
    font-size: 14px
}

.el-input--medium .el-input__inner {
    height: 36px;
    line-height: 36px
}

.el-input--medium .el-input__icon {
    line-height: 36px
}

.el-input--small {
    font-size: 13px
}

.el-input--small .el-input__inner {
    height: 32px;
    line-height: 32px
}

.el-input--small .el-input__icon {
    line-height: 32px
}

.el-input--mini {
    font-size: 12px
}

.el-input--mini .el-input__inner {
    height: 28px;
    line-height: 28px
}

.el-input--mini .el-input__icon {
    line-height: 28px
}

.el-input-group {
    line-height: normal;
    display: inline-table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0
}

.el-input-group>.el-input__inner {
    vertical-align: middle;
    display: table-cell
}

.el-input-group__append,.el-input-group__prepend {
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 20px;
    width: 1px;
    white-space: nowrap
}

.el-input-group--prepend .el-input__inner,.el-input-group__append {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.el-input-group--append .el-input__inner,.el-input-group__prepend {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.el-input-group__append:focus,.el-input-group__prepend:focus {
    outline: 0
}

.el-input-group__append .el-button,.el-input-group__append .el-select,.el-input-group__prepend .el-button,.el-input-group__prepend .el-select {
    display: inline-block;
    margin: -10px -20px
}

.el-input-group__append button.el-button,.el-input-group__append div.el-select .el-input__inner,.el-input-group__append div.el-select:hover .el-input__inner,.el-input-group__prepend button.el-button,.el-input-group__prepend div.el-select .el-input__inner,.el-input-group__prepend div.el-select:hover .el-input__inner {
    border-color: transparent;
    background-color: transparent;
    color: inherit;
    border-top: 0;
    border-bottom: 0
}

.el-input-group__append .el-button,.el-input-group__append .el-input,.el-input-group__prepend .el-button,.el-input-group__prepend .el-input {
    font-size: inherit
}

.el-input-group__prepend {
    border-right: 0
}

.el-input-group__append {
    border-left: 0
}

.el-input-group--append .el-select .el-input.is-focus .el-input__inner,.el-input-group--prepend .el-select .el-input.is-focus .el-input__inner {
    border-color: transparent
}

.el-input__inner::-ms-clear {
    display: none;
    width: 0;
    height: 0
}

.el-scrollbar {
    overflow: hidden;
    position: relative
}

.el-scrollbar:active>.el-scrollbar__bar,.el-scrollbar:focus>.el-scrollbar__bar,.el-scrollbar:hover>.el-scrollbar__bar {
    opacity: 1;
    transition: opacity .34s ease-out
}

.el-scrollbar__wrap {
    overflow: scroll;
    height: 100%
}

.el-scrollbar__wrap--hidden-default::-webkit-scrollbar {
    width: 0;
    height: 0
}

.el-scrollbar__thumb {
    position: relative;
    display: block;
    width: 0;
    height: 0;
    cursor: pointer;
    border-radius: inherit;
    background-color: hsla(220,4%,58%,.3);
    transition: background-color .3s
}

.el-scrollbar__thumb:hover {
    background-color: hsla(220,4%,58%,.5)
}

.el-scrollbar__bar {
    position: absolute;
    right: 2px;
    bottom: 2px;
    z-index: 1;
    border-radius: 4px;
    opacity: 0;
    transition: opacity .12s ease-out
}

.el-scrollbar__bar.is-vertical {
    width: 6px;
    top: 2px
}

.el-scrollbar__bar.is-vertical>div {
    width: 100%
}

.el-scrollbar__bar.is-horizontal {
    height: 6px;
    left: 2px
}

.el-scrollbar__bar.is-horizontal>div {
    height: 100%
}

.el-popper .popper__arrow,.el-popper .popper__arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.el-popper .popper__arrow {
    border-width: 6px;
    filter: drop-shadow(0 2px 12px rgba(0,0,0,.03))
}

.el-popper .popper__arrow:after {
    content: " ";
    border-width: 6px
}

.el-popper[x-placement^=top] {
    margin-bottom: 12px
}

.el-popper[x-placement^=top] .popper__arrow {
    bottom: -6px;
    left: 50%;
    margin-right: 3px;
    border-top-color: #ebeef5;
    border-bottom-width: 0
}

.el-popper[x-placement^=top] .popper__arrow:after {
    bottom: 1px;
    margin-left: -6px;
    border-top-color: #fff;
    border-bottom-width: 0
}

.el-popper[x-placement^=bottom] {
    margin-top: 12px
}

.el-popper[x-placement^=bottom] .popper__arrow {
    top: -6px;
    left: 50%;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5
}

.el-popper[x-placement^=bottom] .popper__arrow:after {
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff
}

.el-popper[x-placement^=right] {
    margin-left: 12px
}

.el-popper[x-placement^=right] .popper__arrow {
    top: 50%;
    left: -6px;
    margin-bottom: 3px;
    border-right-color: #ebeef5;
    border-left-width: 0
}

.el-popper[x-placement^=right] .popper__arrow:after {
    bottom: -6px;
    left: 1px;
    border-right-color: #fff;
    border-left-width: 0
}

.el-popper[x-placement^=left] {
    margin-right: 12px
}

.el-popper[x-placement^=left] .popper__arrow {
    top: 50%;
    right: -6px;
    margin-bottom: 3px;
    border-right-width: 0;
    border-left-color: #ebeef5
}

.el-popper[x-placement^=left] .popper__arrow:after {
    right: 1px;
    bottom: -6px;
    margin-left: -6px;
    border-right-width: 0;
    border-left-color: #fff
}

.el-fade-in-enter,.el-fade-in-leave-active,.el-fade-in-linear-enter,.el-fade-in-linear-leave,.el-fade-in-linear-leave-active,.fade-in-linear-enter,.fade-in-linear-leave,.fade-in-linear-leave-active {
    opacity: 0
}

.el-fade-in-linear-enter-active,.el-fade-in-linear-leave-active,.fade-in-linear-enter-active,.fade-in-linear-leave-active {
    transition: opacity .2s linear
}

.el-fade-in-enter-active,.el-fade-in-leave-active,.el-zoom-in-center-enter-active,.el-zoom-in-center-leave-active {
    transition: all .3s cubic-bezier(.55,0,.1,1)
}

.el-zoom-in-center-enter,.el-zoom-in-center-leave-active {
    opacity: 0;
    transform: scaleX(0)
}

.el-zoom-in-top-enter-active,.el-zoom-in-top-leave-active {
    opacity: 1;
    transform: scaleY(1);
    transition: transform .3s cubic-bezier(.23,1,.32,1),opacity .3s cubic-bezier(.23,1,.32,1);
    transform-origin: center top
}

.el-zoom-in-top-enter,.el-zoom-in-top-leave-active {
    opacity: 0;
    transform: scaleY(0)
}

.el-zoom-in-bottom-enter-active,.el-zoom-in-bottom-leave-active {
    opacity: 1;
    transform: scaleY(1);
    transition: transform .3s cubic-bezier(.23,1,.32,1),opacity .3s cubic-bezier(.23,1,.32,1);
    transform-origin: center bottom
}

.el-zoom-in-bottom-enter,.el-zoom-in-bottom-leave-active {
    opacity: 0;
    transform: scaleY(0)
}

.el-zoom-in-left-enter-active,.el-zoom-in-left-leave-active {
    opacity: 1;
    transform: scale(1);
    transition: transform .3s cubic-bezier(.23,1,.32,1),opacity .3s cubic-bezier(.23,1,.32,1);
    transform-origin: top left
}

.el-zoom-in-left-enter,.el-zoom-in-left-leave-active {
    opacity: 0;
    transform: scale(.45)
}

.collapse-transition {
    transition: height .3s ease-in-out,padding-top .3s ease-in-out,padding-bottom .3s ease-in-out
}

.horizontal-collapse-transition {
    transition: width .3s ease-in-out,padding-left .3s ease-in-out,padding-right .3s ease-in-out
}

.el-list-enter-active,.el-list-leave-active {
    transition: all 1s
}

.el-list-enter,.el-list-leave-active {
    opacity: 0;
    transform: translateY(-30px)
}

.el-opacity-transition {
    transition: opacity .3s cubic-bezier(.55,0,.1,1)
}

#sale_tips {
    background: #1b66ff;
    height: 100%
}

#sale_tips b {
    color: #545454;
    padding: 0 .07rem
}

#sale_tips .btn_cons:active,#sale_tips .btn_gopay:active {
    opacity: .7!important;
    transition: .1s!important;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

#sale_tips .title {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: url(data:image/png;base64,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) no-repeat;background-size: cover
}

#sale_tips .title p {
    font-size: .4rem;
    text-align: center;
    color: #fff;
    font-weight: 700;
    display: inline-block
}

#sale_tips .title #sale {
    width: .52rem;
    vertical-align: middle;
    margin-right: .18rem
}

#sale_tips .container {
    padding: .56rem .413333rem;
    box-sizing: border-box;
    background: #fff;
    width: 94%;
    margin: 0 auto;
    border-radius: .133333rem;
    max-width: 600px
}

#sale_tips .container .order_box .tit {
    font-size: .413333rem;
    text-align: left;
    display: block;
    font-weight: 700;
    margin-bottom: .413333rem;
    color: #545454
}

#sale_tips .container .order_box .tit img {
    width: .4rem;
    margin-right: .1rem
}

#sale_tips .container .order_box .goods .goods_group {
    display: block;
    margin-top: .18rem;
    margin-bottom: .18rem;
    font-size: .32rem
}

#sale_tips .container .order_box .goods .goods_group div:first-child {
    display: inline-block;
    color: #999;
    letter-spacing: .01rem
}

#sale_tips .container .risk_box .tit {
    font-size: .413333rem;
    text-align: left;
    display: block;
    font-weight: 700;
    margin-bottom: .513333rem;
    margin-top: .67rem;
    color: #545454
}

#sale_tips .container .risk_box .tit img {
    width: .4rem;
    margin-right: .1rem
}

#sale_tips .container .risk_box .content {
    margin-top: .13rem
}

#sale_tips .container .risk_box .content .risk_group {
    margin-bottom: .413333rem
}

#sale_tips .container .risk_box .content .risk_group div:first-child {
    display: block;
    color: #545454;
    font-size: .34rem;
    font-weight: 700
}

#sale_tips .container .risk_box .content .risk_group div:nth-child(2) {
    margin-top: .33rem;
    display: block;
    color: #999;
    font-size: .32rem;
    letter-spacing: .01rem;
    line-height: .57rem
}

#sale_tips .container .btns {
    margin-top: .3rem;
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-pack: start;
    justify-content: flex-start
}

#sale_tips .container .btns .toast {
    position: absolute
}

#sale_tips .container .btns .btn_cons {
    border-radius: 1.32rem;
    width: 2.813333rem;
    margin-right: .13rem;
    font-style: normal;
    text-decoration: none;
    color: #2a62ff;
    border: .026667rem solid #2a62ff
}

#sale_tips .container .btns .btn_cons,#sale_tips .container .btns .btn_gopay {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: .273333rem;
    font-weight: 700;
    height: 1rem;
    padding-left: .07rem;
    padding-right: .07rem;
    letter-spacing: .02rem
}

#sale_tips .container .btns .btn_gopay {
    color: #fff;
    background: #1b66ff;
    border-radius: 1.32rem;
    -ms-flex: 1;
    flex: 1;
    box-shadow: 0 .01rem .08rem rgba(0,0,0,.117647),0 .01rem .05rem rgba(0,0,0,.117647);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

#sale_tips .container .btns .ban {
    background: #2a62ff;
    color: #fff;
    box-shadow: none;
    text-decoration: none;
}
