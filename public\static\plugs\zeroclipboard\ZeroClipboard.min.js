/*!
 * ZeroClipboard
 * The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface
 * Copyright (c) 2009-2017 <PERSON>, <PERSON>
 * Licensed MIT
 * http://zeroclipboard.github.io/
 * v2.4.0-beta.1
 */
!function(a,b){"use strict";var c,d,e,f=a,g=f.document,h=f.navigator,i=f.setTimeout,j=f.clearTimeout,k=f.setInterval,l=f.clearInterval,m=f.getComputedStyle,n=f.encodeURIComponent,o=f.ActiveXObject,p=f.Error,q=f.Number.parseInt||f.parseInt,r=f.Number.parseFloat||f.parseFloat,s=f.Number.isNaN||f.isNaN,t=f.Date.now,u=f.Object.keys,v=f.Object.prototype.hasOwnProperty,w=f.Array.prototype.slice,x=function(){var a=function(a){return a};if("function"==typeof f.wrap&&"function"==typeof f.unwrap)try{var b=g.createElement("div"),c=f.unwrap(b);1===b.nodeType&&c&&1===c.nodeType&&(a=f.unwrap)}catch(d){}return a}(),y=function(a){return w.call(a,0)},z=function(){var a,c,d,e,f,g,h=y(arguments),i=h[0]||{};for(a=1,c=h.length;c>a;a++)if(null!=(d=h[a]))for(e in d)v.call(d,e)&&(f=i[e],g=d[e],i!==g&&g!==b&&(i[e]=g));return i},A=function(a){var b,c,d,e;if("object"!=typeof a||null==a||"number"==typeof a.nodeType)b=a;else if("number"==typeof a.length)for(b=[],c=0,d=a.length;d>c;c++)v.call(a,c)&&(b[c]=A(a[c]));else{b={};for(e in a)v.call(a,e)&&(b[e]=A(a[e]))}return b},B=function(a,b){for(var c={},d=0,e=b.length;e>d;d++)b[d]in a&&(c[b[d]]=a[b[d]]);return c},C=function(a,b){var c={};for(var d in a)-1===b.indexOf(d)&&(c[d]=a[d]);return c},D=function(a){if(a)for(var b in a)v.call(a,b)&&delete a[b];return a},E=function(a,b){if(a&&1===a.nodeType&&a.ownerDocument&&b&&(1===b.nodeType&&b.ownerDocument&&b.ownerDocument===a.ownerDocument||9===b.nodeType&&!b.ownerDocument&&b===a.ownerDocument))do{if(a===b)return!0;a=a.parentNode}while(a);return!1},F=function(a){var b;return"string"==typeof a&&a&&(b=a.split("#")[0].split("?")[0],b=a.slice(0,a.lastIndexOf("/")+1)),b},G=function(a){var b,c;return"string"==typeof a&&a&&(c=a.match(/^(?:|[^:@]*@|.+\)@(?=http[s]?|file)|.+?\s+(?: at |@)(?:[^:\(]+ )*[\(]?)((?:http[s]?|file):\/\/[\/]?.+?\/[^:\)]*?)(?::\d+)(?::\d+)?/),c&&c[1]?b=c[1]:(c=a.match(/\)@((?:http[s]?|file):\/\/[\/]?.+?\/[^:\)]*?)(?::\d+)(?::\d+)?/),c&&c[1]&&(b=c[1]))),b},H=function(){var a,b;try{throw new p}catch(c){b=c}return b&&(a=b.sourceURL||b.fileName||G(b.stack)),a},I=function(){var a,c,d;if(g.currentScript&&(a=g.currentScript.src))return a;if(c=g.getElementsByTagName("script"),1===c.length)return c[0].src||b;if("readyState"in(c[0]||document.createElement("script")))for(d=c.length;d--;)if("interactive"===c[d].readyState&&(a=c[d].src))return a;return"loading"===g.readyState&&(a=c[c.length-1].src)?a:(a=H())?a:b},J=function(){var a,c,d,e=g.getElementsByTagName("script");for(a=e.length;a--;){if(!(d=e[a].src)){c=null;break}if(d=F(d),null==c)c=d;else if(c!==d){c=null;break}}return c||b},K=function(){var a=F(I())||J()||"";return a+"ZeroClipboard.swf"},L=function(){var a=/win(dows|[\s]?(nt|me|ce|xp|vista|[\d]+))/i;return!!h&&(a.test(h.appVersion||"")||a.test(h.platform||"")||-1!==(h.userAgent||"").indexOf("Windows"))},M=function(){return null==f.opener&&(!!f.top&&f!=f.top||!!f.parent&&f!=f.parent)}(),N="html"===g.documentElement.nodeName,O={bridge:null,version:"0.0.0",pluginType:"unknown",sandboxed:null,disabled:null,outdated:null,insecure:null,unavailable:null,degraded:null,deactivated:null,overdue:null,ready:null},P="11.0.0",Q={},R={},S=null,T=0,U=0,V={ready:"Flash communication is established",error:{"flash-sandboxed":"Attempting to run Flash in a sandboxed iframe, which is impossible","flash-disabled":"Flash is disabled or not installed. May also be attempting to run Flash in a sandboxed iframe, which is impossible.","flash-outdated":"Flash is too outdated to support ZeroClipboard","flash-insecure":"Flash will be unable to communicate due to a protocol mismatch between your `swfPath` configuration and the page","flash-unavailable":"Flash is unable to communicate bidirectionally with JavaScript","flash-degraded":"Flash is unable to preserve data fidelity when communicating with JavaScript","flash-deactivated":"Flash is too outdated for your browser and/or is configured as click-to-activate.\nThis may also mean that the ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity.\nMay also be attempting to run Flash in a sandboxed iframe, which is impossible.","flash-overdue":"Flash communication was established but NOT within the acceptable time limit","version-mismatch":"ZeroClipboard JS version number does not match ZeroClipboard SWF version number","clipboard-error":"At least one error was thrown while ZeroClipboard was attempting to inject your data into the clipboard","config-mismatch":"ZeroClipboard configuration does not match Flash's reality","swf-not-found":"The ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity","browser-unsupported":"The browser does not support the required HTML DOM and JavaScript features"}},W=["flash-unavailable","flash-degraded","flash-overdue","version-mismatch","config-mismatch","clipboard-error"],X=["flash-sandboxed","flash-disabled","flash-outdated","flash-insecure","flash-unavailable","flash-degraded","flash-deactivated","flash-overdue"],Y=new RegExp("^flash-("+X.map(function(a){return a.replace(/^flash-/,"")}).join("|")+")$"),Z=new RegExp("^flash-("+X.filter(function(a){return"flash-disabled"!==a}).map(function(a){return a.replace(/^flash-/,"")}).join("|")+")$"),$={swfPath:K(),trustedDomains:f.location.host?[f.location.host]:[],cacheBust:!0,forceEnhancedClipboard:!1,flashLoadTimeout:3e4,autoActivate:!0,bubbleEvents:!0,fixLineEndings:!0,containerId:"global-zeroclipboard-html-bridge",containerClass:"global-zeroclipboard-container",swfObjectId:"global-zeroclipboard-flash-bridge",hoverClass:"zeroclipboard-is-hover",activeClass:"zeroclipboard-is-active",forceHandCursor:!1,title:null,zIndex:999999999},_=function(a){"object"!=typeof a||!a||"length"in a||u(a).forEach(function(b){if(/^(?:forceHandCursor|title|zIndex|bubbleEvents|fixLineEndings)$/.test(b))$[b]=a[b];else if(null==O.bridge)if("containerId"===b||"swfObjectId"===b){if(!qa(a[b]))throw new Error("The specified `"+b+"` value is not valid as an HTML4 Element ID");$[b]=a[b]}else $[b]=a[b]});{if("string"!=typeof a||!a)return A($);if(v.call($,a))return $[a]}},aa=function(){return Ya(),{browser:z(B(h,["userAgent","platform","appName","appVersion"]),{isSupported:ba()}),flash:C(O,["bridge"]),zeroclipboard:{version:$a.version,config:$a.config()}}},ba=function(){return!!(g.addEventListener&&f.Object.keys&&f.Array.prototype.map)},ca=function(){return!!(O.sandboxed||O.disabled||O.outdated||O.unavailable||O.degraded||O.deactivated)},da=function(a,d){var e,f,g,h={};if("string"==typeof a&&a?g=a.toLowerCase().split(/\s+/):"object"!=typeof a||!a||"length"in a||"undefined"!=typeof d||u(a).forEach(function(b){var c=a[b];"function"==typeof c&&$a.on(b,c)}),g&&g.length&&d){for(e=0,f=g.length;f>e;e++)a=g[e].replace(/^on/,""),h[a]=!0,Q[a]||(Q[a]=[]),Q[a].push(d);if(h.ready&&O.ready&&$a.emit({type:"ready"}),h.error){for(ba()||$a.emit({type:"error",name:"browser-unsupported"}),e=0,f=X.length;f>e;e++)if(O[X[e].replace(/^flash-/,"")]===!0){$a.emit({type:"error",name:X[e]});break}c!==b&&$a.version!==c&&$a.emit({type:"error",name:"version-mismatch",jsVersion:$a.version,swfVersion:c})}}return $a},ea=function(a,b){var c,d,e,f,g;if(0===arguments.length?f=u(Q):"string"==typeof a&&a?f=a.toLowerCase().split(/\s+/):"object"!=typeof a||!a||"length"in a||"undefined"!=typeof b||u(a).forEach(function(b){var c=a[b];"function"==typeof c&&$a.off(b,c)}),f&&f.length)for(c=0,d=f.length;d>c;c++)if(a=f[c].replace(/^on/,""),g=Q[a],g&&g.length)if(b)for(e=g.indexOf(b);-1!==e;)g.splice(e,1),e=g.indexOf(b,e);else g.length=0;return $a},fa=function(a){var b;return b="string"==typeof a&&a?A(Q[a])||null:A(Q)},ga=function(a){var b,c,d;return a=ra(a),a&&!ya(a)?"ready"===a.type&&O.overdue===!0?$a.emit({type:"error",name:"flash-overdue"}):(b=z({},a),wa.call(this,b),"copy"===a.type&&(d=Ha(R),c=d.data,S=d.formatMap),c):void 0},ha=function(){var a=$.swfPath||"",b=a.slice(0,2),c=a.slice(0,a.indexOf("://")+1);return"\\\\"===b?"file:":"//"===b||""===c?f.location.protocol:c},ia=function(){var a,b,c=O.sandboxed;return ba()?(Ya(),"boolean"!=typeof O.ready&&(O.ready=!1),void(O.sandboxed!==c&&O.sandboxed===!0?(O.ready=!1,$a.emit({type:"error",name:"flash-sandboxed"})):$a.isFlashUnusable()||null!==O.bridge||(b=ha(),b&&b!==f.location.protocol?$a.emit({type:"error",name:"flash-insecure"}):(a=$.flashLoadTimeout,"number"==typeof a&&a>=0&&(T=i(function(){"boolean"!=typeof O.deactivated&&(O.deactivated=!0),O.deactivated===!0&&$a.emit({type:"error",name:"flash-deactivated"})},a)),O.overdue=!1,Fa())))):(O.ready=!1,void $a.emit({type:"error",name:"browser-unsupported"}))},ja=function(){$a.clearData(),$a.blur(),$a.emit("destroy"),Ga(),$a.off()},ka=function(a,b){var c;if("object"==typeof a&&a&&"undefined"==typeof b)c=a,$a.clearData();else{if("string"!=typeof a||!a)return;c={},c[a]=b}for(var d in c)"string"==typeof d&&d&&v.call(c,d)&&"string"==typeof c[d]&&c[d]&&(R[d]=Xa(c[d]))},la=function(a){"undefined"==typeof a?(D(R),S=null):"string"==typeof a&&v.call(R,a)&&delete R[a]},ma=function(a){return"undefined"==typeof a?A(R):"string"==typeof a&&v.call(R,a)?R[a]:void 0},na=function(a){if(a&&1===a.nodeType){d&&(Pa(d,$.activeClass),d!==a&&Pa(d,$.hoverClass)),d=a,Oa(a,$.hoverClass);var b=a.getAttribute("title")||$.title;if("string"==typeof b&&b){var c=Da(O.bridge);c&&c.setAttribute("title",b)}var e=$.forceHandCursor===!0||"pointer"===Qa(a,"cursor");Va(e),Ua()}},oa=function(){var a=Da(O.bridge);a&&(a.removeAttribute("title"),a.style.left="0px",a.style.top="-9999px",a.style.width="1px",a.style.height="1px"),d&&(Pa(d,$.hoverClass),Pa(d,$.activeClass),d=null)},pa=function(){return d||null},qa=function(a){return"string"==typeof a&&a&&/^[A-Za-z][A-Za-z0-9_:\-\.]*$/.test(a)},ra=function(a){var b;if("string"==typeof a&&a?(b=a,a={}):"object"==typeof a&&a&&"string"==typeof a.type&&a.type&&(b=a.type),b){b=b.toLowerCase(),!a.target&&(/^(copy|aftercopy|_click)$/.test(b)||"error"===b&&"clipboard-error"===a.name)&&(a.target=e),z(a,{type:b,target:a.target||d||null,relatedTarget:a.relatedTarget||null,currentTarget:O&&O.bridge||null,timeStamp:a.timeStamp||t()||null});var c=V[a.type];return"error"===a.type&&a.name&&c&&(c=c[a.name]),c&&(a.message=c),"ready"===a.type&&z(a,{target:null,version:O.version}),"error"===a.type&&(Y.test(a.name)&&z(a,{target:null,minimumVersion:P}),Z.test(a.name)&&z(a,{version:O.version}),"flash-insecure"===a.name&&z(a,{pageProtocol:f.location.protocol,swfProtocol:ha()})),"copy"===a.type&&(a.clipboardData={setData:$a.setData,clearData:$a.clearData}),"aftercopy"===a.type&&(a=Ia(a,S)),a.target&&!a.relatedTarget&&(a.relatedTarget=sa(a.target)),ta(a)}},sa=function(a){var b=a&&a.getAttribute&&a.getAttribute("data-clipboard-target");return b?g.getElementById(b):null},ta=function(a){if(a&&/^_(?:click|mouse(?:over|out|down|up|move))$/.test(a.type)){var c=a.target,d="_mouseover"===a.type&&a.relatedTarget?a.relatedTarget:b,e="_mouseout"===a.type&&a.relatedTarget?a.relatedTarget:b,h=Ra(c),i=f.screenLeft||f.screenX||0,j=f.screenTop||f.screenY||0,k=g.body.scrollLeft+g.documentElement.scrollLeft,l=g.body.scrollTop+g.documentElement.scrollTop,m=h.left+("number"==typeof a._stageX?a._stageX:0),n=h.top+("number"==typeof a._stageY?a._stageY:0),o=m-k,p=n-l,q=i+o,r=j+p,s="number"==typeof a.movementX?a.movementX:0,t="number"==typeof a.movementY?a.movementY:0;delete a._stageX,delete a._stageY,z(a,{srcElement:c,fromElement:d,toElement:e,screenX:q,screenY:r,pageX:m,pageY:n,clientX:o,clientY:p,x:o,y:p,movementX:s,movementY:t,offsetX:0,offsetY:0,layerX:0,layerY:0})}return a},ua=function(a){var b=a&&"string"==typeof a.type&&a.type||"";return!/^(?:(?:before)?copy|destroy)$/.test(b)},va=function(a,b,c,d){d?i(function(){a.apply(b,c)},0):a.apply(b,c)},wa=function(a){if("object"==typeof a&&a&&a.type){var b=ua(a),c=Q["*"]||[],d=Q[a.type]||[],e=c.concat(d);if(e&&e.length){var g,h,i,j,k,l=this;for(g=0,h=e.length;h>g;g++)i=e[g],j=l,"string"==typeof i&&"function"==typeof f[i]&&(i=f[i]),"object"==typeof i&&i&&"function"==typeof i.handleEvent&&(j=i,i=i.handleEvent),"function"==typeof i&&(k=z({},a),va(i,j,[k],b))}return this}},xa=function(a){var b=null;return(M===!1||a&&"error"===a.type&&a.name&&-1!==W.indexOf(a.name))&&(b=!1),b},ya=function(a){var b=a.target||d||null,f="swf"===a._source;switch(delete a._source,a.type){case"error":var g="flash-sandboxed"===a.name||xa(a);"boolean"==typeof g&&(O.sandboxed=g),"browser-unsupported"===a.name?z(O,{disabled:!1,outdated:!1,unavailable:!1,degraded:!1,deactivated:!1,overdue:!1,ready:!1}):-1!==X.indexOf(a.name)?z(O,{disabled:"flash-disabled"===a.name,outdated:"flash-outdated"===a.name,insecure:"flash-insecure"===a.name,unavailable:"flash-unavailable"===a.name,degraded:"flash-degraded"===a.name,deactivated:"flash-deactivated"===a.name,overdue:"flash-overdue"===a.name,ready:!1}):"version-mismatch"===a.name&&(c=a.swfVersion,z(O,{disabled:!1,outdated:!1,insecure:!1,unavailable:!1,degraded:!1,deactivated:!1,overdue:!1,ready:!1})),Ta();break;case"ready":c=a.swfVersion;var h=O.deactivated===!0;z(O,{sandboxed:!1,disabled:!1,outdated:!1,insecure:!1,unavailable:!1,degraded:!1,deactivated:!1,overdue:h,ready:!h}),Ta();break;case"beforecopy":e=b;break;case"copy":var i,j,k=a.relatedTarget;!R["text/html"]&&!R["text/plain"]&&k&&(j=k.value||k.outerHTML||k.innerHTML)&&(i=k.value||k.textContent||k.innerText)?(a.clipboardData.clearData(),a.clipboardData.setData("text/plain",i),j!==i&&a.clipboardData.setData("text/html",j)):!R["text/plain"]&&a.target&&(i=a.target.getAttribute("data-clipboard-text"))&&(a.clipboardData.clearData(),a.clipboardData.setData("text/plain",i));break;case"aftercopy":za(a),$a.clearData(),b&&b!==Na()&&b.focus&&b.focus();break;case"_mouseover":$a.focus(b),$.bubbleEvents===!0&&f&&(b&&b!==a.relatedTarget&&!E(a.relatedTarget,b)&&Aa(z({},a,{type:"mouseenter",bubbles:!1,cancelable:!1})),Aa(z({},a,{type:"mouseover"})));break;case"_mouseout":$a.blur(),$.bubbleEvents===!0&&f&&(b&&b!==a.relatedTarget&&!E(a.relatedTarget,b)&&Aa(z({},a,{type:"mouseleave",bubbles:!1,cancelable:!1})),Aa(z({},a,{type:"mouseout"})));break;case"_mousedown":Oa(b,$.activeClass),$.bubbleEvents===!0&&f&&Aa(z({},a,{type:a.type.slice(1)}));break;case"_mouseup":Pa(b,$.activeClass),$.bubbleEvents===!0&&f&&Aa(z({},a,{type:a.type.slice(1)}));break;case"_click":e=null,$.bubbleEvents===!0&&f&&Aa(z({},a,{type:a.type.slice(1)}));break;case"_mousemove":$.bubbleEvents===!0&&f&&Aa(z({},a,{type:a.type.slice(1)}))}return/^_(?:click|mouse(?:over|out|down|up|move))$/.test(a.type)?!0:void 0},za=function(a){if(a.errors&&a.errors.length>0){var b=A(a);z(b,{type:"error",name:"clipboard-error"}),delete b.success,i(function(){$a.emit(b)},0)}},Aa=function(a){if(a&&"string"==typeof a.type&&a){var b,c=a.target||null,d=c&&c.ownerDocument||g,e={view:d.defaultView||f,canBubble:!0,cancelable:!0,detail:"click"===a.type?1:0,button:"number"==typeof a.which?a.which-1:"number"==typeof a.button?a.button:d.createEvent?0:1},h=z(e,a);c&&d.createEvent&&c.dispatchEvent&&(h=[h.type,h.canBubble,h.cancelable,h.view,h.detail,h.screenX,h.screenY,h.clientX,h.clientY,h.ctrlKey,h.altKey,h.shiftKey,h.metaKey,h.button,h.relatedTarget],b=d.createEvent("MouseEvents"),b.initMouseEvent&&(b.initMouseEvent.apply(b,h),b._source="js",c.dispatchEvent(b)))}},Ba=function(){var a=$.flashLoadTimeout;if("number"==typeof a&&a>=0){var b=Math.min(1e3,a/10),c=$.swfObjectId+"_fallbackContent";U=k(function(){var a=g.getElementById(c);Sa(a)&&(Ta(),O.deactivated=null,$a.emit({type:"error",name:"swf-not-found"}))},b)}},Ca=function(){var a=g.createElement("div");return a.id=$.containerId,a.className=$.containerClass,a.style.position="absolute",a.style.left="0px",a.style.top="-9999px",a.style.width="1px",a.style.height="1px",a.style.zIndex=""+Wa($.zIndex),a},Da=function(a){for(var b=a&&a.parentNode;b&&"OBJECT"===b.nodeName&&b.parentNode;)b=b.parentNode;return b||null},Ea=function(a){return"string"==typeof a&&a?a.replace(/["&'<>]/g,function(a){switch(a){case'"':return"&quot;";case"&":return"&amp;";case"'":return"&apos;";case"<":return"&lt;";case">":return"&gt;";default:return a}}):a},Fa=function(){var a,b=O.bridge,c=Da(b);if(!b){var d=Ma(f.location.host,$),e="never"===d?"none":"all",h=Ka(z({jsVersion:$a.version},$)),i=$.swfPath+Ja($.swfPath,$);N&&(i=Ea(i)),c=Ca();var j=g.createElement("div");c.appendChild(j),g.body.appendChild(c);var k=g.createElement("div"),l="activex"===O.pluginType;k.innerHTML='<object id="'+$.swfObjectId+'" name="'+$.swfObjectId+'" width="100%" height="100%" '+(l?'classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"':'type="application/x-shockwave-flash" data="'+i+'"')+">"+(l?'<param name="movie" value="'+i+'"/>':"")+'<param name="allowScriptAccess" value="'+d+'"/><param name="allowNetworking" value="'+e+'"/><param name="menu" value="false"/><param name="wmode" value="transparent"/><param name="flashvars" value="'+h+'"/><div id="'+$.swfObjectId+'_fallbackContent">&nbsp;</div></object>',b=k.firstChild,k=null,x(b).ZeroClipboard=$a,c.replaceChild(b,j),Ba()}return b||(b=g[$.swfObjectId],b&&(a=b.length)&&(b=b[a-1]),!b&&c&&(b=c.firstChild)),O.bridge=b||null,b},Ga=function(){var a=O.bridge;if(a){var d=Da(a);d&&("activex"===O.pluginType&&"readyState"in a?(a.style.display="none",function e(){if(4===a.readyState){for(var b in a)"function"==typeof a[b]&&(a[b]=null);a.parentNode&&a.parentNode.removeChild(a),d.parentNode&&d.parentNode.removeChild(d)}else i(e,10)}()):(a.parentNode&&a.parentNode.removeChild(a),d.parentNode&&d.parentNode.removeChild(d))),Ta(),O.ready=null,O.bridge=null,O.deactivated=null,O.insecure=null,c=b}},Ha=function(a){var b={},c={};if("object"==typeof a&&a){for(var d in a)if(d&&v.call(a,d)&&"string"==typeof a[d]&&a[d])switch(d.toLowerCase()){case"text/plain":case"text":case"air:text":case"flash:text":b.text=a[d],c.text=d;break;case"text/html":case"html":case"air:html":case"flash:html":b.html=a[d],c.html=d;break;case"application/rtf":case"text/rtf":case"rtf":case"richtext":case"air:rtf":case"flash:rtf":b.rtf=a[d],c.rtf=d}return{data:b,formatMap:c}}},Ia=function(a,b){if("object"!=typeof a||!a||"object"!=typeof b||!b)return a;var c={};for(var d in a)if(v.call(a,d))if("errors"===d){c[d]=a[d]?a[d].slice():[];for(var e=0,f=c[d].length;f>e;e++)c[d][e].format=b[c[d][e].format]}else if("success"!==d&&"data"!==d)c[d]=a[d];else{c[d]={};var g=a[d];for(var h in g)h&&v.call(g,h)&&v.call(b,h)&&(c[d][b[h]]=g[h])}return c},Ja=function(a,b){var c=null==b||b&&b.cacheBust===!0;return c?(-1===a.indexOf("?")?"?":"&")+"noCache="+t():""},Ka=function(a){var b,c,d,e,g="",h=[];if(a.trustedDomains&&("string"==typeof a.trustedDomains?e=[a.trustedDomains]:"object"==typeof a.trustedDomains&&"length"in a.trustedDomains&&(e=a.trustedDomains)),e&&e.length)for(b=0,c=e.length;c>b;b++)if(v.call(e,b)&&e[b]&&"string"==typeof e[b]){if(d=La(e[b]),!d)continue;if("*"===d){h.length=0,h.push(d);break}h.push.apply(h,[d,"//"+d,f.location.protocol+"//"+d])}return h.length&&(g+="trustedOrigins="+n(h.join(","))),a.forceEnhancedClipboard===!0&&(g+=(g?"&":"")+"forceEnhancedClipboard=true"),"string"==typeof a.swfObjectId&&a.swfObjectId&&(g+=(g?"&":"")+"swfObjectId="+n(a.swfObjectId)),"string"==typeof a.jsVersion&&a.jsVersion&&(g+=(g?"&":"")+"jsVersion="+n(a.jsVersion)),g},La=function(a){if(null==a||""===a)return null;if(a=a.replace(/^\s+|\s+$/g,""),""===a)return null;var b=a.indexOf("//");a=-1===b?a:a.slice(b+2);var c=a.indexOf("/");return a=-1===c?a:-1===b||0===c?null:a.slice(0,c),a&&".swf"===a.slice(-4).toLowerCase()?null:a||null},Ma=function(){var a=function(a){var b,c,d,e=[];if("string"==typeof a&&(a=[a]),"object"!=typeof a||!a||"number"!=typeof a.length)return e;for(b=0,c=a.length;c>b;b++)if(v.call(a,b)&&(d=La(a[b]))){if("*"===d){e.length=0,e.push("*");break}-1===e.indexOf(d)&&e.push(d)}return e};return function(b,c){var d=La(c.swfPath);null===d&&(d=b);var e=a(c.trustedDomains),f=e.length;if(f>0){if(1===f&&"*"===e[0])return"always";if(-1!==e.indexOf(b))return 1===f&&b===d?"sameDomain":"always"}return"never"}}(),Na=function(){try{return g.activeElement}catch(a){return null}},Oa=function(a,b){var c,d,e,f=[];if("string"==typeof b&&b&&(f=b.split(/\s+/)),a&&1===a.nodeType&&f.length>0){for(e=(" "+(a.className||"")+" ").replace(/[\t\r\n\f]/g," "),c=0,d=f.length;d>c;c++)-1===e.indexOf(" "+f[c]+" ")&&(e+=f[c]+" ");e=e.replace(/^\s+|\s+$/g,""),e!==a.className&&(a.className=e)}return a},Pa=function(a,b){var c,d,e,f=[];if("string"==typeof b&&b&&(f=b.split(/\s+/)),a&&1===a.nodeType&&f.length>0&&a.className){for(e=(" "+a.className+" ").replace(/[\t\r\n\f]/g," "),c=0,d=f.length;d>c;c++)e=e.replace(" "+f[c]+" "," ");e=e.replace(/^\s+|\s+$/g,""),e!==a.className&&(a.className=e)}return a},Qa=function(a,b){var c=m(a,null).getPropertyValue(b);return"cursor"!==b||c&&"auto"!==c||"A"!==a.nodeName?c:"pointer"},Ra=function(a){var b={left:0,top:0,width:0,height:0};if(a.getBoundingClientRect){var c=a.getBoundingClientRect(),d=f.pageXOffset,e=f.pageYOffset,h=g.documentElement.clientLeft||0,i=g.documentElement.clientTop||0,j=0,k=0;if("relative"===Qa(g.body,"position")){var l=g.body.getBoundingClientRect(),m=g.documentElement.getBoundingClientRect();j=l.left-m.left||0,k=l.top-m.top||0}b.left=c.left+d-h-j,b.top=c.top+e-i-k,b.width="width"in c?c.width:c.right-c.left,b.height="height"in c?c.height:c.bottom-c.top}return b},Sa=function(a){if(!a)return!1;var b=m(a,null);if(!b)return!1;var c=r(b.height)>0,d=r(b.width)>0,e=r(b.top)>=0,f=r(b.left)>=0,g=c&&d&&e&&f,h=g?null:Ra(a),i="none"!==b.display&&"collapse"!==b.visibility&&(g||!!h&&(c||h.height>0)&&(d||h.width>0)&&(e||h.top>=0)&&(f||h.left>=0));return i},Ta=function(){j(T),T=0,l(U),U=0},Ua=function(){var a;if(d&&(a=Da(O.bridge))){var b=Ra(d);z(a.style,{width:b.width+"px",height:b.height+"px",top:b.top+"px",left:b.left+"px",zIndex:""+Wa($.zIndex)})}},Va=function(a){O.ready===!0&&(O.bridge&&"function"==typeof O.bridge.setHandCursor?O.bridge.setHandCursor(a):O.ready=!1)},Wa=function(a){if(/^(?:auto|inherit)$/.test(a))return a;var b;return"number"!=typeof a||s(a)?"string"==typeof a&&(b=Wa(q(a,10))):b=a,"number"==typeof b?b:"auto"},Xa=function(a){var b=/(\r\n|\r|\n)/g;return"string"==typeof a&&$.fixLineEndings===!0&&(L()?/((^|[^\r])\n|\r([^\n]|$))/.test(a)&&(a=a.replace(b,"\r\n")):/\r/.test(a)&&(a=a.replace(b,"\n"))),a},Ya=function(b){var c,d,e,f=O.sandboxed,g=null;if(b=b===!0,M===!1)g=!1;else{try{d=a.frameElement||null}catch(h){e={name:h.name,message:h.message}}if(d&&1===d.nodeType&&"IFRAME"===d.nodeName)try{g=d.hasAttribute("sandbox")}catch(h){g=null}else{try{c=document.domain||null}catch(h){c=null}(null===c||e&&"SecurityError"===e.name&&/(^|[\s\(\[@])sandbox(es|ed|ing|[\s\.,!\)\]@]|$)/.test(e.message.toLowerCase()))&&(g=!0)}}return O.sandboxed=g,f===g||b||Za(o),g},Za=function(a){function b(a){var b=a.match(/[\d]+/g);return b.length=3,b.join(".")}function c(a){return!!a&&(a=a.toLowerCase())&&(/^(pepflashplayer\.dll|libpepflashplayer\.so|pepperflashplayer\.plugin)$/.test(a)||"chrome.plugin"===a.slice(-13))}function d(a){a&&(i=!0,a.version&&(l=b(a.version)),!l&&a.description&&(l=b(a.description)),a.filename&&(k=c(a.filename)))}var e,f,g,i=!1,j=!1,k=!1,l="";if(h.plugins&&h.plugins.length)e=h.plugins["Shockwave Flash"],d(e),h.plugins["Shockwave Flash 2.0"]&&(i=!0,l="********");else if(h.mimeTypes&&h.mimeTypes.length)g=h.mimeTypes["application/x-shockwave-flash"],e=g&&g.enabledPlugin,d(e);else if("undefined"!=typeof a){j=!0;try{f=new a("ShockwaveFlash.ShockwaveFlash.7"),i=!0,l=b(f.GetVariable("$version"))}catch(m){try{f=new a("ShockwaveFlash.ShockwaveFlash.6"),i=!0,l="6.0.21"}catch(n){try{f=new a("ShockwaveFlash.ShockwaveFlash"),i=!0,l=b(f.GetVariable("$version"))}catch(o){j=!1}}}}O.disabled=i!==!0,O.outdated=l&&r(l)<r(P),O.version=l||"0.0.0",O.pluginType=k?"pepper":j?"activex":i?"netscape":"unknown"};Za(o),Ya(!0);var $a=function(){return this instanceof $a?void("function"==typeof $a._createClient&&$a._createClient.apply(this,y(arguments))):new $a};$a.version="2.4.0-beta.1",$a.config=function(){return _.apply(this,y(arguments))},$a.state=function(){return aa.apply(this,y(arguments))},$a.isFlashUnusable=function(){return ca.apply(this,y(arguments))},$a.on=function(){return da.apply(this,y(arguments))},$a.off=function(){return ea.apply(this,y(arguments))},$a.handlers=function(){return fa.apply(this,y(arguments))},$a.emit=function(){return ga.apply(this,y(arguments))},$a.create=function(){return ia.apply(this,y(arguments))},$a.destroy=function(){return ja.apply(this,y(arguments))},$a.setData=function(){return ka.apply(this,y(arguments))},$a.clearData=function(){return la.apply(this,y(arguments))},$a.getData=function(){return ma.apply(this,y(arguments))},$a.focus=$a.activate=function(){return na.apply(this,y(arguments))},$a.blur=$a.deactivate=function(){return oa.apply(this,y(arguments))},$a.activeElement=function(){return pa.apply(this,y(arguments))};var _a=0,ab={},bb=0,cb={},db={};z($,{autoActivate:!0});var eb=function(a){var b,c=this;c.id=""+_a++,b={instance:c,elements:[],handlers:{},coreWildcardHandler:function(a){return c.emit(a)}},ab[c.id]=b,a&&c.clip(a),$a.on("*",b.coreWildcardHandler),$a.on("destroy",function(){c.destroy()}),$a.create()},fb=function(a,d){var e,f,g,h={},i=this,j=ab[i.id],k=j&&j.handlers;if(!j)throw new Error("Attempted to add new listener(s) to a destroyed ZeroClipboard client instance");if("string"==typeof a&&a?g=a.toLowerCase().split(/\s+/):"object"!=typeof a||!a||"length"in a||"undefined"!=typeof d||u(a).forEach(function(b){var c=a[b];"function"==typeof c&&i.on(b,c)}),g&&g.length&&d){for(e=0,f=g.length;f>e;e++)a=g[e].replace(/^on/,""),h[a]=!0,k[a]||(k[a]=[]),k[a].push(d);if(h.ready&&O.ready&&this.emit({type:"ready",client:this}),h.error){for(e=0,f=X.length;f>e;e++)if(O[X[e].replace(/^flash-/,"")]){this.emit({type:"error",name:X[e],client:this});break}c!==b&&$a.version!==c&&this.emit({type:"error",name:"version-mismatch",jsVersion:$a.version,swfVersion:c})}}return i},gb=function(a,b){var c,d,e,f,g,h=this,i=ab[h.id],j=i&&i.handlers;if(!j)return h;if(0===arguments.length?f=u(j):"string"==typeof a&&a?f=a.split(/\s+/):"object"!=typeof a||!a||"length"in a||"undefined"!=typeof b||u(a).forEach(function(b){var c=a[b];"function"==typeof c&&h.off(b,c)}),f&&f.length)for(c=0,d=f.length;d>c;c++)if(a=f[c].toLowerCase().replace(/^on/,""),g=j[a],g&&g.length)if(b)for(e=g.indexOf(b);-1!==e;)g.splice(e,1),e=g.indexOf(b,e);else g.length=0;return h},hb=function(a){var b=null,c=ab[this.id]&&ab[this.id].handlers;return c&&(b="string"==typeof a&&a?c[a]?c[a].slice(0):[]:A(c)),b},ib=function(a){var b,c=this;return nb.call(c,a)&&("object"==typeof a&&a&&"string"==typeof a.type&&a.type&&(a=z({},a)),b=z({},ra(a),{client:c}),ob.call(c,b)),c},jb=function(a){if(!ab[this.id])throw new Error("Attempted to clip element(s) to a destroyed ZeroClipboard client instance");a=pb(a);for(var b=0;b<a.length;b++)if(v.call(a,b)&&a[b]&&1===a[b].nodeType){a[b].zcClippingId?-1===cb[a[b].zcClippingId].indexOf(this.id)&&cb[a[b].zcClippingId].push(this.id):(a[b].zcClippingId="zcClippingId_"+bb++,cb[a[b].zcClippingId]=[this.id],$.autoActivate===!0&&qb(a[b]));var c=ab[this.id]&&ab[this.id].elements;-1===c.indexOf(a[b])&&c.push(a[b])}return this},kb=function(a){var b=ab[this.id];if(!b)return this;var c,d=b.elements;a="undefined"==typeof a?d.slice(0):pb(a);for(var e=a.length;e--;)if(v.call(a,e)&&a[e]&&1===a[e].nodeType){for(c=0;-1!==(c=d.indexOf(a[e],c));)d.splice(c,1);var f=cb[a[e].zcClippingId];if(f){for(c=0;-1!==(c=f.indexOf(this.id,c));)f.splice(c,1);0===f.length&&($.autoActivate===!0&&rb(a[e]),delete a[e].zcClippingId)}}return this},lb=function(){var a=ab[this.id];return a&&a.elements?a.elements.slice(0):[]},mb=function(){var a=ab[this.id];a&&(this.unclip(),this.off(),$a.off("*",a.coreWildcardHandler),delete ab[this.id])},nb=function(a){if(!a||!a.type)return!1;if(a.client&&a.client!==this)return!1;var b=ab[this.id],c=b&&b.elements,d=!!c&&c.length>0,e=!a.target||d&&-1!==c.indexOf(a.target),f=a.relatedTarget&&d&&-1!==c.indexOf(a.relatedTarget),g=a.client&&a.client===this;return b&&(e||f||g)?!0:!1},ob=function(a){var b=ab[this.id];if("object"==typeof a&&a&&a.type&&b){var c=ua(a),d=b&&b.handlers["*"]||[],e=b&&b.handlers[a.type]||[],g=d.concat(e);if(g&&g.length){var h,i,j,k,l,m=this;for(h=0,i=g.length;i>h;h++)j=g[h],k=m,"string"==typeof j&&"function"==typeof f[j]&&(j=f[j]),"object"==typeof j&&j&&"function"==typeof j.handleEvent&&(k=j,j=j.handleEvent),"function"==typeof j&&(l=z({},a),va(j,k,[l],c))}}},pb=function(a){return"string"==typeof a&&(a=[]),"number"!=typeof a.length?[a]:a},qb=function(a){if(a&&1===a.nodeType){var b=function(a){(a||(a=f.event))&&("js"!==a._source&&(a.stopImmediatePropagation(),a.preventDefault()),delete a._source)},c=function(c){(c||(c=f.event))&&(b(c),$a.focus(a))};a.addEventListener("mouseover",c,!1),a.addEventListener("mouseout",b,!1),a.addEventListener("mouseenter",b,!1),a.addEventListener("mouseleave",b,!1),a.addEventListener("mousemove",b,!1),db[a.zcClippingId]={mouseover:c,mouseout:b,mouseenter:b,mouseleave:b,mousemove:b}}},rb=function(a){if(a&&1===a.nodeType){var b=db[a.zcClippingId];if("object"==typeof b&&b){for(var c,d,e=["move","leave","enter","out","over"],f=0,g=e.length;g>f;f++)c="mouse"+e[f],d=b[c],"function"==typeof d&&a.removeEventListener(c,d,!1);delete db[a.zcClippingId]}}};$a._createClient=function(){eb.apply(this,y(arguments))},$a.prototype.on=function(){return fb.apply(this,y(arguments))},$a.prototype.off=function(){return gb.apply(this,y(arguments))},$a.prototype.handlers=function(){return hb.apply(this,y(arguments))},$a.prototype.emit=function(){return ib.apply(this,y(arguments))},$a.prototype.clip=function(){return jb.apply(this,y(arguments))},$a.prototype.unclip=function(){return kb.apply(this,y(arguments))},$a.prototype.elements=function(){return lb.apply(this,y(arguments))},$a.prototype.destroy=function(){return mb.apply(this,y(arguments))},$a.prototype.setText=function(a){if(!ab[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return $a.setData("text/plain",a),this},$a.prototype.setHtml=function(a){if(!ab[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return $a.setData("text/html",a),this},$a.prototype.setRichText=function(a){if(!ab[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return $a.setData("application/rtf",a),this},$a.prototype.setData=function(){if(!ab[this.id])throw new Error("Attempted to set pending clipboard data from a destroyed ZeroClipboard client instance");return $a.setData.apply(this,y(arguments)),this},$a.prototype.clearData=function(){if(!ab[this.id])throw new Error("Attempted to clear pending clipboard data from a destroyed ZeroClipboard client instance");return $a.clearData.apply(this,y(arguments)),this},$a.prototype.getData=function(){if(!ab[this.id])throw new Error("Attempted to get pending clipboard data from a destroyed ZeroClipboard client instance");return $a.getData.apply(this,y(arguments))},"function"==typeof define&&define.amd?define(function(){return $a}):"object"==typeof module&&module&&"object"==typeof module.exports&&module.exports?module.exports=$a:a.ZeroClipboard=$a}(function(){return this||window}());
//# sourceMappingURL=ZeroClipboard.min.map