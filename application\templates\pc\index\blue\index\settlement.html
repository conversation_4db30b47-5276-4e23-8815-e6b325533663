<!DOCTYPE html>
<html lang="zh">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
    <link rel="stylesheet" href="__RES__/theme/blue/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/style.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/response.css">
    <style>
        .page-link {padding: 0;}
        .page-item .page-link .bx-chevron-left,.page-item .page-link .bx-chevron-right{font-style:normal}
        .page-item .page-link .bx-chevron-left:before {content: "<";}
        .page-item .page-link .bx-chevron-right:before{content: ">";}
    </style>
</head>
<body>
<header class="header help-header">
    <div class="bgimg"><img src="__RES__/theme/blue/picture/header_bg.png" alt=""></div>
    <div class="container">
     {include file="./default_header"}
        <div class="banner">
            <div class="text-introduce">
                <div class="h1">结算公告</div>
                <div class="p">行业新闻、公告通知、发卡帮自动发卡平台咨询您在这里都能看到！</div>
            </div>
            <div class="img img1"><img src="__RES__/theme/blue/picture/help_banner_img1.png" alt=""></div>
            <div class="img img2"><img src="__RES__/theme/blue/picture/help_banner_img2.png" alt=""></div>
        </div>
    </div>
</header>
<div class="helpbox">
    <div class="container">
        <ul class="nav nav-tabs">
            <li class="nav-item"><a href="/company/notice" class="nav-link  ">公告通知</a></li>
            <li class="nav-item"><a href="/company/settlement" class="nav-link active">结算公告</a></li>
            <li class="nav-item"><a href="/company/news" class="nav-link ">行业新闻</a></li>
            <li class="nav-item"><a href="/company/faq" class="nav-link ">常见问题</a></li>
        </ul>
        <div class="tab-content">
            <div class="tab-pane active">
                <div class="acticle-other">
                	                   
                	                   
                	                   
                	                   
                	                   {foreach $articles as $v}
                	                    <div class="d-flex item">
                        <div class="date">
                            <div class="h1">结算</div>
                            <div class="h2">{:date('m-d',$v.create_at)}</div>
                        </div>
                        <dl>
                            <dt><a href="/article/{$v.id}.html">{$v.title}</a></dt>
                            <dd onclick="window.location.href='/article/{$v.id}.html'">{$v.content|htmlspecialchars_decode|removeXSS}</dd>
                        </dl>
                        <a href="/article/{$v.id}.html" class="more"><img src="__RES__/theme/blue/picture/more_icon.png" alt=""></a>
                    </div>                    
                          {/foreach}       
                    
                    
                    
                    
                    
                    
                    </div>
                           
            </div>
        </div>
    </div>
</div>
{include file="./default_footer"}

</body>
</html>