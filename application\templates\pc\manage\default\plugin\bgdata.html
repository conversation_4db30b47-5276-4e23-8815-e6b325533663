{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >


    <div class="form-group">
        <label class="col-sm-2 control-label">后台端数据可视化</label>
        <div class='col-sm-8'>
            <select name="admin_bgdata" class="layui-input" disabled="disabled" style="background: #e9e9e9">
                <option value="0" {if plugconf('bgdata','admin_bgdata')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('bgdata','admin_bgdata')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">后台端此功能不可关闭</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">商户端数据可视化</label>
        <div class='col-sm-8'>
            <select name="merchant_bgdata" class="layui-input" >
                <option value="0" {if plugconf('bgdata','merchant_bgdata')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('bgdata','merchant_bgdata')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">开启之后商户端将可使用该功能</p>
        </div>
    </div>

    <div class="hr-line-dashed"></div>


    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>

</form>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
{/block}