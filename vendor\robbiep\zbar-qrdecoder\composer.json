{"name": "robbiep/zbar-qrdecoder", "description": "A PHP wrapper for Zbar. Decodes images/photos containing QR codes.", "keywords": ["zbar", "qr", "qrcode", "decode", "php", "laravel"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.5.0", "symfony/process": "^3.0"}, "require-dev": {"phpunit/phpunit": "4.3.5", "mockery/mockery": "0.9.*@dev"}, "autoload": {"psr-0": {"RobbieP\\ZbarQrdecoder\\": "src/"}}, "minimum-stability": "stable"}