<!-- Loader-->
<div id="preloader">
    <div id="status">
        <div class="spinner">
            <div class="double-bounce1"></div>
            <div class="double-bounce2"></div>
        </div>
    </div>
</div> 
<!-- Loader -->

<!-- Navbar STart -->
<header id="topnav" class="defaultscroll sticky">
    <div class="container">
        <!-- Logo container-->
        <div>
            <a class="logo" href="/">
                <img src="{:sysconf('site_logo')}" height="24" alt="">
            </a>
        </div>                 
        <div class="buy-button">
            {if session('?merchant.user')}
            <a href="/merchant">
                <div class="btn btn-primary login-btn-primary"  style="font-size: 13px;padding: 6px 20px;">商户中心</div>
                <div class="btn btn-light login-btn-light"  style="font-size: 13px;padding: 6px 20px;">商户中心</div>
            </a>
            {else}<a href="/login">
                <div class="btn btn-primary login-btn-primary"  style="font-size: 13px;padding: 6px 20px;">登录 / 注册</div>
                <div class="btn btn-light login-btn-light"  style="font-size: 13px;padding: 6px 20px;">登录 / 注册</div></a>
            {/if}
        </div><!--end login button-->
        <!-- End Logo container-->
        <div class="menu-extras">
            <div class="menu-item">
                <!-- Mobile menu toggle-->
                <a class="navbar-toggle">
                    <div class="lines">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </a>
                <!-- End mobile menu toggle-->
            </div>
        </div>

        <div id="navigation">
            <ul class="navigation-menu">
                {volist name="nav" id="vo"}
                <li><a href="{$vo.url}" {if $vo.target==1}target="_blank"{/if}>{$vo.title}</a></li>
                {/volist}
            </ul>
            <div class="buy-menu-btn d-none">
                {if session('?merchant.user')}
                <a href="/merchant" class="btn btn-primary">商户中心</a>
                {else}
                <a href="/login" class="btn btn-primary">登录 / 注册</a>
                {/if}
            </div><!--end login button-->
        </div><!--end navigation-->
    </div><!--end container-->
</header><!--end header-->
<!-- Navbar End -->
