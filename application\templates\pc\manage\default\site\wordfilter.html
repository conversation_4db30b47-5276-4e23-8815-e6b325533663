{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' style='padding-top:20px'>

    <div class="form-group">
        <label class="col-sm-2 control-label">过滤开关</label>
        <div class='col-sm-8'>
            <select name="site_wordfilter_status" class="layui-input" required>
                <option value="0" {if sysconf('site_wordfilter_status')==0}selected{/if}>关闭过滤</option>
                <option value="1" {if sysconf('site_wordfilter_status')==1}selected{/if}>开启过滤</option>
            </select>
            <p class="help-block">过滤开关</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">危险关键字</label>
        <div class='col-sm-8'>
            <textarea name="site_wordfilter_danger" id="" cols="30" rows="5" class="layui-textarea">{:sysconf('site_wordfilter_danger')}</textarea>
            <p class="help-block">危险关键字，使用“|”分隔</p>
        </div>
    </div>

    <div class="form-group">
        <div class="col-sm-4 col-sm-offset-2">
            <div class="layui-form-item text-center">
                <button class="layui-btn" type="submit">保存配置</button>
            </div>
        </div>
    </div>

</form>

<div class="hr-line-dashed"></div>

<form onsubmit="return false;" action="{:url('wordreplace')}" data-auto="true" method="post" class='form-horizontal' style='padding-top:20px'>
    <div class="form-group">
        <label class="col-sm-2 control-label">要替换的字符串</label>
        <div class='col-sm-2'>
            <input type="text"  name="source"  autocomplete="off" class="layui-input required" >
        </div>
        <label class="col-sm-2 control-label">替换后的字符串</label>
        <div class='col-sm-2'>
            <input type="text"  name="target"  autocomplete="off" class="layui-input required">
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">数据类型</label>
        <div class='col-sm-8'>
            <input type="checkbox" name="data_type[]" title="" value="1">商品分类名
            <input type="checkbox" name="data_type[]" title="" value="2">商品名
            <input type="checkbox" name="data_type[]" title="" value="3">商品说明
            <input type="checkbox" name="data_type[]" title="" value="4">使用说明
        </div>
    </div>

    <div class="form-group">
        <div class="col-sm-4 col-sm-offset-2">
            <div class="layui-form-item text-center">
                <button class="layui-btn" type="submit">立即替换</button>
            </div>
        </div>
    </div>

</form>
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
{/block}
