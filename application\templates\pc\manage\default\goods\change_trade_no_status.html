{extend name='./content'}
{block name='content'}
<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" data-auto="true" method="post" style="padding: 50px;">
    <div class="layui-form-item layui-block">
        <label class="layui-form-label">订单生成方式</label>
        <div class="layui-input-inline">
            <select class="layui-form-item layui-inline change1" name="order_trade_no_type" style="display: block;height: 32px;">
                <option value="0" {if sysconf('order_trade_no_type') == 0}selected=""{/if}>系统默认</option>
                <option value="1" {if sysconf('order_trade_no_type') == 1}selected=""{/if}>用户id+时间+顺序号</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item layui-block profix" {if sysconf('order_trade_no_type') == 0}style="display:none;"{/if}>
         <label class="layui-form-label">订单号前缀</label>
        <div class="layui-input-inline">
            <input name="order_trade_no_profix" value="{:sysconf('order_trade_no_profix')}" placeholder="请输入订单号前缀" class="layui-input">
            <span style="color:red;">* 注意前缀不能超过 3 位</span>
        </div>
    </div>

    <div class="hr-line-dashed"></div>
    <div class="layui-form-item layui-block">
        <label class="layui-form-label">订单标题生成方式</label>
        <div class="layui-input-inline">
            <select class="layui-form-item layui-inline change2" name="order_title_type" style="display: block;height: 32px;">
                <option value="0" {if sysconf('order_title_type') == 0}selected=""{/if}>商品名称</option>
                <option value="1" {if sysconf('order_title_type') == 1}selected=""{/if}>订单编号</option>
                <option value="2" {if sysconf('order_title_type') == 2}selected=""{/if}>前缀+订单编号</option>
                <option value="3" {if sysconf('order_title_type') == 3}selected=""{/if}>自定义</option>
                <option value="4" {if sysconf('order_title_type') == 4}selected=""{/if}>订单编号+后缀</option>

            </select>
        </div>
    </div>

    <div class="layui-form-item layui-block order_title2" {if sysconf('order_title_type') != 2}style="display:none;"{/if}>
         <label class="layui-form-label">标题前缀</label>
        <div class="layui-input-inline">
            <input name="order_title_profix_l" value="{:sysconf('order_title_profix')}" placeholder="请输入标题前缀" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-block order_title4" {if sysconf('order_title_type') != 4}style="display:none;"{/if}>
         <label class="layui-form-label">标题后缀</label>
        <div class="layui-input-inline">
            <input name="order_title_profix_r" value="{:sysconf('order_title_profix')}" placeholder="请输入标题后缀" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-block order_title3" {if sysconf('order_title_type') != 3}style="display:none;"{/if}>
         <label class="layui-form-label">自定义标题</label>
        <div class="layui-input-inline">
            <input name="order_title_str" value="{:sysconf('order_title_str')}" placeholder="请输入自定义标题" class="layui-input">
        </div>
    </div>

    <div class="hr-line-dashed"></div>
    <div class="layui-form-item layui-block">
        <label class="layui-form-label">开启历史显示</label>
        <div class="layui-input-inline">
            <select class="layui-form-item layui-inline" name="order_show_history" style="display: block;height: 32px;">
                <option value="0" {if sysconf('order_show_history') == 0}selected=""{/if}>关闭</option>
                <option value="1" {if sysconf('order_show_history') == 1}selected=""{/if}>开启</option>
            </select>
            <span style="color:red;">* 买家查单时将会在显示历史商品名和使用说明（注意只有商品名、使用说明产生变化才会显示）</span>

        </div>
    </div>


    <div class="hr-line-dashed"></div>
    <div class="layui-form-item layui-inline">
        <button class="layui-btn layui-btn-primary">确定</button>
    </div>
</form>
<script>
    $('.change1').change(function () {
        if ($(this).val() == 0) {
            $('.profix').hide();
        } else {
            $('.profix').show();
        }
    });
    $('.change2').change(function () {
        $('.order_title2').hide();
        $('.order_title3').hide();
        $('.order_title4').hide();
        if ($(this).val() == 2) {
            $('.order_title2').show();
        }
        if ($(this).val() == 3) {
            $('.order_title3').show();
        }
        if ($(this).val() == 4) {
            $('.order_title4').show();
        }
    });
</script>
{/block}
