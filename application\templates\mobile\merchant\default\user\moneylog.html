{extend name="simple_base"}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">
            <!-- Search Form Wrapper-->

            <div class="row">
                <div class="col-sm-12">
                    <div class="alert alert-primary alert-dismissable">
                        只保留显示最近30天的流水日志
                    </div>
                </div>
            </div>

            {foreach $logs as $v}

            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="d-flex align-items-center justify-content-between">
                    <h6 class="text-truncate mb-0 float-left d-inline">{$v.create_at|date="Y-m-d H:i:s",###}</h6>

                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>变动金额：</span>
                        <span>
                            {if $v.money>0}
                            <span class="text-success">{$v.money}</span>
                            {else/}
                            <span class="text-danger">{$v.money}</span>
                            {/if}
                        </span>
                    </div>
                    <div class="order-wrap-text">
                        <span>变动后账户余额：</span>
                        <span>{$v.balance}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>变动后冻结余额：</span>
                        <span>{$v.freeze_money}</span>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <p class='mb-0'>{$v.reason}</p>
                </div>

            </div>

            {/foreach}

            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}


