<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8">
        <title>{:sysconf('site_name')} - {:sysconf('site_subtitle')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="{:sysconf('site_keywords')}" />
        <meta name="description" content="{:sysconf('site_desc')}" />
        <link rel="shortcut icon" href="{:sysconf('browser_icon')}" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta name="theme-color" content="#1E78FF">	
		<meta name="msapplication-navbutton-color" content="#1E78FF">		
		<meta name="apple-mobile-web-app-status-bar-style" content="#1E78FF">
		<link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/style.css" media="all">		
		<link rel="stylesheet" type="text/css" href="__RES__/theme/maowang51/css/responsive.css" media="all">	
		
		
	</head>

	<body>
		<div class="main-page-wrapper">
			
			{include file="./default_her"}


			<div class="offcanvas offcanvas-top theme-search-form justify-content-center" tabindex="-1" id="offcanvasTop">
				<button type="button" class="close-btn tran3s" data-bs-dismiss="offcanvas" aria-label="Close"><i class="bi bi-x-lg"></i></button>
				<div class="form-wrapper">
					<form action="orderquery">
						<input type="text" name="orderid" placeholder="请输入您的订单号...">
						<input type="hidden" name="querytype" value="3">      
						<button class="btn-eight">开始查询</button>
					</form>
				</div> <!-- /.form-wrapper -->
			</div>


			
			<header class="theme-main-menu sticky-menu theme-menu-two">
				{include file="./default_header"}
			</header>
			<div class="hero-banner-four">
				<div class="container">
					<div class="row">
						<div class="col-xl-8 col-xl-7 col-lg-8 col-md-11 m-auto">
							<a href="#" class="slogan"><strong> 投 诉 订 单  </strong> ----轻松查询订单，即刻享受卡密自动交易 <i class="fas fa-chevron-right"></i></a>
				
							<p class="mb-50 lg-mb-30"></p>
							
						
							
							
							
							
							
						</div>
					</div>
				</div> <!-- /.container -->

				<div class="illustration-holder-oneee">
					<img src="__RES__/theme/maowang51/picture/ils_10.svg" alt="">
					<img src="__RES__/theme/maowang51/picture/ils_10_1.svg" alt="" class="shapes shape-one">
					<img src="__RES__/theme/maowang51/picture/ils_10_2.svg" alt="" class="shapes shape-two">
				</div> <!-- /.illustration-holder-one -->
				<div class="illustration-holder-tee">
					<img src="__RES__/theme/maowang51/picture/ils_11.svg" alt="">
					<img src="__RES__/theme/maowang51/picture/ils_10_1.svg" alt="" class="shapes shape-one">
				</div>
			</div> 




				<div class="container">
				    
				    <div class="user-data-form mt-60 lg-mt-40">
					

					<div class="row">
				

		
          
                
                        


                           
                                <div class="component-wrapper rounded shadow">
                                    <div class="p-4 border-bottom text-center">
                                        <h5 class="mb-0"> 投 诉 订 单 </h5>
                                    </div>

                                    <div class="p-4">
                                        <form name='report'  action='' method='post' enctype="multipart/form-data">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>订单编号</label>
                                                        <i data-feather="user" class="fea icon-sm icons"></i>
                                                        <input name="trade_no" type="text" value="{$Think.get.trade_no|htmlentities}" class="form-control pl-5">
                                                    </div>
                                                </div><!--end col-->
                                                <div class="col-md-6">
                                                    <div class="form-group position-relative">
                                                        <label>举报原因</label>
                                                        <select name="type" class="form-control custom-select">
                                                            <option value="无效卡密">无效卡密</option>
                                                            <option value="虚假商品">虚假商品</option>
                                                            <option value="非法商品">非法商品</option>
                                                            <option value="侵权商品">侵权商品</option>
                                                            <option value="不能购买">不能购买</option>
                                                            <option value="恐怖色情">恐怖色情</option>
                                                            <option value="其他投诉">其他投诉</option>
                                                        </select>
                                                    </div> 
                                                </div><!--end col-->
                                                <div class="col-md-6"><br>
                                                    <div class="form-group position-relative">
                                                        <label>联系QQ</label>
                                                        <i data-feather="slack" class="fea icon-sm icons"></i>
                                                        <input name="qq" type="text" placeholder="先将常用QQ设置为任何人可以添加，卖家会主动联系你解决" class="form-control pl-5">
                                                    </div> 
                                                </div><!--end col-->
                                                <div class="col-md-6"><br>
                                                    <div class="form-group position-relative">
                                                        <label>手机号码</label>
                                                        <i data-feather="at-sign" class="fea icon-sm icons"></i>
                                                        <input  name="mobile" type="text" placeholder="用于接收撤诉查看进度短信密码，填错将无法查看投诉处理进度，后果自负" class="form-control pl-5">
                                                    </div>
                                                </div>

                                                {if sysconf('complaint_qrcode')==1}
                                                <div class="col-md-6"><br>
                                                    <div class="form-group position-relative">
                                                        <label>收款二维码</label>
                                                        <input  name="buyer_qrcode" type="file" placeholder="用于如果胜诉将会把资金退回此收款账号" class="form-control">
                                                    </div>
                                                </div>
                                                {/if}

                                                <div class="col-md-6"><br>
                                                    <div class="form-group position-relative">
                                                        <label>选择售后卡密</label>
                                                        <input name="select_cards" type="hidden" value="">
                                                        <input readonly="readonly" name="select_text" type="text" placeholder="请选择售后卡密" class="form-control">
                                                        <button id="selectBtn" style="position: absolute;top: 43px;right: 18px;" class="btn btn-primary btn-sm">选择卡密</button>
                                                    </div>
                                                </div>

                                                <div class="col-md-12"><br>
                                                    <div class="form-group position-relative">
                                                        <label>详情说明</label>
                                                        <i data-feather="message-circle" class="fea icon-sm icons"></i>
                                                        <textarea name="desc" rows="4" class="form-control pl-5" placeholder="#### 必须输入*********订单有问题请第一时间联系卡密页面的卖家客服QQ，卖家客服QQ长时间不回复联系卡密下方的平台客服QQ，平台为24点解冻卖家资金，卡密有问题的请在24点前点击投诉按钮，否则我们将打款给卖家。防骗提醒：1.卡密内容为联系QQ的 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。以上问题请在此处投诉订单。（卖家会在24小时内联系你解决，超过24小时没联系解决的请联系平台客服QQ退款"></textarea>
                                                    </div>
                                                </div>
                                                
                                             
                                                
                                            </div><!--end row-->
                                            <div class="row">

                                                <div class="col-sm-12">
                                                    <input type="hidden" name="token" value="{$token|htmlentities}">
                                                    <button type="submit" class="btn-eight w-100 mt-50 mb-40 lg-mt-30 lg-mb-30">提交投诉</button>

                                                </div><!--end col-->
                                            </div><!--end row-->
                                            
                                           
                                            
                                            
                                        </form><!--end form-->
                                    </div>
                                </div>
                           
                            <!-- Forms End -->

                       
                  
						
						
					</div>
					</div>  
					
				</div>  




			<div class="fancy-short-banner-three position-relative mt-160 lg-mt-80">
				<div class="container">
					<div class="bg-wrapper">
						<div class="row align-items-center">
							<div class="col-lg-8 m-auto" data-aos="fade-up">
								<div class="title-style-one text-center white-vr mb-30" data-aos="fade-up">
									<h2 class="main-title">入驻我们，即刻赚钱 <br>24小时监控订单资金无忧</h2>
								</div> <!-- /.title-style-one -->
								<a href="contact-us.html" class="btn-six ripple-btn">立即入驻，成为商户 <i class="fas fa-chevron-right"></i></a>
							</div>
						</div>
					</div> <!-- /.bg-wrapper -->
				</div> <!-- /.container -->
			</div> <!-- /.fancy-short-banner-three -->


		

			<!--
			=====================================================
				Footer
			=====================================================
			-->
			<div class="footer-style-one theme-basic-footer">
				<div class="container">
					<div class="inner-wrapper">
					
					
					
						
{include file="./default_footer"}
						
					</div> <!-- /.inner-wrapper -->
				</div>
			</div> <!-- /.footer-style-one -->


			<button class="scroll-top">
				<i class="bi bi-arrow-up-short"></i>
			</button>
			
			


		<!-- Optional JavaScript _____________________________  -->

    	<!-- jQuery first, then Bootstrap JS -->
    	<!-- jQuery -->
		<script src="__RES__/theme/maowang51/js/jquery.min.js"></script>
		<!-- Bootstrap JS -->
		<script src="__RES__/theme/maowang51/js/bootstrap.bundle.min.js"></script>
		<!-- AOS js -->
		<script src="__RES__/theme/maowang51/js/aos.js"></script>
		<!-- Slick Slider -->
		<script src="__RES__/theme/maowang51/js/slick.min.js"></script>
		<!-- js Counter -->
		<script src="__RES__/theme/maowang51/js/jquery.counterup.min.js"></script>
		<script src="__RES__/theme/maowang51/js/jquery.waypoints.min.js"></script>
		<!-- Fancybox -->
		<script src="__RES__/theme/maowang51/js/jquery.fancybox.min.js"></script>
		<!-- Progress Bar js -->
		<script src="__RES__/theme/maowang51/js/jquery.skills.js"></script>

		<!-- Theme js -->
		<script src="__RES__/theme/maowang51/js/theme.js"></script>
		
		<script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="/static/app/js/layer.js"></script>
        <script>

            var select_card_form;

            function closeSelectForm()
            {
                layer.close(select_card_form);
            }

            $("#selectBtn").click(function ()
            {
                var trade_no='';
                if('{$Think.get.trade_no|htmlentities}'!='')
                {
                    trade_no='{$Think.get.trade_no|htmlentities}';
                }else if($("input[name='trade_no']").val()!='')
                {
                    trade_no=$("input[name='trade_no']").val();
                }
                select_card_form = layer.open({
                    type: 2,
                    fix: false,
                    maxmin: true,
                    shadeClose: false,
                    area: ['420px', 'auto'],
                    shade: 0.4,
                    title: "请选择需要售后的卡密",
                    content: '/index/plugin/complaintCard?trade_no=' +trade_no,
                    success: function (layero, index) {
                        layer.iframeAuto(index);
                    }
                });
                return false;
            });

            var select_lable;
            function selectLable(ids, num)
            {
                $("[name=select_cards]").val(ids);
                $("[name=select_text]").val("已选择" + num + "张");
            }
        </script>
	
        
		
		
		
		
		
		
		
		
		
		</div> <!-- /.main-page-wrapper -->
	</body>
</html>