{extend name="./content"}

{block name="content"}
<form onsubmit="return false;" action="__SELF__" data-auto="true" method="post" class='form-horizontal' >
    <div class="form-group">
        <label class="col-sm-2 control-label">功能是否开启</label>
        <div class='col-sm-8'>
            <select name="status" class="layui-input" >
                <option value="0" {if plugconf('cardstools','status')=='0'}selected{/if}>关闭</option>
                <option value="1" {if plugconf('cardstools','status')=='1'}selected{/if}>开启</option>
            </select>
            <p class="help-block">开启之后可使用该功能</p>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">卡密工具展示页：</label>
        <div class='col-sm-8'>
            <input type="text"  autocomplete="off" class="layui-input" disabled="disabled" style="background: #e9e9e9" value="{:url('index/plugin/cardstools')}">
            <p class="help-block">可以添加在前台导航展示（系统管理->系统配置->前台导航）</p>
        </div>
    </div>


    <div class="col-sm-12">
        <div class="layui-form-item text-center">
            <button class="layui-btn" type="submit">保存配置</button>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label"></label>
        <div class='col-sm-8'>
            <div class="alert alert-success alert-dismissible" role="alert" style="border-radius:0">
                <p style="font-size:13px">此工具由“初心、陌路つ”QQ834215121赞助！</p>
            </div>
        </div>
    </div>


</form>



<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });


</script>
{/block}