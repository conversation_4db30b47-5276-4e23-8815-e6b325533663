{extend name="base"}

{block name="content"}

<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-flex align-items-center justify-content-between">
                    <h4 class="mb-0 font-size-18">{$_title}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">后台管理</a></li>
                            <li class="breadcrumb-item active">{$_title}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">


            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">

                        <div class="row">

                            <div class="col-lg-12">
                                <h4 class="card-title mb-4">平台设置</h4>

                                <form id="form1" class="form-horizontal" role="form" action="" method="post">
                                    <input type="hidden" name="act" value="edit"/> 
                                    <div class="form-group row ">
                                        <label for="status" class="col-md-2 col-form-label">是否在售卡页开启</label>
                                        <div class="col-md-10 d-flex align-items-center">
                                            <div class="custom-control custom-radio custom-control-inline mr-4">
                                                <input {if $chat.status==0}checked{/if} value="0" type="radio" id="status2" name="status" class="custom-control-input">
                                                    <label class="custom-control-label" for="status2">关闭</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline mr-4">
                                                <input {if $chat.status==1}checked{/if} value="1" type="radio" id="status1" name="status" class="custom-control-input">
                                                    <label class="custom-control-label" for="status1">开启</label>
                                            </div>
                                            <button type="button" onclick="save()" class="btn btn-sm btn-primary waves-effect waves-light"><i class="bx bx-check-square mr-1"></i>保存设置</button>
                                        </div>
                                    </div>
                                </form>

                            </div>




                        </div>

                    </div>
                </div>

            </div>

            {if $chat.status==1}
            <div class="col-lg-6">
                <div class="card mini-stats-wid">
                    <div class="card-body">

                        <div class="media">
                            <div class="media-body">
                                <p class="text-muted font-weight-medium">客服后台登录</p>
                                <h6>登录地址：<a target='_blank' href='{$domain}/user/login'>{$domain}/user/login</a></h6>
                                <h6>
                                    用户名：{$chat.username}  默认密码：123456（登录后请及时修改密码）
                                </h6>
                            </div>
                            <div class="align-self-center">

                                <a target='_blank' href="{:url('plugin/chatUserLogin')}" class="btn btn-light waves-effect waves-light text-primary">一键登录</a>
                                <a target='_blank' href="{:url('plugin/chatUserData')}" class="btn btn-light waves-effect waves-light text-primary">数据看板</a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-lg-6">
                <div class="card mini-stats-wid">
                    <div class="card-body">
                        <div class="media">
                            <div class="media-body">
                                <p class="text-muted font-weight-medium">客服坐席登录</p>
                                <h6>登录地址：<a target='_blank' href='{$domain}/work/login'>{$domain}/work/login</a></h6>
                                <h6>
                                    请先登录客服后台设置客服坐席账号、密码
                                </h6>
                            </div>
                            <div class="align-self-center">
                                <a target='_blank' href="{:url('plugin/chatWork')}" class="btn btn-light waves-effect waves-light text-primary">一键登录坐席</a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            {/if}
        </div>
    </div>

</div>
<!-- container-fluid -->

<!-- End Page-content -->


{/block}
{block name="js"}
<script>

    function save()
    {
        var loading = layer.load(1, {shade: [0.1, '#fff']});


        $.ajax({
            url: "{:url('plugin/chat')}",
            type: 'post',
            data: $('#form1').serialize(),
            success: function (res) {
                layer.close(loading);
                if (res.code == 1) {
                    location.reload();
                }
            }
        });

        return false;
    }

</script>
{/block}
