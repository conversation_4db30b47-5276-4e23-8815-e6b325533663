<!doctype html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>用户登录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__RES__/theme/blue/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/iview.css">
    <script src="__RES__/theme/blue/js/vue.min.js"></script>
    <script src="__RES__/theme/blue/js/iview.min.js"></script>
    <link rel="stylesheet" href="__RES__/theme/blue/css/login.css">
    <style>
        .btn-login {cursor: pointer;}
        .logo-login, .yzm-img {cursor: pointer;}
        .buy_code {margin-top: 10px;box-sizing: border-box;text-align: right;display: flex;align-items: center;justify-content: flex-end;}
        .buy_code a {background-image: linear-gradient(#3476fe, #3476fe8f);color: #fff;padding: 5px;box-sizing: border-box;border-radius: 3px;transition: all .3s ease-in-out;}
        .qrcode-box {text-align: center;width: 250px;height: 250px;margin: 0 auto;}
        .qrcode-box img {max-width: 100%;}
        @media (min-width: 992px) {.login-reg .item {margin-bottom: 20px}.logo-login img {width: 328px;}};
        .titlel {align-content: center;}
    </style>
</head>
<body class="login merchant">
<div class="merchantbg1"><img src="/static/theme/blue/images/bg_merchant1.png" alt=""></div>
<div class="merchantbg2"><img src="/static/theme/blue/images/bg_merchant2.png" alt=""></div>
<div class="header">
    <div class="logo-login" onclick="location.href='/'">
        <img src="{:sysconf('site_logo')}" alt="">
    </div>
</div>
<div class="container login-box" id="login">
    <div class="row">
        <div class="col-lg-6">
            <div class="login-leftbox d-none d-lg-block">
                <div class="img"><img src="__RES__/theme/blue/picture/bg_login2.png" alt=""></div>
                <dl>
                    <dt>商户保障</dt>
                    <dd>商户的商品，全部加密处理，专业运维24小时处理，<br>您的帐户安全将得到充分的保障。</dd>
                </dl>
            </div>
        </div>
        <div class="login-form" v-if="is_login">
            <h1>商户登录</h1>
            <form class="login-form mt-4" role="form" method="post" action="/login/userlogin">
                <input type="hidden" name="__token__" value="{$Request.token|htmlentities}" />
                <div class="row">
                    <div class="col-lg-12">
                        <div class="item">
                            <div class="icon"><img src="__RES__/theme/blue/picture/icon_merchant1.png" alt=""></div>
                            <input class="form-control" placeholder="请输入用户名" type="text" name="username" id="username" value="" required="">
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="item">
                            <div class="icon"><img src="__RES__/theme/blue/picture/icon_merchant2.png" alt=""></div>
                            <input type="password" name="password" id="password" class="form-control" value="" placeholder="请输入密码" required="">
                        </div>
                    </div>
                    <div class="col-lg-12 mb-0">
                        <button type="button" id="login_btn" style="margin-bottom: 40px" class="form-control btn-login">
                            登 录
                        </button>
                    </div>
                    <div class="col-lg-12 mb-0">
                        <div class="end-item"><a href="/login/retpwd">忘记密码</a>
                            <div class="r"><span>没有账号？<a href="/register">立即注册</a></span></div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="otherlogin">
                            <div class="h"><span>第三方账号登录</span></div>
                            <div class="bd">                                
                                <div class="icon" style="cursor: pointer;">
                                    <div>
                                                    {if plugconf("oauth2", "qq_open_merchant") == 1}
                                                    <a href="{:url('user/qqlogin')}" class="mr-2">
                                                        <img src="__STATIC__/theme/landrick/images/qq.svg" width="45" height="45">
                                                    </a>
                                                    {/if}
                                                    {if plugconf("oauth2", "wechat_open_merchant") == 1}
                                                    <a href="{:url('user/wechatlogin')}" class="mr-2">
                                                        <img src="__STATIC__/theme/landrick/images/wx.svg" width="45" height="45">
                                                    </a>
                                                    {/if}
                                                </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
  <script src="__RES__/theme/landrick/js/jquery-3.5.1.min.js"></script>
        <script src="__RES__/theme/landrick/js/bootstrap.bundle.min.js"></script>
        <script src="__RES__/theme/landrick/js/jquery.easing.min.js"></script>
        <script src="__RES__/theme/landrick/js/scrollspy.min.js"></script>
        <!-- Icons -->
        <script src="__RES__/theme/landrick/js/feather.min.js"></script>
        <script src="__RES__/theme/landrick/js/bundle.js"></script>
        <!-- Main Js -->
        <script src="__RES__/theme/landrick/js/app.js"></script>

        <script src="/static/app/js/layer.js"></script>
        <script>
            $('#login_btn').click(function () {
                if ($('#username').val() == '') {
                    layer.msg('请输入用户名');
                    return false;
                }
                if ($('#password').val() == '') {
                    layer.msg('请输入密码');
                    return false;
                }
                var loading = '';
                $.ajax({
                    type: 'post',
                    url: '/index/user/doLogin',
                    dataType: "json",
                    data: $("form").serialize(),
                    beforeSend: function (xhr) {
                        loading = layer.load()
                    },
                    success: function (res) {
                        layer.close(loading);
                        if (res.code == 1) {
                            layer.msg('恭喜您，登录成功！', {icon: 6, time: 1000});
                            window.location.href = '/merchant';
                        } else {
                            layer.msg(res.msg, {icon: 6, time: 1000});
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        layer.close(loading);
                        layer.msg('请刷新页面重试');
                    }
                });
            })
            $(document).keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#login_btn").trigger("click");
                }
            })
        </script>

</body>
</html>