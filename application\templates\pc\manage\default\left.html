<div class="framework-sidebar hide">
    <div class="sidebar-content">
        <div class="sidebar-inner">
            <div class="sidebar-fold">
                <span class="glyphicon glyphicon-option-vertical transition"></span>
            </div>
            {volist name='menus' id='pmenu'}
            {notempty name='pmenu.sub'}
            <div data-menu-box="m-{$pmenu.id}">
                {volist name='pmenu.sub' id='menu'}
                <div class="sidebar-nav main-nav">
                    {empty name='menu.sub'}
                    <ul class="sidebar-trans">
                        <li class="nav-item">
                            <a data-menu-node='m-{$pmenu.id}-{$menu.id}' data-open="{$menu.url}"
                               class="sidebar-trans">
                                <div class="nav-icon sidebar-trans">
                                    <span class="{$menu.icon|default='fa fa-link'} transition"></span>
                                </div>
                                <span class="nav-title">{$menu.title}</span>
                            </a>
                        </li>
                    </ul>
                    {else}
                    <div class="sidebar-title">
                        <div class="sidebar-title-inner">
                            <span class="sidebar-title-icon fa fa-caret-right transition"></span>
                            <span class="sidebar-title-text">{$menu.title}</span>
                        </div>
                    </div>
                    <ul class="sidebar-trans" style="display:none" data-menu-node='m-{$pmenu.id}-{$menu.id}'>
                        {volist name='menu.sub' id='submenu'}
                        <li class="nav-item">
                            <a data-menu-node='m-{$pmenu.id}-{$submenu.id}' data-open="{$submenu.url}"
                               class="sidebar-trans">
                                <div class="nav-icon sidebar-trans">
                                    <span class="{$submenu.icon|default='fa fa-link'} transition"></span>
                                </div>
                                <span class="nav-title">{$submenu.title}</span>
                            </a>
                        </li>
                        {/volist}
                    </ul>
                    {/empty}
                </div>
                {/volist}
            </div>
            {/notempty}
            {/volist}
        </div>
    </div>
</div>