<!DOCTYPE html>
<html lang="zh">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <title>投诉订单 -  {:sysconf('site_subtitle')}</title>
    <link rel="stylesheet" href="__RES__/theme/blue/css/layui.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/animate.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/bootstrap.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/swiper.min.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/style.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/response.css">
    <link rel="stylesheet" href="__RES__/theme/blue/css/order.css">
    <style>
        .complaint-info{padding-top: .1rem;}
        .complaint-info .title{ border-left: 3px solid #3476fe; font-size: .4rem; padding-top: .2rem;padding-left: .1rem;padding-bottom: .2rem;}
        .complaint-info .form-item{display: flex;  margin-bottom: 15px;font-size: .2rem;}
        .complaint-info .form-item .label{
            margin: 0 10px 0 0;
            flex-shrink: 0;
            line-height: 37px;
            height: 37px;
            user-select: none;
            font-family: 'é»‘ä½“';
        }
        .complaint-info .form-item .input-box input[type="text"]{
            line-height: 37px;
            height: 37px;
            font-size: 14px;
        }
        .complaint-info .form-item .input-box input[type="file"]{
            line-height: 37px;
            height: 37px;
            font-size: 14px;
        }
        .complaint-info .form-item .input-box textarea{
            font-size: 14px;
        }
        .complaint-info .form-item .input-box{
            width: 100%;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .complaint-info .form-control{
            padding: 0 .1rem;
        }
        .complaint-info .input-box .type-item{
            border: 1px solid #eee;
            display: inline-block;
            padding: 0 10px;
            border-radius: 3px;
            margin: 0 10px 10px 0;
            font-size: 14px;
            line-height: 35px;
            cursor: pointer;
            user-select: none;
            position: relative;
            overflow: hidden;
        }
        .complaint-info .input-box .type-item input{
            display: none;
        }
        .complaint-info .input-box .type-item.checked{
            border: 1px solid #3476fe;
        }
        .complaint-info .input-box .type-item.checked:before{
            content: '';
            position: absolute;
            right: -9px;
            bottom: -5px;
            background-color: #3476fe;
            width: 26px;
            height: 15px;
            transform: rotate(-45deg);
        }
        .complaint-info .input-box .type-item.checked:after{
            opacity: 1;
            content: '';
            position: absolute;
            width: 5px;
            height: 11px;
            background: transparent;
            bottom: 1px;
            right: 1px;
            border: 2px solid #fff;
            border-top: none;
            border-left: none;
            -webkit-transform: rotate(35deg);
            -moz-transform: rotate(35deg);
            -o-transform: rotate(35deg);
            -ms-transform: rotate(35deg);
            transform: rotate(35deg);
        }
        .complaint-info form [type="submit"]{
            width: 150px;
            display: block;
        }

        .complaint-info .input-box .btn-experience{
            height: .5rem;
            line-height: 36px;
            padding: 0;
            background-color: #3476fe;
            color: #fff;
            -webkit-border-radius: 18px;
            -moz-border-radius: 18px;
            border-radius: 18px;
            font-size: .2rem;
        }
        @media (max-width: 768px){
            .complaint-info .input-box .btn-experience{
                height: .8rem;font-size: .3rem;
            }
        }
        .form-control:focus{
            box-shadow: 0 0 0 0.03rem rgba(0,123,255,.25);
        }
    </style>
</head>
<body>
<header class="header query-header">
    <div class="bgimg"><img src="__RES__/theme/blue/picture/header_bg.png" alt=""></div>
    <div class="container">
{include file="./default_header"}
        <div class="banner">
            <div class="text-introduce">
                <div class="h1">轻松投诉订单，保障您的消费权益</div>
            </div>
               <div class="img"><img src="__RES__/theme/blue/picture/banner_query_img.png" alt=""></div>
        </div>
    </div>
</header>
<div class="query">
    <div class="container">
        <div class="order-search">
            <div class="order-search-box complaint-info">
                <div class="title">订单投诉</div>
                <form class="form" style="margin-top: .2rem;padding-bottom: .2rem;" name='report'  action='' method='post' enctype="multipart/form-data">
                    <div class="form-item">
                        <label class="label">订单编号</label>
                        <div class="input-box">
                            <input type="text"  name="trade_no" value="{$Think.get.trade_no|htmlentities}" placeholder="请输入订单号"  class="form-control">
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">投诉原因</label>
                        <div class="input-box">
                            <label class="type-item checked">无效卡密<input type="radio" name="type" checked="" value="无效卡密"></label>
                            <label class="type-item">虚假商品<input type="radio" name="type" value="虚假商品"></label>
                            <label class="type-item">非法商品<input type="radio" name="type" value="非法商品"></label>
                            <label class="type-item">侵权商品<input type="radio" name="type" value="侵权商品"></label>
                            <label class="type-item">不能购买<input type="radio" name="type" value="不能购买"></label>
                            <label class="type-item">恐怖色情<input type="radio" name="type" value="恐怖色情"></label>
                            <label class="type-item">其他投诉<input type="radio" name="type" value="其他投诉"></label>
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">联系方式</label>
                        <div class="input-box">
                            <input name="qq" type="text" placeholder="先将常用QQ设置为任何人可以添加，卖家会主动联系你解决"  class="form-control">
                        </div>
                    </div>
                    <div class="form-item">
                        <label class="label">手机号码</label>
                        <div class="input-box">
                            <input type="text" name="mobile" value="" placeholder="用于接收撤诉查看进度短信密码，填错将无法查看投诉处理进度，后果自负" class="form-control">
                        </div>
                    </div>
                    
                    
                    {if sysconf('complaint_qrcode')==1}
                                        <div class="form-item">
                        <label class="label">上传二维码</label>
                        <div class="input-box" style="position :relative">
                            <input  name="buyer_qrcode" type="file" placeholder="用于如果胜诉将会把资金退回此收款账号" class="form-control">
                        </div>
                    </div>
                    {/if}
                    
                    
                    
                    
                    
                    <div class="form-item">
                        <label class="label">选择卡密</label>
                        <div class="input-box" style="position :relative">
    <input name="select_cards" type="hidden" value="">
                                                        <input readonly="readonly" name="select_text" type="text" placeholder="请选择售后卡密" class="form-control">
                                                        <button id="selectBtn" style="position: absolute; top: 6px;right: 18px;background-color: #3476fe;
    border-radius: 25px;color:#fff;padding:6px 8px; font-size: 14px; border:none">选择卡密</button>
    
    
    
    
    
                        </div>
                    </div>
                    
                    
                    
                    
                    <div class="form-item">
                        <label class="label">详细内容</label>
                        <div class="input-box">
                            <textarea name="desc" class="layui-textarea" maxlength="100" placeholder="请具体说明问题，如：无法正常充值，卖家不处理等（100字以内）"></textarea>
                        </div>
                    </div>
                    <div class="form-item">
                        <input type="hidden" name="token" value="{$token|htmlentities}">
                        <label class="label"><span style="opacity: 0;">操作按钮</span></label>
                        <div class="input-box">
                            <button type="submit" class="btn btn-experience">提交投诉</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal order-modal" id="orderModal">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="modal-header">
				<img src="__RES__/theme/blue/picture/icon_merchant2.png" alt="">
				<dl>
					<dt>请输入投诉查询密码</dt>
					<dd>输入查询密码才可查看投诉内容</dd>
				</dl>
				<button type="button" class="close" data-dismiss="modal">&times;</button>
			</div>
			<form action="/order_complaint_detail" method="post" onsubmit="return check_take_pass()">
				<div class="modal-body">
					<input type="text" name="pass" maxlength="30" class="form-control" placeholder="请输入投诉查询密码">
					<input type="hidden" name="trade_no">
					<input type="hidden" name="token" value="bb5c0a420a4c32df8d48bc0c671d88d31679143303">
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-cancel" data-dismiss="modal">取 消</button>
					<button type="submit" class="btn btn-submit">确 认</button>
				</div>
			</form>
		</div>
	</div>
</div>
{include file="./default_footer"}



















<script>
    $(window).on("scroll",function(){
        if($(window).scrollTop() > 100){
            $(".go-top").fadeIn();
        }else{
            $(".go-top").fadeOut();
        }
    })
    $(".go-top").on("click",function(){
        $("body,html").animate({scrollTop:0},500);
        return false
    });
</script>
<script>
    var layer;
    layui.use(['layer'], function () {
        layer = layui.layer;
    })
</script>

<script>
    $(function () {
        $(".type-item").click(function () {
            $(this).addClass('checked');
            $(this).siblings().removeClass('checked');
        })
    })
</script>
<script>
$(".navbar-nav a").each(function () {
    if (this.href == window.location.href) {
        $(this).addClass("active");
        //$(this).parent().parent().parent().addClass("active");
        //$(this).parent().parent().parent().parent().parent().addClass("active");
    }
});
</script>




























<script src="__RES__/theme/landrick/js/app.js"></script>
        <script src="/static/app/js/layer.js"></script>
        <script>

            var select_card_form;

            function closeSelectForm()
            {
                layer.close(select_card_form);
            }

            $("#selectBtn").click(function ()
            {
                var trade_no='';
                if('{$Think.get.trade_no|htmlentities}'!='')
                {
                    trade_no='{$Think.get.trade_no|htmlentities}';
                }else if($("input[name='trade_no']").val()!='')
                {
                    trade_no=$("input[name='trade_no']").val();
                }
                select_card_form = layer.open({
                    type: 2,
                    fix: false,
                    maxmin: true,
                    shadeClose: false,
                    area: ['420px', 'auto'],
                    shade: 0.4,
                    title: "请选择需要售后的卡密",
                    content: '/index/plugin/complaintCard?trade_no=' +trade_no,
                    success: function (layero, index) {
                        layer.iframeAuto(index);
                    }
                });
                return false;
            });

            var select_lable;
            function selectLable(ids, num)
            {
                $("[name=select_cards]").val(ids);
                $("[name=select_text]").val("已选择" + num + "张");
            }
        </script>

</body>
</html>