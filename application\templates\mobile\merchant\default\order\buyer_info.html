{extend name="lite_base" /}

{block name="content"}
<div class="card mb-0">

    <div class="card-content">
        <div class="card-body">
            <form id="form1" class="form form-vertical">
                <div class="form-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="name">IP地址</label>
                                <input id="name" type="text"  class="form-control" name="name" value="{$order.create_ip}">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label for="sort">设备指纹</label>
                                <input id="name" type="text"  class="form-control" name="name" value="{$order.fingerprint}">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label for="sort">微信openid</label>
                                <input id="name" type="text"  class="form-control" name="name" value="{$order.openid}">
                            </div>
                        </div>

                    </div>
                </div>
            </form>
            <div class="col-12 d-flex justify-content-center">
                <button  class="btn btn-primary mr-1 mb-1 btn-submit" onclick="del('{$order.id}')">一键拉黑</button>
            </div>
        </div>
    </div>

</div>
{/block}

{block name="js"}
<!-- BEGIN: Page JS-->

<script>
    function del(id)
    {
        $.confirm({
            title: '温馨提示',
            content: '是否确认拉黑买家？',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});


                        $.ajax({
                            url: "{:url('buyerToBlack')}",
                            data: {
                                id: id,
                            },
                            type: 'post',
                            dataType: 'json',
                            beforeSend: function () {
                                loading = layer.load();
                            },
                            success: function (res) {
                                layer.close(loading);
                                if (res.code == 1) {
                                    layer.msg(res.msg, {icon: 1, time: 1000});
                                } else {
                                    layer.msg(res.msg, {time: 2000, icon: 'error'});
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                layer.close(loading);
                                layer.msg('连接错误');
                            }
                        });



                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });

        return false;
    }
</script>

<!-- END: Page JS-->
{/block}

