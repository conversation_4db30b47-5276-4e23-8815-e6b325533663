{extend name='./content'}

{block name="button"}
<div class="nowrap pull-right" style="margin-top:10px">
    <button data-modal='{:url("add")}' data-title="添加商户" class='layui-btn layui-btn-small'><i
            class='fa fa-plus'></i> 添加商户
    </button>
</div>
{/block}

{block name="content"}

<!-- 表单搜索 开始 -->
<form class="layui-form layui-form-pane form-search" action="__SELF__" onsubmit="return false" method="get">

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">搜索字段</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="field">
                <option value="1" {if $Think.get.field=='1'}selected{/if}>商户ID</option>
                <option value="2" {if $Think.get.field=='2'}selected{/if}>账号</option>
                <option value="3" {if $Think.get.field=='3'}selected{/if}>店铺名</option>
                <option value="4" {if $Think.get.field=='4'}selected{/if}>手机号码</option>
                <option value="5" {if $Think.get.field=='5'}selected{/if}>QQ</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">搜索关键词</label>
        <div class="layui-input-inline">
            <input name="keyword" value="{$Think.get.keyword|default=''|htmlentities}" placeholder="请输入关键词" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="status">
                <option value="" {if $Think.get.status==''}selected{/if}>全部</option>
                <option value="0" {if $Think.get.status==='0'}selected{/if}>未审核</option>
                <option value="1" {if $Think.get.status=='1'}selected{/if}>已审核</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">冻结状态</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="is_freeze">
                <option value="" {if $Think.get.is_freeze==''}selected{/if}>全部</option>
                <option value="0" {if $Think.get.is_freeze==='0'}selected{/if}>未冻结</option>
                <option value="1" {if $Think.get.is_freeze=='1'}selected{/if}>已冻结</option>
            </select>
        </div>
    </div>


    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">API状态</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="payapi">
                <option value="" {if $Think.get.payapi==''}selected{/if}>全部</option>
                <option value="0" {if $Think.get.payapi==='0'}selected{/if}>未开启</option>
                <option value="1" {if $Think.get.payapi=='1'}selected{/if}>已开启</option>
            </select>
        </div>
    </div>


    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">费率分组</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="group_id">
                <option value="" {if $Think.get.group_id==''}selected{/if}>全部</option>
                {foreach $rate_group as $v}
                <option value="{$v.id}" {if $Think.get.group_id==$v.id}selected{/if}>{$v.title}</option>
                {/foreach}
                <option value="-1" {if $Think.get.group_id=='-1'}selected{/if}>单独自定义费率</option>
                <option value="-2" {if $Think.get.group_id=='-2'}selected{/if}>未设置费率分组(默认使用通道费率)</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">注册时间</label>
        <div class="layui-input-inline">
            <input name="date_range" id="date_range" value="{:urldecode($Think.get.date_range)}" placeholder="请选择时间范围" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item layui-inline">
        <label class="layui-form-label">余额</label>
        <div class="layui-input-inline">
            <select class="layui-input" name="have_balance">
                <option value="" {if $Think.get.have_balance==''}selected{/if}>全部</option>
                <option value="1" {if $Think.get.have_balance=='1'}selected{/if}>有余额</option>
                <option value="0" {if $Think.get.have_balance=='0'}selected{/if}>无余额</option>
            </select>
        </div>
    </div>


    <div class="layui-form-item layui-inline">
        <button type="submit" class="layui-btn layui-btn-primary"><i class="fa fa-search"></i> 搜 索</button>
    </div>
</form>
<!-- 表单搜索 结束 -->

<div class="layui-form-item layui-inline">
    <label class="layui-form-label">商户数</label>
    <div class="layui-input-inline">
        <input type="text" class="layui-input" value="{$user_count|htmlentities}" readonly>
    </div>
</div>


<div class="layui-form-item layui-inline">
    <div class="layui-input-inline"  style="width: 260px;">
        <form onsubmit="return false;" data-auto="true" method="post">
            {if sysconf('user_registernotify_open')==1}
            <span style="color:green"><i class="glyphicon glyphicon-ok"></i> 已开启新注册邮箱通知管理员</span>
            <a class="btn btn-danger btn-xs text-white" data-update="0" data-field='status' data-value='0' data-action='{:url("user_registernotify_open")}'
               href="javascript:void(0)">关闭</a>
            {else/}
            <span style="color:red"><i class="glyphicon glyphicon-remove"></i> 已关闭新注册邮箱通知管理员</span>
            <a class="btn btn-warning btn-xs"  data-update="0" data-field='status' data-value='1' data-action='{:url("user_registernotify_open")}'
               href="javascript:void(0)">开启</a>
            {/if}
        </form>
    </div>
</div>

<form onsubmit="return false;" data-auto="true" method="post">
    <input type="hidden" value="resort" name="action"/>
    <table class="layui-table" lay-skin="line" lay-size="sm">
        <thead>
            <tr>
                <th class='text-center'>ID</th>
                <th>账号</th>
                <th>下级商户数</th>
                <th>店铺名</th>
                <th>手机</th>
                <th>身份证号码</th>
                <th>QQ</th>
                <th>余额</th>
                <th>冻结金额</th>
                <th>佣金</th>
                <th>状态</th>
                <th>冻结</th>
                <th>被投诉次数</th>
                <th class='text-center'>注册时间</th>
                <th class='text-center'>操作</th>
            </tr>
        </thead>
        <tbody>
            {foreach $users as $v}
            <tr>
                <td class='text-center'>{$v.id}</td>
                <td>{$v.username}</td>
                <td class='text-center'>{$v.sub_user_count}</td>
                <td>{$v.shop_name}</td>
                <td>{$v.mobile}</td>
                <td>{$v.idcard_number}</td>
                <td>{$v.qq}</td>
                <td>{$v.money}</td>
                <td>{$v.freeze_money}</td>
                <td>{$v.rebate}</td>
                <td>
                    {if $v.status==1}
                    <a style="color:green" data-tips="确定不可用吗？ " data-update="{$v.id}" data-field='status' data-value='0' data-action='{:url("change_status")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-ok"></i></a>
                    {else/}
                    <a style="color:red" data-tips="确定可用吗？" data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("change_status")}'
                       href="javascript:void(0)"><i class="glyphicon glyphicon-remove"></i></a>
                    {/if}
                </td>
                <td>
                    {if $v.is_freeze==1}
                    <a style="color:red" data-tips="确定取消冻结吗？ " data-update="{$v.id}" data-field='status' data-value='0' data-action='{:url("change_freeze_status")}'
                       href="javascript:void(0)">已冻结</a>
                    {else/}
                    <a style="color:green" data-tips="确定冻结吗？" data-update="{$v.id}" data-field='status' data-value='1' data-action='{:url("change_freeze_status")}'
                       href="javascript:void(0)">未冻结</a>
                    {/if}
                </td>
                <td class='text-center'>{$v.complaint_count}</td>
                <td class='text-center'>{$v.create_at|date="Y-m-d H:i:s",###}</td>
                <td class='text-center nowrap'>
                    <a href="{:url("login")}?user_id={$v.id}" target="_blank">登录</a>
                    <a data-title="发送站内信" data-modal='{:url("message")}?user_id={$v.id}' href="javascript:void(0)">站内信</a>
                    <a data-title="商户详情" data-modal='{:url("detail")}?user_id={$v.id}' href="javascript:void(0)">详情</a>
                    <a data-title="编辑" data-modal='{:url("edit")}?user_id={$v.id}' href="javascript:void(0)">编辑</a>
                    <a data-title="删除"  href="javascript:void(0)" onclick="user_del(this, '{$v.id}')">删除</a>
                    <br>
                    <a data-title="设置费率" data-modal='{:url("rate")}?user_id={$v.id}' href="javascript:void(0)">设置费率</a>
                    <a data-title="资金管理" data-modal='{:url("manage_money")}?user_id={$v.id}' href="javascript:void(0)">资金管理</a>
                    <a data-title="收款信息"  href="javascript:void(0)" onclick="allow_update(this, '{$v.id}')">收款信息</a>
                </td>
            </tr>
            {/foreach}
        </tbody>
    </table>
</form>
{$page}
<script>
    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //日期范围
        laydate.render({
            elem: '#date_range',
            range: true
        });
    });
    /*用户-删除*/
    function user_del(obj, id) {
        layer.confirm('确认要删除这个用户吗？', function (index) {
            $.ajax({
                url: "/manage/user/del",
                type: 'post',
                data: 'id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        $(obj).parents("tr").remove();
                        layer.msg('已删除!', {icon: 1, time: 1000});
                    } else {
                        layer.msg('删除失败!', {icon: 1, time: 1000});
                    }
                }
            });
        });
    }
    /*用户-修改收款信息*/
    function allow_update(obj, id) {
        layer.confirm('允许用户修改收款信息？', function (index) {
            $.ajax({
                url: "{:url('allow_update')}",
                type: 'post',
                data: 'id=' + id,
                success: function (res) {
                    if (res.code == 1) {
                        layer.msg('设置成功!', {icon: 1, time: 1000});
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1000});
                    }
                }
            });
        });
    }

</script>
{/block}
