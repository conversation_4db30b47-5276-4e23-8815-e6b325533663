{extend name="simple_base"}

{block name="css"}
<link href="__RES__/merchant/default/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />
<style>
    .swal2-title{
        font-size: 1rem;
    }
</style>
{/block}

{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body p-3">

            <h4 class="card-title mb-6 fs-6">店铺代理设置</h4>
            <div class="form-group">
                <label class="form-label" >代理功能状态{if $_user.agent_goods_switch==1}  <span class="badge badge-pill bg-success font-size-12">已开启</span>{else/} <span class="badge badge-pill bg-danger font-size-12">已关闭</span>{/if}</label>
                <div class="form-group">
                    {if $_user.agent_goods_switch==1}
                    <button  onclick="agent_key('0', '关闭商品代理功能后,将隐藏供货相关参数 不涉及功能使用，如禁止代理继续售卖请编辑商品关闭代理功能！');" class="btn btn-danger  waves-effect waves-light btn-sm ml-2">关闭代理功能</button>
                    {else/}
                    <button  onclick="agent_key('1', '开启商品代理功能后,将显示出供货相关参数 不涉及功能使用');" class="btn btn-success  waves-effect waves-light btn-sm  ml-2">开启代理功能</button>
                    {/if}
                </div>
            </div>
            {if $_user.agent_goods_switch==1}
            <div class="form-group">
                <label class="form-label">我的店铺对接码</label>
                <div class="form-group">
                    <p class='mb-2'><b>{$_user.agent_key}</b></p>
                    <a href="javascript:;" class="btn btn-primary btn-sm waves-effect waves-light clipboard me-2" data-clipboard-text="{$_user.agent_key}">复制对接码</a>
                    <button onclick="editagentkey()"  class="btn btn-primary  waves-effect waves-light btn-sm me-2">自定义对接码</button>

                </div>
            </div>
            <div class="form-group">
                <label class="form-label">成为代理是否需要审核</label>
                <div class="form-group">

                    <div class="d-flex align-items-center">

                        <div class="form-check">
                            <input onchange="change_check(this, 0)"  {if $_user.need_check_agent==0}checked{/if} value="0" type="radio" id="need_check_agent0" name="need_check_agent" class="form-check-input mt-0" checked="">
                                   <label class="form-label mb-0 " for="can_proxy0">免审</label>
                        </div>

                        <div class="form-check ms-3">
                            <input onchange="change_check(this, 1)"  {if $_user.need_check_agent==1}checked{/if} value="1" type="radio" id="need_check_agent1" name="need_check_agent" class="form-check-input mt-0">
                                   <label class="form-label mb-0 " for="need_check_agent1">需要</label>
                        </div>
                    </div>

                </div>
            </div>


     
            {/if}

            <div class="divider border-light"></div>

            <div class="row">
                <div class="col-12 mb-2">
                    <a href="{:url('agent/pool')}" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                        <i class="bx bx-plus me-1"></i>全网商品对接
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-6 mb-2">
                    <a href="{:url('agent/mypool')}" class="btn btn-light w-100 d-flex align-items-center justify-content-center">
                        <i class="bx bxl-dropbox me-1"></i>商品池管理
                    </a>
                </div>
                <div class="col-6 mb-2">
                    <a href="{:url('agent/supplygoods')}" class="btn btn-light w-100 d-flex align-items-center justify-content-center">
                        <i class="bx bx-archive me-1"></i>对接的商品
                    </a>
                </div>
                <div class="col-6 mb-2">
                    <a href="{:url('agent/proxylist')}" class="btn btn-light w-100 d-flex align-items-center justify-content-center">
                        <i class="bx bx-group me-1"></i>下级代理列表
                    </a>
                </div>
                <div class="col-6 mb-2">
                    <a href="{:url('agent/proxyOrder')}" class="btn btn-light w-100 d-flex align-items-center justify-content-center">
                        <i class="bx bx-receipt me-1"></i>下级代理订单
                    </a>
                </div>
            </div>

        </div>
    </div>
</div>

{/block}
{block name="js"}
<script src="__RES__/app/js/clipboard.js"></script>
<script src="__RES__/merchant/default/libs/sweetalert2/sweetalert2.min.js"></script>
<script>
                        var clipboard = new ClipboardJS('.btn');
                        clipboard.on('success', function (e) {
                            layer.msg('复制成功！', {
                                icon: 1
                            });
                        });

                        clipboard.on('error', function (e) {
                            layer.msg('复制失败，请手动复制！', {
                                icon: 2
                            });
                        });

                        function editagentkey()
                        {
                            Swal.fire({
                                title: '请输入自定义对接码',
                                input: 'text',
                                showCancelButton: true,
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                showLoaderOnConfirm: true,
                                confirmButtonColor: "#556ee6",
                                cancelButtonColor: "#f46a6a",
                            }).then(function (res) {
                                if (res.isConfirmed)
                                {
                                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                                    $.post("{:url('user/editAgentkey')}", {agent_key: res.value}, function (res) {
                                        layer.closeAll();
                                        if (res.code != 1) {
                                            $.alert(res.msg);
                                        } else {
                                            $.alert("设置成功");
                                            setTimeout(function () {
                                                location.reload();
                                            }, 200);
                                        }
                                    });
                                }

                            })
                        }

                        function agent_key(status, msg) {

                            $.confirm({
                                title: '温馨提示',
                                content: msg,
                                type: 'red',
                                typeAnimated: true,
                                buttons: {
                                    tryAgain: {
                                        text: '确定',
                                        btnClass: 'btn-red',
                                        action: function () {
                                            var loading = layer.load(1, {shade: [0.1, '#fff']});
                                            $.post("{:url('agent/agentSwitch')}", {status: status}, function (res) {
                                                if (res.code != 1) {
                                                    $.alert("设置失败");
                                                } else {
                                                    $.alert("设置成功");
                                                    setTimeout(function () {
                                                        location.reload();
                                                    }, 200);
                                                }
                                            });
                                        }
                                    },
                                    cancel: {
                                        text: '取消',
                                    }
                                }
                            });
                        }
                        function change_check(obj, status)
                        {
                            var loading = layer.load(1, {shade: [0.1, '#fff']});
                            $.post("{:url('agent/changeAgetneedcheck')}", {status: status}, function (res) {
                                layer.close(loading);
                                if (res.code != 1) {
                                    $.alert("设置失败");
                                } else {
                                    $.alert("设置成功");
                                    setTimeout(function () {
                                        location.reload();
                                    }, 200);
                                }
                            });
                        }
</script>
{/block}


