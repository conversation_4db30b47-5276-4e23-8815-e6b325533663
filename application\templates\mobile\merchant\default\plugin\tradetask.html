{extend name="simple_base"}


{block name="css"}
<style>
    .avatar-title {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        background-color: #556ee6;
        color: #fff;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        font-weight: 500;
        height: 100%;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: 100%;
        width:20px;
        height: 20px;
    }
    .bg-warning.bg-soft {
        background-color: rgba(241,180,76,.25) !important;
    }

</style>
{/block}
{block name="content"}

<div class="container">

    {if $pedding}
    <div class="col-xl-12">
        <div class="card">
            <div class="card-body p-3">

                {if $pedding.trade_money>=$pedding.task.target}
                <div class="alert alert-success" role="alert">
                    {$pedding.task.name}&nbsp;&nbsp;<b>已完成</b>
                </div>
                {else/}
                <div class="alert alert-primary" role="alert">
                    {$pedding.task.name}&nbsp;&nbsp;<b>进行中</b>
                </div>
                {/if}
                <p class="card-title-desc">目标流水：￥{$pedding.task.target}&nbsp;&nbsp;任务奖励：￥{$pedding.task.reward}</p>
                <p class="card-title-desc">任务时间：{$pedding.create_at|date='Y-m-d H:i:s',###} - {$pedding.expire_at|date='Y-m-d H:i:s',###}</p>
                <span class="badge badge-pill bg-success">已完成流水：{$pedding.trade_money}元</span>
                <span class="badge bg-success text-white">剩余时间：{$pedding.expire_day}</span>
                {if $pedding.trade_money>=$pedding.task.target}
                <button onclick="receivereward(this, '{$pedding.id}')" class="btn btn-outline-success float-end btn-sm">领取奖励</button>
                {else/}
                <button onclick="giveuptask(this, '{$pedding.id}')" class="btn btn-outline-danger float-end btn-sm">放弃</button>
                {/if}
            </div>
        </div>
    </div>
    {/if}
    <div class="col-xl-12">
        <div class="card mt-3">
            <div class="card-body p-3">

                <div class='row'>
                    {foreach $tradeTask as $v}
                    <div class="col-lg-3">
                        <div class="border p-3 rounded mt-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-xs me-1">
                                    <span class="avatar-title rounded-circle bg-warning bg-soft text-warning">
                                        <i class="mdi mdi-bitcoin"></i>
                                    </span>
                                </div>
                                <h6 class="font-size-14 font-weight-bold mb-0">{$v.name}</h6>
                            </div>
                            <div class="row">
                                <div class="col-lg-9">
                                    <div class="text-muted">
                                        <p class="mb-2">目标金额：{$v.target}&nbsp;任务时长：{$v.duration}天</p>
                                        <h6>奖励：￥{$v.reward}</h6>
                                        <p class="mb-0">{$v.desc}</p>
                                    </div>
                                </div>
                                <div class="col-lg-3 align-self-end">
                                    <div class="float-end mt-1">
                                        <button onclick="applytask(this, '{$v.id}')" class="btn btn-outline-primary btn-sm">领取</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>

            </div>
        </div>
    </div>
    <div class="col-xl-12">
        <div class="card  mt-3">
            <div class="card-body">
                <h6 class="card-title mb-4">任务记录</h6>

                {foreach $order as $o}
                <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="text-truncate mb-0 float-left d-inline">{$o.task.name}</h6>
                        {switch name="o.status"}
                        {case value="0"}<span class="badge rounded-pill bg-warning float-end">未完成</span>{/case}
                        {case value="1"}<span class="badge rounded-pill bg-success float-end">已完成</span>{/case}
                        {case value="-1"}<span class="badge rounded-pill bg-danger float-end">已放弃</span>{/case}
                        {/switch}
                    </div>
                    <div class="d-flex align-items-center mt-1 justify-content-between">
                        <div class="order-wrap-text">
                            <span>目标金额：</span>
                            <span>{$o.task.target}元</span>
                        </div>
                        <div class="order-wrap-text">
                            <span>奖励金额：</span>
                            <span>{$o.task.reward}元</span>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mt-1 justify-content-between">
                        <div class="order-wrap-text">
                            <span>完成时间：</span>
                            <span>
                                {if $o.status==1}
                                {$o.success_at|date="Y-m-d H:i:s",###}
                                {else}
                                ---
                                {/if}
                            </span>
                        </div>

                    </div>
                </div>

                {/foreach}

            </div>
        </div>
    </div>

</div>

{/block}
{block name="js"}

<script>

    function applytask(obj, id)
    {
        $.confirm({
            title: '确定要申请当前任务吗？',
            content: '任务只能同时领取一个',
            type: 'green',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-success',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('tradetask')}", {
                            act: "applytask",
                            id: id
                        }, function (res) {
                            layer.closeAll();
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("领取成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }
    function giveuptask(obj, id)
    {
        $.confirm({
            title: '确定要放弃当前任务吗？',
            content: '已完成流水将会清空',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-red',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('tradetask')}", {
                            act: "giveuptask",
                            id: id
                        }, function (res) {
                            layer.closeAll();
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert("放弃成功");
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });
    }

    function receivereward(obj, id)
    {
        var loading = layer.load(1, {shade: [0.1, '#fff']});
        $.post("{:url('tradetask')}", {
            act: "receivereward",
            id: id
        }, function (res) {
            layer.closeAll();
            $.alert(res.msg);
            if (res.code == 1) {
                setTimeout(function () {
                    location.reload();
                }, 200);
            }
        });
    }

</script>
{/block}


