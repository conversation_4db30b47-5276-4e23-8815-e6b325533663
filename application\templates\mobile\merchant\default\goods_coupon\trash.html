{extend name="simple_base"}
{block name="css"}

{/block}


{block name="content"}

<div class="container">
    <div class="card">
        <div class="card-body  p-3">

            {foreach $coupons as $k=> $v}
            <div class="single-search-result mb-3 border-bottom pb-3 order-wrap">
                <div class="">
                    <h6 class="text-truncate mb-0 float-left d-inline">{$v.code}</h6>
                    {if $v.status==2}
                    <span class="badge rounded-pill bg-success float-end">已使用</span>
                    {else/}
                    <span class="badge rounded-pill bg-light float-end">未使用</span>
                    {/if}
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>适用商品：</span>
                        <span>{if $v.cate_id==0}全部{else/}{$v.category.name}{/if}</span>
                    </div>
                    <div class="order-wrap-text">
                        <span>面值：</span>
                        <span>
                            {$v.amount}{if $v.type==1}元{else/}%{/if}
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-center mt-1 justify-content-between">
                    <div class="order-wrap-text">
                        <span>删除：</span>
                        <span>{$v.delete_at|date="Y-m-d H:i:s",###}</span>
                    </div>
                </div>

                <div class="goods-warp d-flex align-items-center mt-3 justify-content-end">
                    <a class="me-1" href="javascript:void(0);" onclick="restore(this, '{$v.id}')"><span class="goods-warp-btn" >恢复</span></a>
                </div>
            </div>
            {/foreach}


            <nav aria-label="Page navigation">
                {$page}
            </nav>
        </div>
    </div>
</div>

{/block}
{block name="js"}

<script>
    function restore(obj, id)
    {
        $.confirm({
            title: '提示！',
            content: '确定恢复吗？',
            type: 'green',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: '确定',
                    btnClass: 'btn-green',
                    action: function () {
                        var loading = layer.load(1, {shade: [0.1, '#fff']});
                        $.post("{:url('goods_coupon/restore')}", {
                            id: id
                        }, function (res) {
                            if (res.code != 1) {
                                $.alert(res.msg);
                            } else {
                                $.alert('恢复成功');
                                setTimeout(function () {
                                    location.reload();
                                }, 200);
                            }
                        });
                    }
                },
                cancel: {
                    text: '取消'
                }
            }
        });

    }
</script>
{/block}


