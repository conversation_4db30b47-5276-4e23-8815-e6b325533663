<form class="layui-form layui-box" style='padding:25px 30px 20px 0' action="{:url('add')}" data-auto="true" method="post">

    <div class="layui-form-item">
        <label class="layui-form-label">费率分组名称</label>
        <div class="layui-input-block">
            <input type="text" name="title" value="" class="layui-input"
                   title="请输入费率分组名称" placeholder="请输入费率分组名称">
        </div>
    </div>

    {foreach $rules as $v}
    <div class="layui-form-item">
        <label class="layui-form-label">{$v['channel']['title']}</label>
        <div class="layui-input-block">
            <input type="hidden" name="channel[{$v['id']}]" value="{$v['channel']['id']}">
            <input type="number" name="rate[{$v['id']}]" value="{$v['rate']*1000}" class="layui-input"
                   title="请输入{$v['channel']['title']}费率" placeholder="请输入{$v['channel']['title']}费率">
            <span>单位：千分率，充值费率：{$v['channel']['lowrate']*1000}‰</span>
        </div>
    </div>
    {/foreach}

    <div class="hr-line-dashed"></div>

    <div class="layui-form-item text-center">
        <button class="layui-btn" type='submit'>保存</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消吗？" data-close>取消</button>
    </div>

</form>

<script>
    group = {};
    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.render();
    });
</script>
