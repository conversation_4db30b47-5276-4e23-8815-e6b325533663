<?php

namespace app\common\util\dwz;

use app\common\util\DWZ;

class tbh extends DWZ {

    protected $type;
    protected $key;
    protected $pattern;

    public function __construct() {
        $this->type = sysconf('tbh_type');
        $this->token = sysconf('tbh_token');
        $this->pattern = sysconf('tbh_pattern');
    }

    public function create($url) {
        $Gate_way = 'http://www.tbhwp.com/api/url.php?';
        $data = [
            'url' => $url,
            'type' => $this->type, // 短网址类型，默认tcn1，suiji则随机短网址类型
            'token' => $this->token, //此处填写 token
            'pattern' => $this->pattern, ////短网址模式（普通：1，防红：2，直链：3, 缩短：4）
        ];
        $res = file_get_contents($Gate_way . \http_build_query($data));
        $json = json_decode($res, true);
        if (!$json) {
            record_system_log("tbh短链接生成失败：" . $res);
            return false;
        }

        if ($json['code'] != '200') {
            record_system_log("tbh短链接生成失败：" . $res);
            return false;
        }
        return $json['dwz'];
    }

}
