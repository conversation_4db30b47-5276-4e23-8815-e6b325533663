﻿body {
	font-size: 14px;
	color: #8e9eb3;
	font-family: MicrosoftYaHei;
	background: #f9f9f9;
}

ul {
	overflow: hidden;
}

.wrapper {
	width: 1200px;
	margin: 0 auto;
}

.msgGreen .header {
	background: url(../images/banner_01.jpg) no-repeat center;
	background-size: cover;
}

.msgBlue .header {
	background: url(../images/banner_02.jpg) no-repeat center !important;
	background-size: cover;
}

.msgBlue .g-hd-tit {
	color: #428ff8 !important;
	border-top-color: #609afa;
}

.msgBlue .choose-rigt span.on,
.msgBlue .paytype-body .paytype-item.on {
	border-color: #609afa;
}

.msgBlue .paytype-tab li.on {
	background: #609afa;
}

.msgBlue .choose-rigt span.on:before,
.msgBlue .paytype-body .paytype-item.on:before {
	background: url(../images/img_03.png) no-repeat center !important;
}

.msgBlue .choose-rigt input:focus {
	border-color: #609afa;
}

.msgBlue .rl-qq a {
	background: #609afa;
}

.header-top {
	overflow: hidden;
}

.header-top .logo {
	margin-top: 20px;
	float: left;
}

.header-top .nav {
	margin-top: 35px;
	float: right;
	font-size: 16px;
}

.header-top .nav li {
	float: left;
	margin-left: 70px;
}

.header-top .nav li:first-child {
	margin: 0;
}

.header-top .nav a {
	color: #ffff;
}

.header-main {
	margin-top: 89px;
	padding-bottom: 40px;
	font-size: 26px;
	color: #fff;
	overflow: hidden;
}

.header-main .subnav {
	float: left;
}

.header-main .subnav h2 {
	line-height: 45px;
}

.header-main .subnav ul {
	margin-top: 20px;
}

.header-main .subnav li {
	margin-right: 15px;
	float: left;
	font-size: 14px;
}

.header-main .subnav li a {
	display: inline-block;
	width: 90px;
	line-height: 30px;
	text-align: center;
	color: #fff;
	background: rgba(69, 111, 185, .4);
	border-radius: 15px;
	-webkit-border-radius: 15px;
	-moz-border-radius: 15px;
}

.header-main .search {
	position: relative;
	float: right;
	margin-top: 35px;
}

.header-main form {
	font-size: 0;
}

.header-main .search-text {
	border: none;
	background: none;
	padding: 0 47px;
	color: #fff;
	width: 128px;
	height: 38px;
	border: 1px solid #c2e6ff;
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
}

.search-text::-webkit-input-placeholder {
	color: #fff;
}

.header-main .search-btn {
	position: absolute;
	top: 0;
	right: 8px;
	width: 40px;
	height: 40px;
	background: url(../images/img_01.png) no-repeat center;
	border: none;
}

.main {
	margin-top: 16px;
}

.main .wrapper {
	overflow: hidden;
}

.main-left {
	float: left;
	width: 27.66%;
}

.main-left .rl {
	padding: 0 40px 0 30px;
	background: #fff;
}

.g-hd-tit {
	display: inline-block;
	padding-top: 15px;
	font-size: 16px;
	color: #00b8b3;
	border-top: 1px solid #00b8b3;
}

.rl-main {
	margin-top: 41px;
	padding-bottom: 50px;
	color: #8e9eb3;
}

.rl-credit {}

.rl-credit span {
	vertical-align: middle;
}

.rl-credit i {
	display: inline-block;
	width: 20px;
	height: 17px;
	background: url(../images/img_02.png) no-repeat center;
	background-size: cover;
	vertical-align: middle;
}

.rl-qq {
	margin-top: 28px;
}

.rl-qq span {
	vertical-align: middle;
}

.rl-qq a {
	margin-left: 15px;
	display: inline-block;
	width: 90px;
	line-height: 30px;
	text-align: center;
	background: #00b8b3;
	border-radius\n: 15px;
	-webkit-border-radius: 15px;
	-moz-border-radius: 15px;
	color: #fff;
}



.notice {
	margin-top: 15px;
	padding: 0 40px 0 30px;
	height: 314px;
	background: #fff;
}

.notice-main {
	margin-top: 22px;
	line-height: 35px;
}

.qrpay {
	margin-top: 12px;
	padding: 0 40px 0 30px;
	height: 325px;
	background: #fff;
}

.qrpay-main {
	margin-top: 50px;
	text-align: center;
}

.qrpay-main img {
	padding: 6px 5px 7px 8px;
	border: 1px solid #e5e5e5;
}

.qrpay-main h3 {
	margin-top: 25px;
}



.main-right {
	float: right;
	width: 71.333%;
}

.ginfo {
	padding: 0 0 32px 50px;
	background: #fff;
}

.ginfo-main {
	margin-top: 5px;
	line-height: 35px;
}

.choose {
	margin-top: 13px;
	padding: 0 50px 50px 50px;
	background: #fff;
  	min-height: 100%;
	/*min-height: 416px;*/
  	/*min-height: 398px;*/
}

.choose-main {
	margin-top: 26px;

}

.choose-item {
	margin-bottom: 20px;
	overflow: hidden;
}

.choose-left {
	width: 10%;
	line-height: 36px;
}

.choose-rigt {
	width: 90%;
	overflow: hidden;
}

.choose-rigt span {
	position: relative;
	float: left;
	border: 1px solid #eeeeee;
	text-align: center;
	border-radius: 3px;
	cursor: pointer;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.choose-rigt span.on {
	border-color: #00b8b3;
	background: #fff;
}

.choose-rigt span.on:before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 21px;
	height: 21px;
	background: url(../images/img_11.png) no-repeat center;
	background-size: cover;
}

.choose-rigt-type {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.choose-item-type {
	/*margin-right: 14px;*/
	width: 98px;
	line-height: 36px;
}

.choose-rigt-name {
	margin-bottom: -20px;
}

.choose-item-name {
	margin: 0 25px 20px 0;
	width: 47.82%;
	line-height: 36px;
}

.choose-item-name:nth-child(2n) {
	margin-right: 0;
}

.choose-rigt-moy {
	line-height: 36px;
}

.choose-rigt-moy span {
	border: none;
}

.choose-rigt font {
	float: left;
}

.choose-rigt .money {
	margin-left: 20px;
	color: #fd8a8d;
}

.choose-rigt .tit {
	margin-left: 20%;

}

.choose-rigt .num {
	margin-left: 24px;
	padding-left: 22px;
	border: 1px solid #e5e5e5;
	height: 38px;
	width: 303px;
}

.choose-rigt input {
	-webkit-transition: all .5s;
	transition: all .5s;
	-moz-transition: all .5s;
	-ms-transition: all .5s;
}

.choose-rigt input:focus {
	border-color: #00b8b3;
}

.choose-rigt-lianxi {
	line-height: 1;
}

.choose-rigt-lianxi input,
.choose-rigt-lianxi select {
	padding-left: 25px;
	width: 305px;
	height: 38px;
	border-radius: 3px;
	border: 1px solid #e5e5e5;
	vertical-align: top;
}

.choose-rigt-lianxi input::-webkit-input-placeholder {
	color: #d4d7dc;
}

.choose-rigt-lianxi .iphone-x {
	display: none;
}

.choose-rigt-lianxi span {
	float: none;
	margin-left: 20px;
	display: inline-block;
	width: 98px;
	line-height: 36px;
	text-align: center;
	color: #8e9eb3;
	border: 1px solid #eeeeee;
}


.paytype {
	margin-top: 12px;
	height: 323px;
	background: #fff;
}

.payment {
	padding: 0 36px 0 50px;
	overflow: hidden;
}

.payment .g-hd {
	float: left;
}

.pay-moeny {
	float: right;
	line-height: 50px;
}

.pay-moeny span {
	font-size: 16px;
}

.pay-moeny font {
	font-size: 20px;
	color: #ff7181;
}


.paytype-tab {

	line-height: 47px;
	background: #f7f7f7;
}

.paytype-tab li {
	float: left;
	width: 198px;
	color: #9aa2ac;
	text-align: center;
	font-size: 16px;
	cursor: pointer;
}

.paytype-tab li span {
	overflow: hidden;
	vertical-align: middle;
}

.paytype-tab li.on {
	background: #00b8b3;
	color: #fff;
}

.paytype-tab i {
	margin-right: 15px;
	display: inline-block;
	width: 35px;
	height: 25px;
	vertical-align: middle;
}

.paytype-tab li .ico-qr {
	background: url(../images/img_09.png) no-repeat -35px -7px;
}

.paytype-tab li.on .ico-qr {
	background: url(../images/img_09.png) no-repeat 5px -7px;
}

.paytype-tab li .ico-bk {
	background: url(../images/img_10.png) no-repeat -12px -10px;
}

.paytype-tab li.on .ico-bk {
	background: url(../images/img_10.png) no-repeat -67px -10px;
}

.paytype-body {
	margin-top: 30px;
	margin-right: -45px;
	padding: 0 40px 37px;
	border-bottom: 1px solid #f7f7f7;
	overflow: hidden;
}

.paytype-body .paytype-item {
	position: relative;
  	margin-right: 42px;
	/*margin-right: 45px;*/
	float: left;
	width: 158px;
	height: 68px;
	border: 1px solid transparent;
	font-size: 0;
	text-align: center;
	cursor: pointer;
}

.paytype-body .paytype-item img {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.paytype-body .paytype-item.on {
	border: 1px solid #00b8b3;
}

.paytype-body .paytype-item.on:before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	z-index: 11;
	width: 21px;
	height: 21px;
	background: url(../images/img_11.png) no-repeat center;
	background-size: cover;
}

.paytype-foot {
	margin-top: 17px;
	text-align: center;
}

.paytype-foot input {
	width: 210px;
	line-height: 50px;
	background: #ff7181;
	color: #fff;
	font-size: 18px;
	border: none;
	border-radius: 25px;
	-webkit-box-shadow: 0px 2px 5px 0px rgba(255, 139, 136, 0.36);
	box-shadow: 0px 2px 5px 0px rgba(255, 139, 136, 0.36);
	cursor: pointer;
}

.footer {
	margin-top: 35px;
	line-height: 68px;
	color: #b5b5b5;
	text-align: center;
	background: #333643;
}